package com.cms.entity;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIdentityReference;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "component_listing")
@Getter
@Setter
@ToString(exclude = {"fields", "childComponents"})
@EqualsAndHashCode(callSuper = true, exclude = {"fields", "childComponents"})
@NoArgsConstructor
@AllArgsConstructor
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id", scope = ComponentListing.class)
public class ComponentListing extends Auditable {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "cms_component_listing_seq")
    @SequenceGenerator(name = "cms_component_listing_seq", sequenceName = "cms_component_listing_seq", initialValue = 100, allocationSize = 1)
    private Integer id;



    @NotBlank(message = "Component name is required")
    @Column(name = "component_name", nullable = false, unique=true)
    private String componentName;

    @Column(name = "component_display_name")
    private String componentDisplayName;

    @Column(name = "component_api_id")
    private String componentApiId;

    @Column(name = "is_active")
    private Boolean isActive = true;



    @OneToMany(mappedBy = "component", cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JsonManagedReference(value = "component-fields")
    private List<ComponentField> fields = new ArrayList<>();

    @Column(name = "get_url")
    private String getUrl;

    @Column(name = "post_url")
    private String postUrl;

    @Column(name = "update_url")
    private String updateUrl;

    @OneToMany(mappedBy = "parentComponent", cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JsonIdentityReference(alwaysAsId = true)
    private List<ComponentComponent> childComponents = new ArrayList<>();

}

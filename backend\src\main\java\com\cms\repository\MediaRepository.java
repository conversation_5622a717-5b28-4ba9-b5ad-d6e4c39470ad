package com.cms.repository;

import com.cms.entity.Media;
import com.cms.entity.MediaFolder;
import com.cms.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface MediaRepository extends JpaRepository<Media, Integer> {
    
    List<Media> findByFolder(MediaFolder folder);
    
    List<Media> findByUploadedBy(User user);
    
    Page<Media> findByFileNameContainingIgnoreCase(String fileName, Pageable pageable);
    
    Optional<Media> findByShareToken(String shareToken);
    
    @Query("SELECT m FROM Media m WHERE m.isPublic = true")
    List<Media> findAllPublicMedia();
    
    @Query("SELECT m FROM Media m WHERE m.fileType LIKE :fileType%")
    List<Media> findByFileTypeStartingWith(@Param("fileType") String fileType);
    
    @Query("SELECT COUNT(m) FROM Media m WHERE m.folder = :folder")
    Long countByFolder(@Param("folder") MediaFolder folder);
}

package com.cms.controller;

import com.cms.config.TenantContextHolder;
import com.cms.entity.Tenant;
import com.cms.repository.TenantRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Controller for debugging tenant-related issues
 */
@RestController
@RequestMapping("/test/tenant")
@RequiredArgsConstructor
@Tag(name = "Tenant Debug", description = "Endpoints for debugging tenant-related issues")
@Slf4j
public class TenantDebugController {

    private final TenantRepository tenantRepository;

    @GetMapping("/current")
    @Operation(summary = "Get current tenant context", description = "Returns the current tenant context")
    public ResponseEntity<Map<String, Object>> getCurrentTenant() {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Current tenant context: {}", currentTenant);

        Map<String, Object> response = new HashMap<>();
        response.put("currentTenant", currentTenant);
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/list")
    @Operation(summary = "List all tenants", description = "Returns a list of all tenants")
    public ResponseEntity<List<Tenant>> listTenants() {
        List<Tenant> tenants = tenantRepository.findAll();
        log.info("Found {} tenants", tenants.size());
        
        return ResponseEntity.ok(tenants);
    }
}

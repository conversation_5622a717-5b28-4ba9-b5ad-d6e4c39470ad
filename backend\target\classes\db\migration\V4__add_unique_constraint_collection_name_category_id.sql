-- Migration to add unique constraint on (collection_name, category_id) combination
-- This ensures collection names are unique within each category but can be duplicated across different categories

-- Function to add unique constraint to collection_listing table in all tenant schemas
CREATE OR REPLACE FUNCTION add_collection_name_category_unique_constraint() RETURNS void AS $$
DECLARE
    schema_name text;
    constraint_exists boolean;
    old_constraint_exists boolean;
BEGIN
    -- Loop through all schemas except system schemas
    FOR schema_name IN 
        SELECT nspname FROM pg_namespace 
        WHERE nspname NOT LIKE 'pg_%' AND nspname != 'information_schema'
    LOOP
        -- Check if the collection_listing table exists in this schema
        IF EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_schema = schema_name AND table_name = 'collection_listing'
        ) THEN
            RAISE NOTICE 'Processing schema: %', schema_name;
            
            -- Check if the old unique constraint on collection_name exists
            SELECT EXISTS (
                SELECT 1 FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
                WHERE tc.table_schema = schema_name 
                AND tc.table_name = 'collection_listing'
                AND tc.constraint_type = 'UNIQUE'
                AND kcu.column_name = 'collection_name'
                AND NOT EXISTS (
                    SELECT 1 FROM information_schema.key_column_usage kcu2
                    WHERE kcu2.constraint_name = tc.constraint_name
                    AND kcu2.column_name = 'category_id'
                )
            ) INTO old_constraint_exists;
            
            -- Drop the old unique constraint on collection_name if it exists
            IF old_constraint_exists THEN
                BEGIN
                    -- Find and drop the old constraint
                    FOR constraint_name IN 
                        SELECT tc.constraint_name
                        FROM information_schema.table_constraints tc
                        JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
                        WHERE tc.table_schema = schema_name 
                        AND tc.table_name = 'collection_listing'
                        AND tc.constraint_type = 'UNIQUE'
                        AND kcu.column_name = 'collection_name'
                        AND NOT EXISTS (
                            SELECT 1 FROM information_schema.key_column_usage kcu2
                            WHERE kcu2.constraint_name = tc.constraint_name
                            AND kcu2.column_name = 'category_id'
                        )
                    LOOP
                        EXECUTE format('ALTER TABLE %I.collection_listing DROP CONSTRAINT %I', schema_name, constraint_name);
                        RAISE NOTICE 'Dropped old unique constraint % on collection_name in schema %', constraint_name, schema_name;
                    END LOOP;
                EXCEPTION WHEN OTHERS THEN
                    RAISE NOTICE 'Error dropping old unique constraint in schema %: %', schema_name, SQLERRM;
                END;
            END IF;
            
            -- Check if the new unique constraint already exists
            SELECT EXISTS (
                SELECT 1 FROM information_schema.table_constraints tc
                WHERE tc.table_schema = schema_name 
                AND tc.table_name = 'collection_listing'
                AND tc.constraint_type = 'UNIQUE'
                AND tc.constraint_name = 'uk_collection_name_category_id'
            ) INTO constraint_exists;
            
            -- Add the new unique constraint if it doesn't exist
            IF NOT constraint_exists THEN
                BEGIN
                    EXECUTE format('ALTER TABLE %I.collection_listing ADD CONSTRAINT uk_collection_name_category_id UNIQUE (collection_name, category_id)', schema_name);
                    RAISE NOTICE 'Added unique constraint uk_collection_name_category_id to collection_listing table in schema %', schema_name;
                EXCEPTION WHEN OTHERS THEN
                    RAISE NOTICE 'Error adding unique constraint in schema %: %', schema_name, SQLERRM;
                END;
            ELSE
                RAISE NOTICE 'Unique constraint uk_collection_name_category_id already exists in collection_listing table in schema %', schema_name;
            END IF;
            
        ELSE
            RAISE NOTICE 'collection_listing table does not exist in schema %', schema_name;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Execute the function
SELECT add_collection_name_category_unique_constraint();

-- Drop the function after execution
DROP FUNCTION add_collection_name_category_unique_constraint();

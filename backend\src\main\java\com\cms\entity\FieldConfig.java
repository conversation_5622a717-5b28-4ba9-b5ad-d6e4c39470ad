package com.cms.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "field_configs")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class FieldConfig extends Auditable {

    @Id
     @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "cms_field_config_seq")
    @SequenceGenerator(name = "cms_field_config_seq", sequenceName = "cms_field_config_seq", initialValue = 500, allocationSize = 1)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "field_type_id")
    private FieldType fieldType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "config_type_id")
    private ConfigType configType;

    @Column(name = "config_name")
    private String configName;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @Column(name = "value_type", length = 50)
    private String valueType;
}

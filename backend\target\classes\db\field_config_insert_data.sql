
-- Insert field configs data
INSERT INTO field_configs (id, config_name, is_active, value_type, config_type_id, field_type_id, created_at, created_by, modified_at, modified_by)
VALUES
(1, 'dependOn', TRUE, 'text', 1, 1, NULL, NULL, NULL, NULL),
(2, 'unique-id', TRUE, 'text', 1, 1, NULL, NULL, NULL, NULL),
(3, 'display-name', TRUE, 'text', 1, 1, NULL, NULL, NULL, NULL),
(4, 'name', TRUE, 'text', 1, 1, NULL, NULL, NULL, NULL),
(5, 'description', TRUE, 'text', 1, 1, NULL, NULL, NULL, NULL),
(6, 'is-visible', TRUE, 'boolean', 1, 1, NULL, NULL, NULL, NULL),
(7, 'keyfilter', TRUE, 'regex', 2, 1, NULL, NULL, NULL, NULL),
(8, 'placeholder', TRUE, 'text', 2, 1, NULL, NULL, NULL, NULL),
(9, 'helpText', TRUE, 'text', 2, 1, NULL, NULL, NULL, NULL),
(10, 'FloatLable', TRUE, 'text', 2, 1, NULL, NULL, NULL, NULL),
(11, 'variant', TRUE, 'options', 2, 1, NULL, NULL, NULL, NULL),
(12, 'invalid', TRUE, 'boolean', 2, 1, NULL, NULL, NULL, NULL),
(13, 'disabled', TRUE, 'boolean', 2, 1, NULL, NULL, NULL, NULL),
(14, 'icon', TRUE, 'icon', 2, 1, NULL, NULL, NULL, NULL),
(15, 'tooltip', TRUE, 'text', 2, 1, NULL, NULL, NULL, NULL),
(16, 'tooltipOptions', TRUE, 'text', 2, 1, NULL, NULL, NULL, NULL),
(17, 'autoClear', TRUE, 'boolean', 2, 1, NULL, NULL, NULL, NULL),
(18, 'required', TRUE, 'boolean', 3, 1, NULL, NULL, NULL, NULL),
(19, 'minLength', TRUE, 'number', 2, 1, NULL, NULL, NULL, NULL),
(20, 'maxLength', TRUE, 'number', 2, 1, NULL, NULL, NULL, NULL),
(21, 'regex', TRUE, 'regex', 3, 1, NULL, NULL, NULL, NULL),
(22, 'api-url', TRUE, 'url', 1, 1, NULL, NULL, NULL, NULL),
(23, 'dependOn', TRUE, 'text', 1, 2, NULL, NULL, NULL, NULL),
(24, 'unique-id', TRUE, 'text', 1, 2, NULL, NULL, NULL, NULL),
(25, 'display-name', TRUE, 'text', 1, 2, NULL, NULL, NULL, NULL),
(26, 'name', TRUE, 'text', 1, 2, NULL, NULL, NULL, NULL),
(27, 'description', TRUE, 'text', 1, 2, NULL, NULL, NULL, NULL),
(28, 'is-visible', TRUE, 'boolean', 1, 2, NULL, NULL, NULL, NULL),
(29, 'placeholder', TRUE, 'text', 2, 2, NULL, NULL, NULL, NULL),
(30, 'helpText', TRUE, 'text', 2, 2, NULL, NULL, NULL, NULL),
(31, 'FloatLable', TRUE, 'text', 2, 2, NULL, NULL, NULL, NULL),
(32, 'variant', TRUE, 'options', 2, 2, NULL, NULL, NULL, NULL),
(33, 'invalid', TRUE, 'boolean', 2, 2, NULL, NULL, NULL, NULL),
(34, 'disabled', TRUE, 'boolean', 2, 2, NULL, NULL, NULL, NULL),
(35, 'useGrouping', TRUE, 'boolean', 2, 2, NULL, NULL, NULL, NULL),
(36, 'locale', TRUE, 'text', 2, 2, NULL, NULL, NULL, NULL),
(37, 'suffix', TRUE, 'text', 2, 2, NULL, NULL, NULL, NULL),
(38, 'prefix', TRUE, 'text', 2, 2, NULL, NULL, NULL, NULL),
(39, 'showButtons', TRUE, 'boolean', 2, 2, NULL, NULL, NULL, NULL),
(40, 'mode', TRUE, 'options', 2, 2, NULL, NULL, NULL, NULL),
(41, 'currency', TRUE, 'options', 2, 2, NULL, NULL, NULL, NULL),
(42, 'step', TRUE, 'number', 2, 2, NULL, NULL, NULL, NULL),
(43, 'min', TRUE, 'number', 2, 2, NULL, NULL, NULL, NULL),
(44, 'max', TRUE, 'number', 2, 2, NULL, NULL, NULL, NULL),
(45, 'minFractionDigits', TRUE, 'number', 2, 2, NULL, NULL, NULL, NULL),
(46, 'maxFractionDigits', TRUE, 'number', 2, 2, NULL, NULL, NULL, NULL),
(47, 'incrementButtonIcon', TRUE, 'icon', 2, 2, NULL, NULL, NULL, NULL),
(48, 'decrementButtonIcon', TRUE, 'icon', 2, 2, NULL, NULL, NULL, NULL),
(49, 'decrementButtonClassName', TRUE, 'text', 2, 2, NULL, NULL, NULL, NULL),
(50, 'incrementButtonClassName', TRUE, 'text', 2, 2, NULL, NULL, NULL, NULL),
(51, 'buttonLayout', TRUE, 'text', 2, 2, NULL, NULL, NULL, NULL),
(52, 'autoClear', TRUE, 'boolean', 2, 2, NULL, NULL, NULL, NULL),
(53, 'icon', TRUE, 'icon', 2, 2, NULL, NULL, NULL, NULL),
(54, 'required', TRUE, 'boolean', 3, 2, NULL, NULL, NULL, NULL),
(55, 'minValue', TRUE, 'number', 3, 2, NULL, NULL, NULL, NULL),
(56, 'maxValue', TRUE, 'number', 3, 2, NULL, NULL, NULL, NULL),
(57, 'minFractionDigits', TRUE, 'number', 3, 2, NULL, NULL, NULL, NULL),
(58, 'maxFractionDigits', TRUE, 'number', 3, 2, NULL, NULL, NULL, NULL),
(59, 'regex', TRUE, 'regex', 3, 2, NULL, NULL, NULL, NULL),
(60, 'dependOn', TRUE, 'text', 1, 6, NULL, NULL, NULL, NULL),
(61, 'required', TRUE, 'boolean', 3, 6, NULL, NULL, NULL, NULL),
(62, 'placeholder', TRUE, 'text', 2, 6, NULL, NULL, NULL, NULL),
(63, 'mask', TRUE, 'regex', 2, 6, NULL, NULL, NULL, NULL),
(64, 'helpText', TRUE, 'text', 2, 6, NULL, NULL, NULL, NULL),
(65, 'regex', TRUE, 'regex', 3, 6, NULL, NULL, NULL, NULL),
(66, 'FloatLable', TRUE, 'text', 2, 6, NULL, NULL, NULL, NULL),
(67, 'unmask', TRUE, 'boolean', 3, 6, NULL, NULL, NULL, NULL),
(68, 'variant', TRUE, 'options', 2, 6, NULL, NULL, NULL, NULL),
(69, 'invalid', TRUE, 'boolean', 2, 6, NULL, NULL, NULL, NULL),
(70, 'disabled', TRUE, 'boolean', 2, 6, NULL, NULL, NULL, NULL),
(71, 'slotChar', TRUE, 'regex', 3, 6, NULL, NULL, NULL, NULL),
(72, 'autoClear', TRUE, 'boolean', 2, 6, NULL, NULL, NULL, NULL),
(73, 'icon', TRUE, 'icon', 2, 6, NULL, NULL, NULL, NULL),
(74, 'dependOn', TRUE, 'text', 1, 3, NULL, NULL, NULL, NULL),
(75, 'dateFormat', TRUE, 'date', 2, 3, NULL, NULL, NULL, NULL),
(76, 'locale', TRUE, 'text', 2, 3, NULL, NULL, NULL, NULL),
(77, 'showIcon', TRUE, 'boolean', 2, 3, NULL, NULL, NULL, NULL),
(78, 'minDate', TRUE, 'date', 2, 3, NULL, NULL, NULL, NULL),
(79, 'maxDate', TRUE, 'date', 2, 3, NULL, NULL, NULL, NULL),
(80, 'readOnlyInput', TRUE, 'boolean', 2, 3, NULL, NULL, NULL, NULL),
(81, 'selectionMode', TRUE, 'options', 2, 3, NULL, NULL, NULL, NULL),
(82, 'hideOnRangeSelection', TRUE, 'boolean', 2, 3, NULL, NULL, NULL, NULL),
(83, 'showButtonBar', TRUE, 'boolean', 2, 3, NULL, NULL, NULL, NULL),
(84, 'showTime', TRUE, 'date', 2, 3, NULL, NULL, NULL, NULL),
(85, 'hourFormat', TRUE, 'options', 2, 3, NULL, NULL, NULL, NULL),
(86, 'view', TRUE, 'options', 2, 3, NULL, NULL, NULL, NULL),
(87, 'numberOfMonths', TRUE, 'number', 2, 3, NULL, NULL, NULL, NULL),
(88, 'FloatLable', TRUE, 'text', 2, 3, NULL, NULL, NULL, NULL),
(89, 'variant', TRUE, 'options', 2, 3, NULL, NULL, NULL, NULL),
(90, 'invalid', TRUE, 'boolean', 2, 3, NULL, NULL, NULL, NULL),
(91, 'disabled', TRUE, 'boolean', 2, 3, NULL, NULL, NULL, NULL),
(92, 'timeOnly', TRUE, 'date', 2, 3, NULL, NULL, NULL, NULL),
(93, 'icon', TRUE, 'icon', 2, 3, NULL, NULL, NULL, NULL),
(94, 'inline', TRUE, 'boolean', 2, 3, NULL, NULL, NULL, NULL),
(95, 'showWeek', TRUE, 'boolean', 2, 3, NULL, NULL, NULL, NULL),
(96, 'readonly', TRUE, 'boolean', 2, 3, NULL, NULL, NULL, NULL),
(97, 'dependon', TRUE, 'text', 1, 9, NULL, NULL, NULL, NULL),
(98, 'feedback_(true/false)', TRUE, 'boolean', 2, 9, NULL, NULL, NULL, NULL),
(99, 'required', TRUE, 'boolean', 3, 9, NULL, NULL, NULL, NULL),
(100, 'unique-id', TRUE, 'text', 1, 9, NULL, NULL, NULL, NULL),
(101, 'promptlabel', TRUE, 'text', 2, 9, NULL, NULL, NULL, NULL),
(102, 'minlength', TRUE, 'number', 2, 9, NULL, NULL, NULL, NULL),
(103, 'display-name', TRUE, 'text', 2, 9, NULL, NULL, NULL, NULL),
(104, 'weaklabel', TRUE, 'text', 2, 9, NULL, NULL, NULL, NULL),
(105, 'maxlength', TRUE, 'number', 2, 9, NULL, NULL, NULL, NULL),
(106, 'name', TRUE, 'text', 2, 9, NULL, NULL, NULL, NULL),
(107, 'mediumlabel', TRUE, 'text', 2, 9, NULL, NULL, NULL, NULL),
(108, 'regex', TRUE, 'regex', 3, 9, NULL, NULL, NULL, NULL),
(109, 'description', TRUE, 'text', 1, 9, NULL, NULL, NULL, NULL),
(110, 'stronglabel', TRUE, 'text', 2, 9, NULL, NULL, NULL, NULL),
(111, 'is-visible', TRUE, 'boolean', 1, 9, NULL, NULL, NULL, NULL),
(112, 'toggle(true/false)', TRUE, 'boolean', 2, 9, NULL, NULL, NULL, NULL),
(113, 'floatlable_(true/false)', TRUE, 'boolean', 2, 9, NULL, NULL, NULL, NULL),
(114, 'variant_(filled)', TRUE, 'string', 2, 9, NULL, NULL, NULL, NULL),
(115, 'invalid_(true/false)', TRUE, 'boolean', 2, 9, NULL, NULL, NULL, NULL),
(116, 'disabled__(true/false)', TRUE, 'boolean', 2, 9, NULL, NULL, NULL, NULL),
(117, 'icon', TRUE, 'icon', 2, 9, NULL, NULL, NULL, NULL),
(118, 'dependon', TRUE, 'text', 1, 10, NULL, NULL, NULL, NULL),
(119, 'dropdown', TRUE, 'options', 2, 10, NULL, NULL, NULL, NULL),
(120, 'unique-id', TRUE, 'text', 1, 10, NULL, NULL, NULL, NULL),
(121, 'display-name', TRUE, 'text', 2, 10, NULL, NULL, NULL, NULL),
(122, 'object', TRUE, 'object', 2, 10, NULL, NULL, NULL, NULL),
(123, 'name', TRUE, 'text', 2, 10, NULL, NULL, NULL, NULL),
(124, 'group', TRUE, 'text', 2, 10, NULL, NULL, NULL, NULL),
(125, 'description', TRUE, 'text', 1, 10, NULL, NULL, NULL, NULL),
(126, 'force_selection', TRUE, 'boolean', 2, 10, NULL, NULL, NULL, NULL),
(127, 'is-visible', TRUE, 'boolean', 1, 10, NULL, NULL, NULL, NULL),
(128, 'multiple', TRUE, 'boolean', 2, 10, NULL, NULL, NULL, NULL),
(129, 'floatlable_(true/false)', TRUE, 'boolean', 2, 10, NULL, NULL, NULL, NULL),
(130, 'variant_(filled)', TRUE, 'string', 2, 10, NULL, NULL, NULL, NULL),
(131, 'invalid_(true/false)', TRUE, 'boolean', 1, 10, NULL, NULL, NULL, NULL),
(132, 'disabled__(true/false)', TRUE, 'boolean', 2, 10, NULL, NULL, NULL, NULL),
(133, 'dependon', TRUE, 'text', 1, 11, NULL, NULL, NULL, NULL),
(134, 'unique-id', TRUE, 'text', 1, 11, NULL, NULL, NULL, NULL),
(135, 'placeholder', TRUE, 'text', 1, 11, NULL, NULL, NULL, NULL),
(136, 'display-name', TRUE, 'text', 1, 11, NULL, NULL, NULL, NULL),
(137, 'float_label', TRUE, 'boolean', 2, 11, NULL, NULL, NULL, NULL),
(138, 'name', TRUE, 'text', 2, 11, NULL, NULL, NULL, NULL),
(139, 'variant_(filled)', TRUE, 'string', 2, 11, NULL, NULL, NULL, NULL),
(140, 'description', TRUE, 'text', 1, 11, NULL, NULL, NULL, NULL),
(141, 'invalid', TRUE, 'boolean', 2, 11, NULL, NULL, NULL, NULL),
(142, 'is-visible', TRUE, 'boolean', 1, 11, NULL, NULL, NULL, NULL),
(143, 'disabled', TRUE, 'boolean', 2, 11, NULL, NULL, NULL, NULL),
(144, 'dependOn', TRUE, 'text', 1, 12, NULL, NULL, NULL, NULL),
(145, 'unique-id', TRUE, 'text', 1, 12, NULL, NULL, NULL, NULL),
(146, 'display-name', TRUE, 'text', 1, 12, NULL, NULL, NULL, NULL),
(147, 'name', TRUE, 'text', 1, 12, NULL, NULL, NULL, NULL),
(148, 'description', TRUE, 'text', 1, 12, NULL, NULL, NULL, NULL),
(149, 'is-visible', TRUE, 'boolean', 1, 12, NULL, NULL, NULL, NULL),
(150, 'get-api-url', TRUE, 'url', 1, 12, NULL, NULL, NULL, NULL),
(151, 'placeholder', TRUE, 'text', 1, 12, NULL, NULL, NULL, NULL),
(152, 'checkmark', TRUE, 'boolean', 2, 12, NULL, NULL, NULL, NULL),
(153, 'highlightOnSelect', TRUE, 'boolean', 2, 12, NULL, NULL, NULL, NULL),
(154, 'editable', TRUE, 'boolean', 2, 12, NULL, NULL, NULL, NULL),
(155, 'optionGroupLabel', TRUE, 'text', 2, 12, NULL, NULL, NULL, NULL),
(156, 'optionGroupChildren', TRUE, 'options', 2, 12, NULL, NULL, NULL, NULL),
(157, 'optionGroupTemplate', TRUE, 'template', 2, 12, NULL, NULL, NULL, NULL),
(158, 'valueTemplate', TRUE, 'template', 2, 12, NULL, NULL, NULL, NULL),
(159, 'itemTemplate', TRUE, 'template', 2, 12, NULL, NULL, NULL, NULL),
(160, 'panelFooterTemplate', TRUE, 'template', 2, 12, NULL, NULL, NULL, NULL),
(161, 'filter', TRUE, 'boolean', 2, 12, NULL, NULL, NULL, NULL),
(162, 'showClear', TRUE, 'boolean', 2, 12, NULL, NULL, NULL, NULL),
(163, 'loading', TRUE, 'boolean', 2, 12, NULL, NULL, NULL, NULL),
(164, 'virtualScrollerOptions', TRUE, 'boolean', 2, 12, NULL, NULL, NULL, NULL),
(165, 'FloatLable', TRUE, 'text', 2, 12, NULL, NULL, NULL, NULL),
(166, 'variant', TRUE, 'options', 2, 12, NULL, NULL, NULL, NULL),
(167, 'invalid', TRUE, 'boolean', 2, 12, NULL, NULL, NULL, NULL),
(168, 'disabled', TRUE, 'boolean', 2, 12, NULL, NULL, NULL, NULL),
(169, 'dependOn', TRUE, 'text', 1, 13, NULL, NULL, NULL, NULL),
(170, 'unique-id', TRUE, 'text', 1, 13, NULL, NULL, NULL, NULL),
(171, 'display-name', TRUE, 'text', 1, 13, NULL, NULL, NULL, NULL),
(172, 'name', TRUE, 'text', 1, 13, NULL, NULL, NULL, NULL),
(173, 'description', TRUE, 'text', 1, 13, NULL, NULL, NULL, NULL),
(174, 'is-visible', TRUE, 'boolean', 1, 13, NULL, NULL, NULL, NULL),
(175, 'get-api-url', TRUE, 'url', 1, 13, NULL, NULL, NULL, NULL),
(176, 'mode', TRUE, 'options', 2, 13, NULL, NULL, NULL, NULL),
(177, 'url', TRUE, 'url', 2, 13, NULL, NULL, NULL, NULL),
(178, 'accept', TRUE, 'upload', 2, 13, NULL, NULL, NULL, NULL),
(179, 'minFileSize', TRUE, 'number', 2, 13, NULL, NULL, NULL, NULL),
(180, 'maxFileSize', TRUE, 'number', 2, 13, NULL, NULL, NULL, NULL),
(181, 'auto', TRUE, 'boolean', 2, 13, NULL, NULL, NULL, NULL),
(182, 'chooseLabel', TRUE, 'text', 2, 13, NULL, NULL, NULL, NULL),
(183, 'multiple', TRUE, 'boolean', 2, 13, NULL, NULL, NULL, NULL),
(184, 'emptyTemplate', TRUE, 'template', 2, 13, NULL, NULL, NULL, NULL),
(185, 'customUpload', TRUE, 'boolean', 2, 13, NULL, NULL, NULL, NULL),
(186, 'uploadHandler', TRUE, 'upload', 2, 13, NULL, NULL, NULL, NULL),
(187, 'dependOn', TRUE, 'text', 1, 14, NULL, NULL, NULL, NULL),
(188, 'unique-id', TRUE, 'text', 1, 14, NULL, NULL, NULL, NULL),
(189, 'display-name', TRUE, 'text', 1, 14, NULL, NULL, NULL, NULL),
(190, 'name', TRUE, 'text', 1, 14, NULL, NULL, NULL, NULL),
(191, 'description', TRUE, 'text', 1, 14, NULL, NULL, NULL, NULL),
(192, 'is-visible', TRUE, 'boolean', 1, 14, NULL, NULL, NULL, NULL),
(193, 'options', TRUE, 'array', 2, 14, NULL, NULL, NULL, NULL),
(194, 'iconTemplate', TRUE, 'template', 2, 14, NULL, NULL, NULL, NULL),
(195, 'disabled', TRUE, 'boolean', 2, 14, NULL, NULL, NULL, NULL),
(196, 'value', TRUE, 'object', 2, 14, NULL, NULL, NULL, NULL),
(197, 'optionValue', TRUE, 'options', 2, 14, NULL, NULL, NULL, NULL),
(198, 'required', TRUE, 'boolean', 1, 14, NULL, NULL, NULL, NULL),
(199, 'dependOn', TRUE, 'text', 1, 15, NULL, NULL, NULL, NULL),
(200, 'unique-id', TRUE, 'text', 1, 15, NULL, NULL, NULL, NULL),
(201, 'display-name', TRUE, 'text', 1, 15, NULL, NULL, NULL, NULL),
(202, 'name', TRUE, 'text', 1, 15, NULL, NULL, NULL, NULL),
(203, 'description', TRUE, 'text', 1, 15, NULL, NULL, NULL, NULL),
(204, 'is-visible', TRUE, 'boolean', 1, 15, NULL, NULL, NULL, NULL),
(205, 'options', TRUE, 'array', 2, 15, NULL, NULL, NULL, NULL),
(206, 'placeholder', TRUE, 'text', 1, 15, NULL, NULL, NULL, NULL),
(207, 'display', TRUE, 'options', 2, 15, NULL, NULL, NULL, NULL),
(208, 'optionLabel', TRUE, 'text', 2, 15, NULL, NULL, NULL, NULL),
(209, 'maxSelectedLabels', TRUE, 'number', 2, 15, NULL, NULL, NULL, NULL),
(210, 'optionGroupLabel', TRUE, 'text', 2, 15, NULL, NULL, NULL, NULL),
(211, 'optionGroupChildren', TRUE, 'options', 2, 15, NULL, NULL, NULL, NULL),
(212, 'optionGroupTemplate', TRUE, 'template', 2, 15, NULL, NULL, NULL, NULL),
(213, 'panelFooterTemplate', TRUE, 'template', 2, 15, NULL, NULL, NULL, NULL),
(214, 'itemTemplate', TRUE, 'template', 2, 15, NULL, NULL, NULL, NULL),
(215, 'filter', TRUE, 'boolean', 2, 15, NULL, NULL, NULL, NULL),
(216, 'loading', TRUE, 'boolean', 2, 15, NULL, NULL, NULL, NULL),
(217, 'FloatLable', TRUE, 'text', 2, 15, NULL, NULL, NULL, NULL),
(218, 'variant', TRUE, 'options', 2, 15, NULL, NULL, NULL, NULL),
(219, 'invalid', TRUE, 'boolean', 2, 15, NULL, NULL, NULL, NULL),
(220, 'disabled', TRUE, 'boolean', 2, 15, NULL, NULL, NULL, NULL),
(221, 'required', TRUE, 'boolean', 3, 15, NULL, NULL, NULL, NULL),
(222, 'dependOn', TRUE, 'text', 1, 16, NULL, NULL, NULL, NULL),
(223, 'unique-id', TRUE, 'text', 1, 16, NULL, NULL, NULL, NULL),
(224, 'display-name', TRUE, 'text', 1, 16, NULL, NULL, NULL, NULL),
(225, 'name', TRUE, 'text', 1, 16, NULL, NULL, NULL, NULL),
(226, 'description', TRUE, 'text', 1, 16, NULL, NULL, NULL, NULL),
(227, 'is-visible', TRUE, 'boolean', 3, 16, NULL, NULL, NULL, NULL),
(228, 'field', TRUE, 'string', 2, 16, NULL, NULL, NULL, NULL),
(229, 'placeholder', TRUE, 'text', 2, 16, NULL, NULL, NULL, NULL),
(230, 'rows', TRUE, 'number', 2, 16, NULL, NULL, NULL, NULL),
(231, 'cols', TRUE, 'number', 2, 16, NULL, NULL, NULL, NULL),
(232, 'trigger', TRUE, 'options', 2, 16, NULL, NULL, NULL, NULL),
(233, 'autoResize', TRUE, 'code', 2, 16, NULL, NULL, NULL, NULL),
(234, 'FloatLabel', TRUE, 'string', 2, 16, NULL, NULL, NULL, NULL),
(235, 'variant (Filled)', TRUE, 'string', 2, 16, NULL, NULL, NULL, NULL),
(236, 'Disabled', TRUE, 'boolean', 2, 16, NULL, NULL, NULL, NULL),
(237, 'invalid (True/false)', TRUE, 'boolean', 2, 16, NULL, NULL, NULL, NULL),
(238, 'dependOn', TRUE, 'text', 1, 17, NULL, NULL, NULL, NULL),
(239, 'unique-id', TRUE, 'text', 1, 17, NULL, NULL, NULL, NULL),
(240, 'display-name', TRUE, 'text', 1, 17, NULL, NULL, NULL, NULL),
(241, 'name', TRUE, 'text', 1, 17, NULL, NULL, NULL, NULL),
(242, 'description', TRUE, 'text', 1, 17, NULL, NULL, NULL, NULL),
(243, 'is-visible', TRUE, 'boolean', 1, 17, NULL, NULL, NULL, NULL),
(244, 'rows', TRUE, 'number', 2, 17, NULL, NULL, NULL, NULL),
(245, 'cols', TRUE, 'number', 2, 17, NULL, NULL, NULL, NULL),
(246, 'autoResize', TRUE, 'code', 2, 17, NULL, NULL, NULL, NULL),
(247, 'placeholder', TRUE, 'text', 2, 17, NULL, NULL, NULL, NULL),
(248, 'Key Filter', TRUE, 'string', 2, 17, NULL, NULL, NULL, NULL),
(249, 'FloatLabel', TRUE, 'boolean', 2, 17, NULL, NULL, NULL, NULL),
(250, 'variant (Filled)', TRUE, 'string', 2, 17, NULL, NULL, NULL, NULL),
(251, 'Disabled', TRUE, 'boolean', 2, 17, NULL, NULL, NULL, NULL),
(252, 'invalid (True/false)', TRUE, 'boolean', 2, 17, NULL, NULL, NULL, NULL),
(253, 'required', TRUE, 'boolean', 3, 17, NULL, NULL, NULL, NULL),
(254, 'minLength', TRUE, 'number', 2, 17, NULL, NULL, NULL, NULL),
(255, 'maxLength', TRUE, 'number', 2, 17, NULL, NULL, NULL, NULL),
(256, 'dependOn', TRUE, 'text', 1, 18, NULL, NULL, NULL, NULL),
(257, 'unique-id', TRUE, 'text', 1, 18, NULL, NULL, NULL, NULL),
(258, 'display-name', TRUE, 'text', 1, 18, NULL, NULL, NULL, NULL),
(259, 'name', TRUE, 'text', 1, 18, NULL, NULL, NULL, NULL),
(260, 'description', TRUE, 'text', 1, 18, NULL, NULL, NULL, NULL),
(261, 'is-visible', TRUE, 'boolean', 1, 18, NULL, NULL, NULL, NULL),
(262, 'mask', TRUE, 'regex', 3, 18, NULL, NULL, NULL, NULL),
(263, 'required', TRUE, 'boolean', 3, 18, NULL, NULL, NULL, NULL),
(264, 'integerOnly', TRUE, 'boolean', 2, 18, NULL, NULL, NULL, NULL),
(265, 'inputTemplate', TRUE, 'template', 2, 18, NULL, NULL, NULL, NULL),
(266, 'optionLabel', TRUE, 'text', 2, 12, NULL, NULL, NULL, NULL),
(267, 'optionValue', TRUE, 'text', 2, 12, NULL, NULL, NULL, NULL),
(268, 'dependOn', TRUE, 'string', 1, 19, NULL, NULL, NULL, NULL),
(269, 'required', TRUE, 'boolean', 3, 19, NULL, NULL, NULL, NULL),
(270, 'unique-id', TRUE, 'string', 1, 19, NULL, NULL, NULL, NULL),
(271, 'api-id', TRUE, 'string', 2, 19, NULL, NULL, NULL, NULL),
(272, 'display-name', TRUE, 'string', 1, 19, NULL, NULL, NULL, NULL),
(273, 'name', TRUE, 'string', 1, 19, NULL, NULL, NULL, NULL),
(274, 'description', TRUE, 'string', 1, 19, NULL, NULL, NULL, NULL),
(275, 'display-preference', TRUE, 'string', 2, 19, NULL, NULL, NULL, NULL),
(276, 'is-visible', TRUE, 'boolean', 1, 19, NULL, NULL, NULL, NULL),
(277, 'type', TRUE, 'string', 2, 19, NULL, NULL, NULL, NULL),
(278, 'dependOn', TRUE, 'string', 1, 20, NULL, NULL, NULL, NULL),
(279, 'required', TRUE, 'boolean', 3, 20, NULL, NULL, NULL, NULL),
(280, 'unique-id', TRUE, 'string', 1, 20, NULL, NULL, NULL, NULL),
(281, 'api-id', TRUE, 'string', 2, 20, NULL, NULL, NULL, NULL),
(282, 'display-name', TRUE, 'string', 1, 20, NULL, NULL, NULL, NULL),
(283, 'name', TRUE, 'string', 1, 20, NULL, NULL, NULL, NULL),
(284, 'description', TRUE, 'string', 1, 20, NULL, NULL, NULL, NULL),
(285, 'display-preference', TRUE, 'string', 2, 20, NULL, NULL, NULL, NULL),
(286, 'is-visible', TRUE, 'boolean', 1, 20, NULL, NULL, NULL, NULL),
(287, 'type', TRUE, 'string', 2, 20, NULL, NULL, NULL, NULL),
(288, 'dependOn', TRUE, 'string', 1, 21, NULL, NULL, NULL, NULL),
(289, 'required', TRUE, 'boolean', 3, 21, NULL, NULL, NULL, NULL),
(290, 'unique-id', TRUE, 'string', 1, 21, NULL, NULL, NULL, NULL),
(291, 'api-id', TRUE, 'string', 2, 21, NULL, NULL, NULL, NULL),
(292, 'display-name', TRUE, 'string', 1, 21, NULL, NULL, NULL, NULL),
(293, 'name', TRUE, 'string', 1, 21, NULL, NULL, NULL, NULL),
(294, 'description', TRUE, 'string', 1, 21, NULL, NULL, NULL, NULL),
(295, 'display-preference', TRUE, 'string', 2, 21, NULL, NULL, NULL, NULL),
(296, 'is-visible', TRUE, 'boolean', 1, 21, NULL, NULL, NULL, NULL),
(297, 'type', TRUE, 'string', 2, 21, NULL, NULL, NULL, NULL),
(298, 'Group', TRUE, 'string', 2, 19, NULL, NULL, NULL, NULL),
(299, 'Dynamic', TRUE, 'boolean', 2, 19, NULL, NULL, NULL, NULL),
(300, 'Invalid (True/false)', TRUE, 'boolean', 2, 19, NULL, NULL, NULL, NULL),
(301, 'Filled', TRUE, 'string', 2, 19, NULL, NULL, NULL, NULL),
(302, 'Disabled', TRUE, 'boolean', 2, 19, NULL, NULL, NULL, NULL),
(303, 'Accessibility', TRUE, 'string', 2, 19, NULL, NULL, NULL, NULL),
(304, 'Group', TRUE, 'string', 2, 20, NULL, NULL, NULL, NULL),
(305, 'Dynamic', TRUE, 'boolean', 2, 20, NULL, NULL, NULL, NULL),
(306, 'Invalid (True/false)', TRUE, 'boolean', 2, 20, NULL, NULL, NULL, NULL),
(307, 'Filled', TRUE, 'string', 2, 20, NULL, NULL, NULL, NULL),
(308, 'Disabled', TRUE, 'boolean', 2, 20, NULL, NULL, NULL, NULL),
(309, 'Accessibility', TRUE, 'string', 2, 20, NULL, NULL, NULL, NULL),
(310, 'Preselection', TRUE, 'string', 2, 21, NULL, NULL, NULL, NULL),
(311, 'Disabled', TRUE, 'boolean', 2, 21, NULL, NULL, NULL, NULL),
(312, 'Invalid (True/false)', TRUE, 'boolean', 2, 21, NULL, NULL, NULL, NULL),
(313, 'Accessibility', TRUE, 'string', 2, 21, NULL, NULL, NULL, NULL),
(315, 'displayName', TRUE, 'string', 1, 3, NULL, NULL, NULL, NULL),
(316, 'Placeholder', TRUE, 'string', 2, 3, NULL, NULL, NULL, NULL),
(317, 'Name', TRUE, 'string', 1, 3, NULL, NULL, NULL, NULL),
(318, 'displayName', TRUE, 'string', 1, 6, NULL, NULL, NULL, NULL),
(319, 'displayName', TRUE, 'string', 1, 8, NULL, NULL, NULL, NULL),
(320, 'readOnly', TRUE, 'boolean', 1, 8, NULL, NULL, NULL, NULL),
(321, 'label', TRUE, 'string', 2, 19, NULL, NULL, NULL, NULL),
(322, 'label', TRUE, 'string', 2, 20, NULL, NULL, NULL, NULL),
(323, 'is-visible', TRUE, 'boolean', 1, 23, NULL, NULL, NULL, NULL),
(324, 'postapiurls', TRUE, 'string', 1, 23, NULL, NULL, NULL, NULL),
(325, 'updateapiurls', TRUE, 'string', 1, 23, NULL, NULL, NULL, NULL),
(326, 'postapijson', TRUE, 'string', 1, 23, NULL, NULL, NULL, NULL),
(327, 'updateapijson', TRUE, 'string', 1, 23, NULL, NULL, NULL, NULL),
(328, 'is-visible', TRUE, 'boolean', 1, 22, NULL, NULL, NULL, NULL),
(329, 'showButtons', TRUE, 'boolean', 2, 2, NULL, NULL, NULL, NULL),
(330, 'isrelative', TRUE, 'boolean', 2, 3, NULL, NULL, NULL, NULL),
(331, 'previnterval', TRUE, 'integer', 2, 3, NULL, NULL, NULL, NULL),
(332, 'nextinterval', TRUE, 'integer', 2, 3, NULL, NULL, NULL, NULL),
(333, 'previntervalunit', TRUE, 'text', 2, 3, NULL, NULL, NULL, NULL),
(334, 'nextintervalunit', TRUE, 'text', 2, 3, NULL, NULL, NULL, NULL),
(335, 'days', TRUE, 'text', 2, 3, NULL, NULL, NULL, NULL),
(336, 'weeks', TRUE, 'text', 2, 3, NULL, NULL, NULL, NULL),
(337, 'months', TRUE, 'text', 2, 3, NULL, NULL, NULL, NULL),
(338, 'year', TRUE, 'text', 2, 3, NULL, NULL, NULL, NULL),
(339, 'isdefault', TRUE, 'text', 2, 3, NULL, NULL, NULL, NULL),
(340, 'display-name', TRUE, 'text', 1, 3, NULL, NULL, NULL, NULL)
ON CONFLICT (id) DO UPDATE
SET
    config_name = EXCLUDED.config_name,
    is_active = EXCLUDED.is_active,
    value_type = EXCLUDED.value_type,
    config_type_id = EXCLUDED.config_type_id,
    field_type_id = EXCLUDED.field_type_id,
    created_at = EXCLUDED.created_at,
    created_by = EXCLUDED.created_by,
    modified_at = EXCLUDED.modified_at,
    modified_by = EXCLUDED.modified_by;
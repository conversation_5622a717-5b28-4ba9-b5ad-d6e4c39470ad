package com.cms.repository;

import com.cms.entity.ComponentComponent;
import com.cms.entity.ComponentListing;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ComponentComponentRepository extends JpaRepository<ComponentComponent, Integer> {
    List<ComponentComponent> findByParentComponentAndIsActiveTrue(ComponentListing parentComponent);

    List<ComponentComponent> findByParentComponentId(Integer parentComponentId);

    List<ComponentComponent> findByParentComponentIdOrderByDisplayPreference(Integer parentComponentId);

    @Query("SELECT MAX(cc.displayPreference) FROM ComponentComponent cc WHERE cc.parentComponent.id = :parentComponentId")
    Integer findMaxDisplayPreferenceByParentComponentId(@Param("parentComponentId") Integer parentComponentId);

    @Query("SELECT MAX(cc.id) FROM ComponentComponent cc")
    Integer findMaxId();
}

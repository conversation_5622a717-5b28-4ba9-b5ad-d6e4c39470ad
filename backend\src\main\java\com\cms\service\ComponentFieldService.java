package com.cms.service;

import com.cms.entity.ComponentField;

import java.util.List;

public interface ComponentFieldService {
    ComponentField getComponentFieldById(Integer id);
    List<ComponentField> getAllComponentFields();
    List<ComponentField> getComponentFieldsByComponentId(Integer componentId);
    ComponentField createComponentField(ComponentField componentField);
    ComponentField updateComponentField(Integer id, ComponentField componentField);
    void deleteComponentField(Integer id);
    Integer getNextAvailableId();
    List<ComponentField> reorderComponentFields(Integer componentId, List<Integer> fieldIds);
}

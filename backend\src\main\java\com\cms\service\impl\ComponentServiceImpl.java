package com.cms.service.impl;

import com.cms.entity.ComponentField;
import com.cms.entity.ComponentFieldConfig;
import com.cms.entity.ComponentListing;
import com.cms.exception.ResourceNotFoundException;
import com.cms.repository.ComponentFieldConfigRepository;
import com.cms.repository.ComponentFieldRepository;
import com.cms.repository.ComponentRepository;
import com.cms.service.ComponentService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class ComponentServiceImpl implements ComponentService {

    private final ComponentRepository componentRepository;
    private final ComponentFieldRepository componentFieldRepository;
    private final ComponentFieldConfigRepository componentFieldConfigRepository;

    @Override
    public List<ComponentListing> getAllComponents() {
        return componentRepository.findAll();
    }

    @Override
    public List<ComponentListing> getAllActiveComponents() {
        return componentRepository.findByIsActiveTrue();
    }

    @Override
    public Optional<ComponentListing> getComponentById(Integer id) {
        return componentRepository.findById(id);
    }

    @Override
    public Optional<ComponentListing> getComponentDetailsById(Integer id) {
        Optional<ComponentListing> componentOpt = getComponentById(id);
        componentOpt.ifPresent(this::loadComponentDetails);
        return componentOpt;
    }

    @Override
    public Optional<ComponentListing> getComponentByApiId(String apiId) {
        return componentRepository.findByComponentApiId(apiId);
    }

    @Override
    public ComponentListing createComponent(ComponentListing component) {
        return componentRepository.save(component);
    }

    @Override
    public ComponentListing updateComponent(Integer id, ComponentListing component) {
        ComponentListing existingComponent = getComponentById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Component not found with id: " + id));

        existingComponent.setComponentName(component.getComponentName());
        existingComponent.setComponentDisplayName(component.getComponentDisplayName());
        existingComponent.setComponentApiId(component.getComponentApiId());
        existingComponent.setIsActive(component.getIsActive());
        existingComponent.setGetUrl(component.getGetUrl());
        existingComponent.setPostUrl(component.getPostUrl());
        existingComponent.setUpdateUrl(component.getUpdateUrl());

        return componentRepository.save(existingComponent);
    }

    @Override
    public void deleteComponent(Integer id) {
        // Get the component to delete
        ComponentListing component = getComponentById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Component not found with id: " + id));

        // Get all fields associated with this component
        List<ComponentField> fields = componentFieldRepository.findByComponent(component);

        // Delete all field configurations first
        for (ComponentField field : fields) {
            List<ComponentFieldConfig> configs = componentFieldConfigRepository.findByComponentField(field);
            if (configs != null && !configs.isEmpty()) {
                componentFieldConfigRepository.deleteAll(configs);
            }
        }

        // Delete all component fields
        if (!fields.isEmpty()) {
            componentFieldRepository.deleteAll(fields);
        }

        // Finally delete the component itself
        componentRepository.delete(component);
    }

    private void loadComponentDetails(ComponentListing component) {
        // Load component fields
        List<ComponentField> fields = componentFieldRepository.findByComponentOrderByDisplayPreference(component);
        component.setFields(fields);

        // Load field configs for each field
        fields.forEach(field -> {
            List<ComponentFieldConfig> configs = componentFieldConfigRepository.findByComponentField(field);
            field.setConfigs(configs);
        });
    }

    @Override
    public boolean existsByComponentName(String componentName) {
        return componentRepository.existsByComponentName(componentName);
    }
}

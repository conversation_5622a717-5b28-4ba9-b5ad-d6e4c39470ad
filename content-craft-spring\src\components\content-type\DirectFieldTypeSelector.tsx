import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { useNavigate } from 'react-router-dom';
import { Search, Loader2, LogIn, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { FieldTypeEnum, useAuthStore, useCollectionStore } from '@/lib/store';
import { fieldTypesApi, getBaseUrl } from '@/lib/api';

interface DirectFieldTypeSelectorProps {
  open: boolean;
  onClose: () => void;
  onSelect: (type: FieldTypeEnum, fieldTypeId?: number) => void;
}

export default function DirectFieldTypeSelector({ open, onClose, onSelect }: DirectFieldTypeSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  interface FieldType {
    id: number;
    type: FieldTypeEnum;
    title: string;
    description: string;
    iconBg: string;
    iconColor: string;
    iconText: string;
  }

  // Map field type names to FieldTypeEnum
  const mapFieldTypeNameToEnum = (name: string): FieldTypeEnum => {
    console.log(`Mapping field type name: ${name}`);

    // Check if the enum exists directly
    if (Object.values(FieldTypeEnum).includes(name as FieldTypeEnum)) {
      console.log(`Direct match found for ${name}`);
      return name as FieldTypeEnum;
    }

    // Try to find a matching enum by converting to uppercase
    const enumKey = name.toUpperCase().replace(/-/g, '_');
    console.log(`Looking for enum key: ${enumKey}`);

    for (const key in FieldTypeEnum) {
      if (key === enumKey) {
        console.log(`Match found: ${key} -> ${FieldTypeEnum[key as keyof typeof FieldTypeEnum]}`);
        return FieldTypeEnum[key as keyof typeof FieldTypeEnum];
      }
    }

    // Default to TEXT if not found
    console.warn(`Field type ${name} not found in FieldTypeEnum, defaulting to TEXT`);
    return FieldTypeEnum.TEXT;
  };

  const [activeFieldTypes, setActiveFieldTypes] = useState<FieldType[]>([]);
  const { isAuthenticated } = useAuthStore();
  const { selectedCollection } = useCollectionStore();
  const navigate = useNavigate();

  // Fetch active field types from the API
  const fetchActiveFieldTypes = async () => {
    if (!isAuthenticated) {
      setError('Authentication required');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('Making API call to fetch active field types');
      // Add a timestamp to prevent caching
      const timestamp = new Date().getTime();
      console.log(`API call timestamp: ${timestamp}`);

      // Make a direct fetch call to ensure it's not cached
      // Use the getBaseUrl function to ensure consistent API URL handling
      const baseUrl = getBaseUrl();
      console.log(`Using baseUrl from environment: ${baseUrl}`);

      const response = await fetch(`${baseUrl}/field-types/getAllActive?t=${timestamp}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Cache-Control': 'no-cache',
          'Authorization': `Bearer ${localStorage.getItem('cms_token')}`
        },
        credentials: 'omit' // Disable credentials to avoid CORS issues
      });

      const data = await response.json();
      console.log('Fetch API response:', JSON.stringify(data, null, 2));

      if (!data || !Array.isArray(data)) {
        console.error('Invalid response data format:', data);
        setError('Invalid response data format');
        setLoading(false);
        return;
      }

      // Process the field types for display
      const processedFieldTypes = data.map((fieldType: any) => {
        // Map the field type name to FieldTypeEnum
        const fieldTypeEnum = mapFieldTypeNameToEnum(fieldType.fieldTypeName);

        return {
          id: fieldType.id,
          type: fieldTypeEnum,
          title: fieldType.displayName || fieldType.fieldTypeName,
          description: fieldType.fieldTypeDesc || '',
          iconBg: 'bg-primary/10',
          iconColor: 'text-primary',
          iconText: (fieldType.displayName || fieldType.fieldTypeName).substring(0, 1).toUpperCase()
        };
      });

      setActiveFieldTypes(processedFieldTypes);
      console.log('Processed field types:', processedFieldTypes);
    } catch (err) {
      console.error('Failed to fetch active field types:', err);
      setError(err.response?.data?.message || err.message || 'Failed to load field types');
    } finally {
      setLoading(false);
    }
  };

  // Fetch field types when the component mounts or when dialog opens
  useEffect(() => {
    console.log('DirectFieldTypeSelector useEffect triggered, open:', open, 'isAuthenticated:', isAuthenticated);
    if (open && isAuthenticated) {
      console.log('Dialog opened, fetching active field types');
      fetchActiveFieldTypes();
    }
  }, [open, isAuthenticated]);

  // Debug when component renders
  console.log('DirectFieldTypeSelector rendering, open:', open, 'isAuthenticated:', isAuthenticated);

  // Get field description based on field type
  const getFieldDescription = (fieldType: FieldType): string => {
    return fieldType.description || 'Field type';
  };

  // Filter field types based on search
  const filteredFieldTypes = activeFieldTypes.filter((fieldType) =>
    fieldType.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    getFieldDescription(fieldType).toLowerCase().includes(searchQuery.toLowerCase())
  );

  // If dialog is not open, don't render anything
  if (!open) {
    return null;
  }

  // Create the dialog content
  const dialogContent = (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/50" onClick={(e) => {
      // Close when clicking the backdrop
      if (e.target === e.currentTarget) {
        onClose();
      }
    }}>
      <div className="bg-card rounded-lg shadow-lg w-full max-w-[720px] max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            {selectedCollection && (
              <div className="flex items-center justify-center w-8 h-8 bg-primary text-primary-foreground rounded font-bold">
                {selectedCollection.name.substring(0, 2).toUpperCase()}
              </div>
            )}
            <h2 className="text-lg font-semibold">Select a field for your collection type</h2>
          </div>
          <Button variant="ghost" size="icon" onClick={onClose} className="rounded-full">
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="p-6">
          <div className="relative mb-4">
            <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search field types..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          {loading ? (
            <div className="flex items-center justify-center h-32">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading field types...</span>
            </div>
          ) : error ? (
            <div className="space-y-4">
              <Alert variant="destructive" className="mb-4">
                <AlertDescription className="flex flex-col gap-4">
                  <div>Failed to load field types: {error}</div>
                  <div className="flex gap-2">
                    {error.includes('Authentication') ? (
                      <Button
                        variant="outline"
                        onClick={() => navigate('/login')}
                        className="flex items-center gap-2"
                      >
                        <LogIn className="h-4 w-4" />
                        Go to Login
                      </Button>
                    ) : (
                      <Button
                        variant="outline"
                        onClick={fetchActiveFieldTypes}
                        className="flex items-center gap-2"
                      >
                        Try again
                      </Button>
                    )}
                  </div>
                </AlertDescription>
              </Alert>
            </div>
          ) : filteredFieldTypes.length === 0 ? (
            <div className="flex items-center justify-center h-32 border rounded-md">
              <p className="text-gray-500">No field types found matching your search</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredFieldTypes.map((fieldType) => (
                <div
                  key={`${fieldType.type}-${fieldType.id}`}
                  className="flex p-4 border rounded-md cursor-pointer hover:border-primary hover:bg-primary/5 transition-colors"
                  onClick={() => {
                    console.log('Selected field type:', fieldType.type, 'ID:', fieldType.id);
                    // Make sure we're passing the ID as a number
                    const fieldTypeId = typeof fieldType.id === 'number' ? fieldType.id : parseInt(String(fieldType.id), 10);
                    console.log('Parsed fieldTypeId:', fieldTypeId);
                    onSelect(fieldType.type, fieldTypeId);
                    onClose();
                  }}
                >
                  <div className="mr-4 flex-shrink-0">
                    <div className={`w-8 h-8 ${fieldType.iconBg || 'bg-primary/10'} ${fieldType.iconColor || 'text-primary'} flex items-center justify-center rounded`}>
                      {fieldType.iconText || fieldType.title.substring(0, 1)}
                    </div>
                  </div>
                  <div>
                    <h3 className="font-medium">{fieldType.title}</h3>
                    <p className="text-sm text-gray-500">{getFieldDescription(fieldType)}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  // Use createPortal to render the dialog at the document body level
  return createPortal(dialogContent, document.body);
}

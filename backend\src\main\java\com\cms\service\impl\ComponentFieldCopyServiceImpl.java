package com.cms.service.impl;

import com.cms.entity.*;
import com.cms.repository.CollectionFieldConfigRepository;
import com.cms.repository.CollectionFieldRepository;
import com.cms.service.CollectionOrderingService;
import com.cms.service.ComponentFieldCopyService;
import com.cms.service.ComponentFieldService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class ComponentFieldCopyServiceImpl implements ComponentFieldCopyService {

    private final ComponentFieldService componentFieldService;
    private final CollectionFieldRepository collectionFieldRepository;
    private final CollectionFieldConfigRepository collectionFieldConfigRepository;
    private final CollectionOrderingService collectionOrderingService;

    @Override
    @Transactional
    public List<CollectionField> copyComponentFieldsToCollection(CollectionComponent collectionComponent) {
        if (collectionComponent == null || collectionComponent.getComponent() == null || collectionComponent.getCollection() == null) {
            log.warn("Cannot copy fields: collection component, component, or collection is null");
            return new ArrayList<>();
        }

        // Get all fields for the component
        List<ComponentField> componentFields = componentFieldService.getComponentFieldsByComponentId(collectionComponent.getComponent().getId());
        if (componentFields.isEmpty()) {
            log.info("No fields to copy from component ID: {}", collectionComponent.getComponent().getId());
            return new ArrayList<>();
        }

        // Copy each field to the collection
        List<CollectionField> createdFields = new ArrayList<>();
        for (ComponentField componentField : componentFields) {
            CollectionField collectionField = copyComponentFieldToCollectionField(componentField, collectionComponent);
            createdFields.add(collectionField);
        }

        log.info("Copied {} fields from component ID: {} to collection ID: {}", 
                createdFields.size(), collectionComponent.getComponent().getId(), collectionComponent.getCollection().getId());
        return createdFields;
    }

    @Override
    @Transactional
    public CollectionField copyComponentFieldToCollectionField(ComponentField componentField, CollectionComponent collectionComponent) {
        // Create a new collection field
        CollectionField collectionField = new CollectionField();
        
        // Set the collection
        collectionField.setCollection(collectionComponent.getCollection());
        
        // Copy field type
        collectionField.setFieldType(componentField.getFieldType());
        
        // Set display preference
        Integer nextDisplayPreference = collectionOrderingService.getNextDisplayPreference(collectionComponent.getCollection().getId());
        collectionField.setDisplayPreference(nextDisplayPreference);
        
        // Copy additional information
        collectionField.setAdditionalInformation(componentField.getAdditionalInformation());
        
        // Save the collection field first to get an ID
        CollectionField savedField = collectionFieldRepository.save(collectionField);
        
        // Copy configs if they exist
        if (componentField.getConfigs() != null && !componentField.getConfigs().isEmpty()) {
            List<CollectionFieldConfig> configsToSave = new ArrayList<>();
            
            for (ComponentFieldConfig componentConfig : componentField.getConfigs()) {
                // Create a new collection field config
                CollectionFieldConfig collectionConfig = new CollectionFieldConfig();
                collectionConfig.setCollectionField(savedField);
                collectionConfig.setFieldConfig(componentConfig.getFieldConfig());
                collectionConfig.setFieldConfigValue(componentConfig.getFieldConfigValue());
                
                configsToSave.add(collectionConfig);
            }
            
            // Save all configs
            List<CollectionFieldConfig> savedConfigs = collectionFieldConfigRepository.saveAll(configsToSave);
            
            // Update the field with the saved configs
            savedField.setConfigs(savedConfigs);
        }
        
        log.debug("Copied component field ID: {} to collection field ID: {}", componentField.getId(), savedField.getId());
        return savedField;
    }
}

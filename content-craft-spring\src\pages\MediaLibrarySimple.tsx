import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { FileText, Upload } from 'lucide-react';

export default function MediaLibrarySimple() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Set document title
    document.title = 'Media Library | R-CMS';
    
    // Log for debugging
    console.log('MediaLibrarySimple component mounted');
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Media Library (Simple)</h1>
        <Button>
          <Upload className="mr-2 h-4 w-4" />
          Add new assets
        </Button>
      </div>

      {/* Simple content */}
      <div className="flex flex-col items-center justify-center p-12 border border-dashed rounded-lg bg-muted/20">
        <div className="w-20 h-20 mb-4 flex items-center justify-center rounded-full bg-primary/10">
          <FileText className="h-10 w-10 text-primary" />
        </div>
        <h3 className="text-xl font-medium mb-2">Media Library</h3>
        <p className="text-muted-foreground mb-6 text-center max-w-md">
          This is a simplified version of the Media Library to help troubleshoot the blank page issue.
        </p>
      </div>
    </div>
  );
}

package com.cms.controller;

import com.cms.dto.simplified.SimplifiedCollectionDTO;
import com.cms.entity.CollectionListing;
import com.cms.mapper.SimplifiedCollectionMapper;
import com.cms.service.CollectionListingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/public/simplified-collections")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Public Simplified Collection", description = "Public Simplified Collection API")
public class PublicSimplifiedCollectionController {

    private final CollectionListingService collectionListingService;
    private final SimplifiedCollectionMapper simplifiedCollectionMapper;

    @GetMapping("/getAll")
    @Operation(summary = "Get all collections in simplified format (public)", description = "Returns a list of all collections in a simplified format without authentication")
    public ResponseEntity<List<SimplifiedCollectionDTO>> getAllSimplifiedCollections() {
        List<CollectionListing> collections = collectionListingService.getAllCollectionsWithDetails();
        return ResponseEntity.ok(simplifiedCollectionMapper.toDTOList(collections));
    }

    @GetMapping("/getById/{id}")
    @Operation(summary = "Get collection by ID in simplified format (public)", description = "Returns a collection by its ID in a simplified format without authentication")
    public ResponseEntity<SimplifiedCollectionDTO> getSimplifiedCollectionById(@PathVariable Integer id) {
        return collectionListingService.getCollectionByIdWithDetails(id)
                .map(collection -> ResponseEntity.ok(simplifiedCollectionMapper.toDTO(collection)))
                .orElse(ResponseEntity.noContent().build());
    }

    @GetMapping("/getByApiId/{apiId}")
    @Operation(summary = "Get collection by API ID in simplified format (public)", description = "Returns a collection by its API ID in a simplified format without authentication")
    public ResponseEntity<SimplifiedCollectionDTO> getSimplifiedCollectionByApiId(@PathVariable String apiId) {
        return collectionListingService.getCollectionByApiIdWithDetails(apiId)
                .map(collection -> ResponseEntity.ok(simplifiedCollectionMapper.toDTO(collection)))
                .orElse(ResponseEntity.noContent().build());
    }

    @GetMapping("/getByClientId/{clientId}")
    @Operation(
            summary = "Get collections by client ID (mandatory) and optional category ID in simplified format (public)",
            description = "Returns collections for a client. If categoryId is provided, filters collections by that category. Otherwise, returns all collections for the client."
    )
    public ResponseEntity<List<SimplifiedCollectionDTO>> getSimplifiedCollectionsByClientIdAndOptionalCategoryId(
            @PathVariable Integer clientId,
            @RequestParam(required = false) Integer categoryId) {

        log.info("Getting simplified collections for client ID: {} and category ID: {}", clientId, categoryId);

        try {
            List<CollectionListing> collections;

            if (categoryId != null) {
                collections = collectionListingService.getCollectionsByClientIdAndCategoryIdWithDetails(clientId, categoryId);
            } else {
                collections = collectionListingService.getCollectionsByClientIdWithDetails(clientId);
            }

            if (collections.isEmpty()) {
                log.info("No collections found for client ID: {} with category ID: {}", clientId, categoryId);
                return ResponseEntity.noContent().build();
            }

            List<SimplifiedCollectionDTO> simplifiedCollections = simplifiedCollectionMapper.toDTOList(collections);
            log.info("Found {} collections for client ID: {} with category ID: {}", simplifiedCollections.size(), clientId, categoryId);

            return ResponseEntity.ok(simplifiedCollections);

        } catch (Exception e) {
            log.error("Error getting collections for client ID: {} and category ID: {}", clientId, categoryId, e);
            throw e;
        }
    }

    @GetMapping("/getByClientCategoryName/{clientName}/{categoryName}")
    @Operation(
        summary = "Get collections by client name and category name in simplified format (public)",
        description = "Returns all collections associated with a specific client (by name) and category (by name) in simplified format. " +
                     "Includes collections in both the specified category and its child categories."
    )
    public ResponseEntity<List<SimplifiedCollectionDTO>> getSimplifiedCollectionsByClientNameAndCategoryName(
            @PathVariable String clientName,
            @PathVariable String categoryName) {

        log.info("Getting simplified collections for client name: '{}' and category name: '{}'", clientName, categoryName);

        try {
            // Debug: First check what collections exist
            List<CollectionListing> allCollections = collectionListingService.getAllCollections();
            log.info("Debug: Total collections in database: {}", allCollections.size());

            // Debug: Check collections by client name only (including parent client)
            List<CollectionListing> clientCollections = allCollections.stream()
                .filter(c -> c.getCategory() != null && (
                    (c.getCategory().getClient() != null && clientName.equalsIgnoreCase(c.getCategory().getClient().getName().trim())) ||
                    (c.getCategory().getParentCategory() != null && c.getCategory().getParentCategory().getClient() != null &&
                     clientName.equalsIgnoreCase(c.getCategory().getParentCategory().getClient().getName().trim()))
                ))
                .toList();
            log.info("Debug: Collections found for client '{}': {}", clientName, clientCollections.size());

            // Debug: Check collections by category name only
            List<CollectionListing> categoryCollections = allCollections.stream()
                .filter(c -> c.getCategory() != null &&
                           categoryName.equalsIgnoreCase(c.getCategory().getCategoryName().trim()))
                .toList();
            log.info("Debug: Collections found for category '{}': {}", categoryName, categoryCollections.size());

            List<CollectionListing> collections = collectionListingService.getCollectionsByClientNameAndCategoryNameWithDetails(clientName, categoryName);

            if (collections.isEmpty()) {
                log.info("No collections found for client name: '{}' and category name: '{}'", clientName, categoryName);

                // Debug: Show what we actually have
                for (CollectionListing col : allCollections) {
                    String directClient = col.getCategory() != null && col.getCategory().getClient() != null ? col.getCategory().getClient().getName() : "NULL";
                    String parentClient = col.getCategory() != null && col.getCategory().getParentCategory() != null && col.getCategory().getParentCategory().getClient() != null ? col.getCategory().getParentCategory().getClient().getName() : "NULL";

                    log.info("Available Collection: '{}', Direct Client: '{}', Parent Client: '{}', Category: '{}', Parent Category: '{}'",
                        col.getCollectionName(),
                        directClient,
                        parentClient,
                        col.getCategory() != null ? col.getCategory().getCategoryName() : "NULL",
                        col.getCategory() != null && col.getCategory().getParentCategory() != null ? col.getCategory().getParentCategory().getCategoryName() : "NULL"
                    );
                }

                return ResponseEntity.noContent().build();
            }

            List<SimplifiedCollectionDTO> simplifiedCollections = simplifiedCollectionMapper.toDTOList(collections);
            log.info("Found {} simplified collections for client name: '{}' and category name: '{}'", simplifiedCollections.size(), clientName, categoryName);

            return ResponseEntity.ok(simplifiedCollections);
        } catch (Exception e) {
            log.error("Error getting simplified collections for client name: '{}' and category name: '{}'", clientName, categoryName, e);
            throw e;
        }
    }

    @GetMapping("/debug/all")
    @Operation(summary = "Debug: Get all collections in simplified format", description = "Returns all collections for debugging purposes")
    public ResponseEntity<List<SimplifiedCollectionDTO>> getAllSimplifiedCollectionsDebug() {
        log.info("Debug: Getting all simplified collections");

        try {
            List<CollectionListing> collections = collectionListingService.getAllCollections();
            log.info("Debug: Found {} total collections", collections.size());

            for (CollectionListing col : collections) {
                String directClient = col.getCategory() != null && col.getCategory().getClient() != null ? col.getCategory().getClient().getName() : "NULL";
                String parentClient = col.getCategory() != null && col.getCategory().getParentCategory() != null && col.getCategory().getParentCategory().getClient() != null ? col.getCategory().getParentCategory().getClient().getName() : "NULL";

                log.info("Debug Collection: '{}', Direct Client: '{}', Parent Client: '{}', Category: '{}', Parent Category: '{}'",
                    col.getCollectionName(),
                    directClient,
                    parentClient,
                    col.getCategory() != null ? col.getCategory().getCategoryName() : "NULL",
                    col.getCategory() != null && col.getCategory().getParentCategory() != null ? col.getCategory().getParentCategory().getCategoryName() : "NULL"
                );
            }

            List<SimplifiedCollectionDTO> simplifiedCollections = simplifiedCollectionMapper.toDTOList(collections);
            return ResponseEntity.ok(simplifiedCollections);
        } catch (Exception e) {
            log.error("Debug: Error getting all simplified collections", e);
            throw e;
        }
    }

    @GetMapping("/debug/clientName/{clientName}")
    @Operation(summary = "Debug: Get collections by client name in simplified format", description = "Returns all collections for a specific client")
    public ResponseEntity<List<SimplifiedCollectionDTO>> getSimplifiedCollectionsByClientNameDebug(@PathVariable String clientName) {
        log.info("Debug: Getting simplified collections for client name: '{}'", clientName);

        try {
            // Get all collections and filter by client name
            List<CollectionListing> allCollections = collectionListingService.getAllCollections();

            // Filter by client name manually (including parent client)
            List<CollectionListing> collections = allCollections.stream()
                .filter(c -> c.getCategory() != null && (
                    (c.getCategory().getClient() != null && clientName.equalsIgnoreCase(c.getCategory().getClient().getName().trim())) ||
                    (c.getCategory().getParentCategory() != null && c.getCategory().getParentCategory().getClient() != null &&
                     clientName.equalsIgnoreCase(c.getCategory().getParentCategory().getClient().getName().trim()))
                ))
                .toList();

            log.info("Debug: Found {} collections for client '{}'", collections.size(), clientName);

            List<SimplifiedCollectionDTO> simplifiedCollections = simplifiedCollectionMapper.toDTOList(collections);
            return ResponseEntity.ok(simplifiedCollections);
        } catch (Exception e) {
            log.error("Debug: Error getting simplified collections for client '{}'", clientName, e);
            throw e;
        }
    }
}

package com.cms.service;

import com.cms.entity.ComponentComponent;

import java.util.List;

public interface ComponentComponentService {
    List<ComponentComponent> getAllComponentComponents();
    List<ComponentComponent> getComponentComponentsByParentId(Integer parentComponentId);
    java.util.Optional<ComponentComponent> getComponentComponentById(Integer id);
    ComponentComponent createComponentComponent(ComponentComponent componentComponent);
    ComponentComponent updateComponentComponent(Integer id, ComponentComponent componentComponent);
    void deleteComponentComponent(Integer id);
    void reorderComponentComponents(Integer parentComponentId, List<Integer> componentComponentIds);
}

-- Indexes for users table
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_is_logged_in ON users(is_logged_in);

-- Indexes for field_types table
CREATE INDEX IF NOT EXISTS idx_field_types_name ON field_types(field_type_name);
CREATE INDEX IF NOT EXISTS idx_field_types_is_active ON field_types(is_active);

-- Indexes for config_types table
CREATE INDEX IF NOT EXISTS idx_config_types_name ON config_types(config_type_name);
CREATE INDEX IF NOT EXISTS idx_config_types_is_active ON config_types(is_active);

-- Indexes for field_configs table
CREATE INDEX IF NOT EXISTS idx_field_configs_field_type_id ON field_configs(field_type_id);
CREATE INDEX IF NOT EXISTS idx_field_configs_config_type_id ON field_configs(config_type_id);
CREATE INDEX IF NOT EXISTS idx_field_configs_config_name ON field_configs(config_name);
CREATE INDEX IF NOT EXISTS idx_field_configs_is_active ON field_configs(is_active);

-- Indexes for category table
CREATE INDEX IF NOT EXISTS idx_category_name ON category(category_name);

-- Indexes for component_listing table
CREATE INDEX IF NOT EXISTS idx_component_listing_component_name ON component_listing(component_name);
CREATE INDEX IF NOT EXISTS idx_component_listing_component_api_id ON component_listing(component_api_id);
CREATE INDEX IF NOT EXISTS idx_component_listing_is_active ON component_listing(is_active);

-- Indexes for component_fields table
CREATE INDEX IF NOT EXISTS idx_component_fields_component_id ON component_fields(component_id);
CREATE INDEX IF NOT EXISTS idx_component_fields_field_type_id ON component_fields(field_type_id);
CREATE INDEX IF NOT EXISTS idx_component_fields_dependent_on ON component_fields(dependent_on);

-- Indexes for component_field_config table
CREATE INDEX IF NOT EXISTS idx_component_field_config_component_field_id ON component_field_config(component_field_id);
CREATE INDEX IF NOT EXISTS idx_component_field_config_field_config_id ON component_field_config(field_config_id);

-- Indexes for collection_listing table
CREATE INDEX IF NOT EXISTS idx_collection_listing_collection_name ON collection_listing(collection_name);
CREATE INDEX IF NOT EXISTS idx_collection_listing_collection_api_id ON collection_listing(collection_api_id);

-- Indexes for collection_components table
CREATE INDEX IF NOT EXISTS idx_collection_components_collection_id ON collection_components(collection_id);
CREATE INDEX IF NOT EXISTS idx_collection_components_component_id ON collection_components(component_id);
CREATE INDEX IF NOT EXISTS idx_collection_components_is_active ON collection_components(is_active);

-- Indexes for collection_fields table
CREATE INDEX IF NOT EXISTS idx_collection_fields_collection_id ON collection_fields(collection_id);
CREATE INDEX IF NOT EXISTS idx_collection_fields_field_type_id ON collection_fields(field_type_id);
CREATE INDEX IF NOT EXISTS idx_collection_fields_dependent_on ON collection_fields(dependent_on);

-- Indexes for collection_field_config table
CREATE INDEX IF NOT EXISTS idx_collection_field_config_collection_field_id ON collection_field_config(collection_field_id);
CREATE INDEX IF NOT EXISTS idx_collection_field_config_field_config_id ON collection_field_config(field_config_id);

-- Indexes for content_entries table
CREATE INDEX IF NOT EXISTS idx_content_entries_collection_id ON content_entries(collection_id);

-- GIN index for JSONB data in content_entries
CREATE INDEX IF NOT EXISTS idx_content_entries_data_json_gin ON content_entries USING GIN (data_json);

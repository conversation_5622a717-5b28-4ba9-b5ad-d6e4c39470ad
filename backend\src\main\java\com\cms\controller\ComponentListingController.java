package com.cms.controller;

import com.cms.dto.ComponentWithChildrenDTO;
import com.cms.entity.ComponentListing;
import com.cms.exception.NoContentException;
import com.cms.exception.ResourceNotFoundException;
import com.cms.exception.UniqueConstraintViolationException;
import com.cms.mapper.ComponentWithChildrenMapper;
import com.cms.service.ComponentListingService;
import com.cms.service.ComponentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/components")
@RequiredArgsConstructor
@Tag(name = "Component", description = "Component API")
public class ComponentListingController {

    private final ComponentListingService componentListingService;
    private final ComponentService componentService;
    private final ComponentWithChildrenMapper componentWithChildrenMapper;

    @GetMapping("/getAll")
    @Operation(summary = "Get all components", description = "Returns a list of all components or 204 if no data is found")
    public ResponseEntity<List<ComponentListing>> getAllComponents() {
        System.out.println("ComponentListingController.getAllComponents() called");
        List<ComponentListing> components = componentListingService.getAllComponents();
        if (components.isEmpty()) {
            System.out.println("No components found");
            throw new NoContentException("No data in the table");
        }
        System.out.println("ComponentListingController.getAllComponents() returning " + components.size() + " components");
        return ResponseEntity.ok(components);
    }

    @GetMapping("/getAllWithRelationships")
    @Operation(summary = "Get all components with their relationships", description = "Returns a list of all components with their child components")
    public ResponseEntity<?> getAllComponentsWithRelationships() {
        List<ComponentListing> components = componentListingService.getAllComponents();

        if (components.isEmpty()) {
            return ResponseEntity.noContent().build();
        }

        // Convert to DTOs to properly serialize child components
        List<ComponentWithChildrenDTO> componentDTOs = componentWithChildrenMapper.toDTOList(components);
        return ResponseEntity.ok(componentDTOs);
    }

    @GetMapping("/getAllActive")
    @Operation(summary = "Get active components", description = "Returns a list of active components or 204 if no active components found")
    public ResponseEntity<?> getActiveComponents() {
        List<ComponentListing> activeComponents = componentListingService.getActiveComponents();
        if (activeComponents.isEmpty()) {
            return ResponseEntity.status(HttpStatus.NO_CONTENT)
                    .body(Map.of("message", "No data in the table"));
        }
        return ResponseEntity.ok(activeComponents);
    }

    @GetMapping("/getById/{id}")
    @Operation(summary = "Get component by ID", description = "Returns a component by its ID or 204 No Content if not found")
    public ResponseEntity<?> getComponentById(@PathVariable Integer id) {
        Optional<ComponentListing> componentOpt = componentListingService.getComponentById(id);
        if (componentOpt.isPresent()) {
            ComponentWithChildrenDTO componentDTO = componentWithChildrenMapper.toDTO(componentOpt.get());
            return ResponseEntity.ok(componentDTO);
        } else {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/getByApiId/{apiId}")
    @Operation(summary = "Get component by API ID", description = "Returns a component by its API ID or 204 No Content if not found")
    public ResponseEntity<?> getComponentByApiId(@PathVariable String apiId) {
        Optional<ComponentListing> componentOpt = componentListingService.getComponentByApiId(apiId);
        if (componentOpt.isPresent()) {
            ComponentWithChildrenDTO componentDTO = componentWithChildrenMapper.toDTO(componentOpt.get());
            return ResponseEntity.ok(componentDTO);
        } else {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/getByIdWithDetails/{id}")
    @Operation(summary = "Get component by ID with details", description = "Returns a component by its ID with all related details or 204 No Content if not found")
    public ResponseEntity<?> getComponentByIdWithDetails(@PathVariable Integer id) {
        Optional<ComponentListing> componentOpt = componentListingService.getComponentById(id);
        if (componentOpt.isPresent()) {
            ComponentWithChildrenDTO componentDTO = componentWithChildrenMapper.toDTO(componentOpt.get());
            return ResponseEntity.ok(componentDTO);
        } else {
            return ResponseEntity.noContent().build();
        }
    }



    @PostMapping("/create")
    @Operation(
        summary = "Create a new component",
        description = "Creates a new component",
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "Component object to be created",
            content = @Content(
                mediaType = "application/json",
                examples = {
                    @ExampleObject(
                        name = "Standard Component",
                        summary = "Standard component creation example",
                        value = "{\"componentName\":\"Example Component\",\"componentDisplayName\":\"Example Component Display\",\"componentApiId\":\"example_component\",\"isActive\":true,\"getUrl\":\"/api/example\",\"postUrl\":\"/api/example/create\",\"updateUrl\":\"/api/example/update\"}"
                    )
                },
                schema = @Schema(implementation = ComponentListing.class)
            )
        ),
        responses = {
            @ApiResponse(
                responseCode = "201",
                description = "Component created successfully",
                content = @Content(mediaType = "application/json")
            ),
            @ApiResponse(
                responseCode = "400",
                description = "Invalid input or component name already exists",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<ComponentListing> createComponent(@Valid @RequestBody ComponentListing component) {
        if (componentListingService.existsByComponentName(component.getComponentName())) {
            throw new UniqueConstraintViolationException("componentName", component.getComponentName());
        }
        return ResponseEntity.status(HttpStatus.CREATED).body(componentListingService.createComponent(component));
    }

    @PutMapping("/update/{id}")
    @Operation(
        summary = "Update a component",
        description = "Updates an existing component",
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "Component object with updated values",
            content = @Content(
                mediaType = "application/json",
                examples = {
                    @ExampleObject(
                        name = "Update Component",
                        summary = "Component update example",
                        value = "{\"id\":100,\"componentName\":\"Updated Component\",\"componentDisplayName\":\"Updated Component Display\",\"componentApiId\":\"updated_component\",\"isActive\":true,\"getUrl\":\"/api/updated\",\"postUrl\":\"/api/updated/create\",\"updateUrl\":\"/api/updated/update\"}"
                    )
                },
                schema = @Schema(implementation = ComponentListing.class)
            )
        ),
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "Component updated successfully",
                content = @Content(mediaType = "application/json")
            ),
            @ApiResponse(
                responseCode = "404",
                description = "Component not found",
                content = @Content(mediaType = "application/json")
            ),
            @ApiResponse(
                responseCode = "701",
                description = "Component name already exists",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<ComponentListing> updateComponent(@PathVariable Integer id, @Valid @RequestBody ComponentListing component) {
        // Get the existing component
        ComponentListing existingComponent = componentListingService.getComponentById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Component not found with id: " + id));

        // Check if component name already exists (excluding the current component)
        if (!existingComponent.getComponentName().equals(component.getComponentName()) &&
            componentListingService.existsByComponentName(component.getComponentName())) {
            throw new UniqueConstraintViolationException("componentName", component.getComponentName());
        }

        return ResponseEntity.ok(componentListingService.updateComponent(id, component));
    }

    @DeleteMapping("/deleteById/{id}")
    @Operation(summary = "Delete a component", description = "Deletes a component and all its related fields and configurations")
    public ResponseEntity<Void> deleteComponent(@PathVariable Integer id) {
        // Use the ComponentService for proper cascading delete
        componentService.deleteComponent(id);
        return ResponseEntity.noContent().build();
    }
}

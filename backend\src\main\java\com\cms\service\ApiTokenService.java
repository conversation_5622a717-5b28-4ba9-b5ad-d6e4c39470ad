package com.cms.service;

import com.cms.entity.ApiToken;
import com.cms.entity.User;

import java.util.List;
import java.util.Optional;

public interface ApiTokenService {
    
    /**
     * Generate a new API token for a user
     * 
     * @param user The user to generate the token for
     * @param name The name of the token
     * @param description The description of the token
     * @param expirationDays Number of days until the token expires
     * @return The generated API token
     */
    ApiToken generateToken(User user, String name, String description, int expirationDays);
    
    /**
     * Get all tokens for a user
     * 
     * @param user The user to get tokens for
     * @return List of API tokens
     */
    List<ApiToken> getTokensByUser(User user);
    
    /**
     * Get all active tokens for a user
     * 
     * @param user The user to get tokens for
     * @return List of active API tokens
     */
    List<ApiToken> getActiveTokensByUser(User user);
    
    /**
     * Find a token by its value
     * 
     * @param tokenValue The token value to search for
     * @return Optional containing the token if found
     */
    Optional<ApiToken> findByTokenValue(String tokenValue);
    
    /**
     * Revoke (deactivate) a token
     * 
     * @param tokenId The ID of the token to revoke
     * @return The revoked token
     */
    ApiToken revokeToken(Long tokenId);
    
    /**
     * Update the last used timestamp for a token
     * 
     * @param token The token to update
     * @return The updated token
     */
    ApiToken updateLastUsed(ApiToken token);
    
    /**
     * Clean up expired tokens
     * 
     * @return Number of tokens deleted
     */
    int cleanupExpiredTokens();
}

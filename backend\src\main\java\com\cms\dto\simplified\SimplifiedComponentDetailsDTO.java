package com.cms.dto.simplified;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SimplifiedComponentDetailsDTO {
    private Integer id;
    private String componentName;
    private String componentDisplayName;
    private String componentApiId;
    private Boolean isActive;
    private String getUrl;
    private String postUrl;
    private String updateUrl;
    private List<SimplifiedFieldDTO> fields = new ArrayList<>();
    private List<SimplifiedChildComponentDTO> childComponents = new ArrayList<>();
}

package com.cms.config;

import com.cms.util.NetworkUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@Configuration
public class FileStorageConfig {

    @Value("${file.upload-dir:uploads}")
    private String uploadDir;

    @Value("${file.base-url:http://localhost:8071/api}")
    private String baseUrl;

    @Value("${server.external-url:http://localhost:8071}")
    private String serverExternalUrl;

    @Value("${server.port:8071}")
    private String serverPort;

    @Autowired
    private NetworkUtil networkUtil;

    @Bean
    public Path fileStorageLocation() {
        Path fileStorageLocation;

        // Always create the directory relative to the application's directory
        String currentDir = System.getProperty("user.dir");
        fileStorageLocation = Paths.get(currentDir, uploadDir);

        try {
            // Ensure the directory exists
            if (!Files.exists(fileStorageLocation)) {
                Files.createDirectories(fileStorageLocation);
                System.out.println("Created media upload directory at: " + fileStorageLocation.toAbsolutePath());
            } else {
                System.out.println("Using existing media upload directory at: " + fileStorageLocation.toAbsolutePath());
            }
            return fileStorageLocation;
        } catch (Exception ex) {
            throw new RuntimeException("Could not create the directory where the uploaded files will be stored: " +
                                      fileStorageLocation.toAbsolutePath(), ex);
        }
    }

    @Bean
    public String mediaBaseUrl() {
        // Use the dynamic IP address instead of the configured one
        String ipAddress = networkUtil.getHostIpAddress();
        // Add a timestamp parameter to prevent caching issues
        return "http://" + ipAddress + ":" + serverPort + "/api/media/files";
    }

    @Bean
    public String serverExternalUrl() {
        // Use the dynamic IP address instead of the configured one
        String ipAddress = networkUtil.getHostIpAddress();
        return "http://" + ipAddress + ":" + serverPort;
    }
}

import React from 'react';
import {
  AlertD<PERSON>og,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { AlertTriangle, Trash2 } from 'lucide-react';

interface DeleteCategoryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  categoryName: string;
  collectionCount: number;
}

export function DeleteCategoryDialog({
  isOpen,
  onClose,
  onConfirm,
  categoryName,
  collectionCount,
}: DeleteCategoryDialogProps) {
  const hasCollections = collectionCount > 0;

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            {hasCollections ? (
              <AlertTriangle className="h-5 w-5 text-destructive" />
            ) : (
              <Trash2 className="h-5 w-5 text-destructive" />
            )}
            {hasCollections ? 'Cannot Delete Category' : 'Delete Category'}
          </AlertDialogTitle>
          <AlertDialogDescription>
            {hasCollections ? (
              <div className="space-y-3">
                <p>
                  The category "{categoryName}" cannot be deleted because it contains{' '}
                  <span className="font-semibold text-destructive">
                    {collectionCount} collection{collectionCount !== 1 ? 's' : ''}
                  </span>.
                </p>
                <div className="bg-destructive/10 border border-destructive/20 rounded-md p-3">
                  <p className="text-sm font-medium text-destructive">
                    To delete this category, you must first:
                  </p>
                  <ul className="text-sm text-destructive mt-1 ml-4 list-disc">
                    <li>Delete all collections in this category, or</li>
                    <li>Move the collections to another category</li>
                  </ul>
                </div>
              </div>
            ) : (
              <p>
                Are you sure you want to delete the category "{categoryName}"?
                This action cannot be undone.
              </p>
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>
            {hasCollections ? 'Close' : 'Cancel'}
          </AlertDialogCancel>
          {!hasCollections && (
            <AlertDialogAction
              onClick={onConfirm}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete Category
            </AlertDialogAction>
          )}
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

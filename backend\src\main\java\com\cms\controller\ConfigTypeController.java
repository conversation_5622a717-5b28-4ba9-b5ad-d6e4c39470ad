package com.cms.controller;

import com.cms.entity.ConfigType;
import com.cms.exception.NoContentException;
import com.cms.exception.UniqueConstraintViolationException;
import com.cms.service.ConfigTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/config-types")
@RequiredArgsConstructor
@Tag(name = "Config Type", description = "Config Type API")
public class ConfigTypeController {

    private final ConfigTypeService configTypeService;

    @GetMapping("/getAll")
    @Operation(summary = "Get all config types", description = "Returns a list of all config types")
    public ResponseEntity<List<ConfigType>> getAllConfigTypes() {
        List<ConfigType> configTypes = configTypeService.getAllConfigTypes();
        if (configTypes.isEmpty()) {
            throw new NoContentException("No config types found");
        }
        return ResponseEntity.ok(configTypes);
    }

    @GetMapping("/getAllActive")
    @Operation(summary = "Get active config types", description = "Returns a list of active config types")
    public ResponseEntity<List<ConfigType>> getActiveConfigTypes() {
        List<ConfigType> activeConfigTypes = configTypeService.getActiveConfigTypes();
        if (activeConfigTypes.isEmpty()) {
            throw new NoContentException("No active config types found");
        }
        return ResponseEntity.ok(activeConfigTypes);
    }

    @GetMapping("/getById/{id}")
    @Operation(summary = "Get config type by ID", description = "Returns a config type by its ID or 204 No Content if not found")
    public ResponseEntity<?> getConfigTypeById(@PathVariable Integer id) {
        Optional<ConfigType> configType = configTypeService.getConfigTypeById(id);
        if (configType.isPresent()) {
            return ResponseEntity.ok(configType.get());
        } else {
            return ResponseEntity.noContent().build();
        }
    }

    @PostMapping("/create")
    @Operation(summary = "Create a new config type", description = "Creates a new config type")
    public ResponseEntity<ConfigType> createConfigType(@Valid @RequestBody ConfigType configType) {
        if (configTypeService.existsByConfigTypeName(configType.getConfigTypeName())) {
            throw new UniqueConstraintViolationException("configTypeName", configType.getConfigTypeName());
        }
        return ResponseEntity.status(HttpStatus.CREATED).body(configTypeService.createConfigType(configType));
    }

    @PutMapping("/update/{id}")
    @Operation(summary = "Update a config type", description = "Updates an existing config type")
    public ResponseEntity<ConfigType> updateConfigType(@PathVariable Integer id, @Valid @RequestBody ConfigType configType) {
        return ResponseEntity.ok(configTypeService.updateConfigType(id, configType));
    }

    @DeleteMapping("/deleteById/{id}")
    @Operation(summary = "Delete a config type", description = "Soft deletes a config type by setting isActive to false")
    public ResponseEntity<Void> deleteConfigType(@PathVariable Integer id) {
        configTypeService.deleteConfigType(id);
        return ResponseEntity.noContent().build();
    }
}

package com.cms.config;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * Special CORS filter specifically for media upload endpoints to prevent ORB blocking issues.
 * This filter has higher precedence than the CustomCorsFilter.
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE - 1)
@Slf4j
@PropertySource("classpath:cors-config.properties")
public class MediaUploadCorsFilter implements Filter {

    @Value("${cors.resource-policy:cross-origin}")
    private String resourcePolicy;

    @Value("${cors.embedder-policy:unsafe-none}")
    private String embedderPolicy;

    @Value("${cors.opener-policy:unsafe-none}")
    private String openerPolicy;

    @Value("${cors.exposed-headers:Content-Disposition,Cross-Origin-Resource-Policy,Cross-Origin-Embedder-Policy,Cross-Origin-Opener-Policy}")
    private String exposedHeaders;

    @Override
    public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain)
            throws IOException, ServletException {
        HttpServletResponse response = (HttpServletResponse) res;
        HttpServletRequest request = (HttpServletRequest) req;

        String requestURI = request.getRequestURI();
        String method = request.getMethod();
        String origin = request.getHeader("Origin");

        log.debug("MediaUploadCorsFilter processing request: {} {} from origin: {}", method, requestURI, origin);

        // Apply this filter to all media endpoints to ensure proper CORS handling
        if (requestURI.contains("/media/") || requestURI.contains("/api/media/")) {
            log.debug("Applying special CORS headers for media endpoint: {}", requestURI);

            // Set permissive CORS headers for all media operations
            if (origin != null) {
                response.setHeader("Access-Control-Allow-Origin", origin);
                log.debug("Setting Access-Control-Allow-Origin to specific origin: {}", origin);
            } else {
                response.setHeader("Access-Control-Allow-Origin", "*");
                log.debug("Setting Access-Control-Allow-Origin to wildcard '*'");
            }

            response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH");
            response.setHeader("Access-Control-Allow-Headers", "*");
            response.setHeader("Access-Control-Max-Age", "3600");
            response.setHeader("Access-Control-Allow-Credentials", "true");

            // Add exposed headers
            response.setHeader("Access-Control-Expose-Headers", exposedHeaders);

            // Disable ORB protection for all media endpoints
            response.setHeader("Cross-Origin-Resource-Policy", resourcePolicy);
            response.setHeader("Cross-Origin-Embedder-Policy", embedderPolicy);
            response.setHeader("Cross-Origin-Opener-Policy", openerPolicy);

            // Add additional headers to prevent caching issues
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");

            log.debug("Added ORB prevention headers for: {}", requestURI);

            // Handle preflight requests
            if ("OPTIONS".equalsIgnoreCase(method)) {
                log.debug("Handling OPTIONS preflight request for: {}", requestURI);
                response.setStatus(HttpServletResponse.SC_OK);
                return;
            }
        }

        chain.doFilter(req, res);
    }

    @Override
    public void init(FilterConfig filterConfig) {
    }

    @Override
    public void destroy() {
    }
}

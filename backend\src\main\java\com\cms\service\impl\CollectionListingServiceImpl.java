package com.cms.service.impl;

import com.cms.entity.CollectionComponent;
import com.cms.entity.CollectionListing;
import com.cms.entity.ComponentComponent;
import com.cms.entity.ComponentField;
import com.cms.entity.ComponentFieldConfig;
import com.cms.entity.ComponentListing;
import com.cms.exception.NullConstraintViolationException;
import com.cms.exception.ResourceNotFoundException;
import com.cms.exception.UniqueConstraintViolationException;
import com.cms.repository.CollectionComponentRepository;
import com.cms.repository.CollectionListingRepository;
import com.cms.repository.ComponentComponentRepository;
import com.cms.repository.ComponentFieldConfigRepository;
import com.cms.repository.ComponentFieldRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.cms.service.CollectionListingService;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class CollectionListingServiceImpl implements CollectionListingService {

    private final CollectionListingRepository collectionListingRepository;
    private final CollectionComponentRepository collectionComponentRepository;
    private final ComponentFieldRepository componentFieldRepository;
    private final ComponentFieldConfigRepository componentFieldConfigRepository;
    private final ComponentComponentRepository componentComponentRepository;

    @Override
    public List<CollectionListing> getAllCollections() {
        List<CollectionListing> collections = collectionListingRepository.findAll();
        log.debug("Retrieved {} collections", collections.size());
        return collections;
    }

    @Override
    @Transactional(readOnly = true)
    public List<CollectionListing> getAllCollectionsWithDetails() {
        List<CollectionListing> collections = collectionListingRepository.findAll();
        log.debug("Retrieved {} collections for detailed view", collections.size());

        collections.forEach(this::loadCollectionDetails);
        return collections;
    }

    @Override
    public Optional<CollectionListing> getCollectionById(Integer id) {
        if (id == null) {
            throw new NullConstraintViolationException("id");
        }

        Optional<CollectionListing> collection = collectionListingRepository.findById(id);
        log.debug("Retrieved collection by ID: {}, found: {}", id, collection.isPresent());
        return collection;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<CollectionListing> getCollectionByIdWithDetails(Integer id) {
        if (id == null) {
            throw new NullConstraintViolationException("id");
        }

        Optional<CollectionListing> collectionOpt = collectionListingRepository.findById(id);
        log.debug("Retrieved collection with details by ID: {}, found: {}", id, collectionOpt.isPresent());

        collectionOpt.ifPresent(this::loadCollectionDetails);
        return collectionOpt;
    }

    @Override
    public Optional<CollectionListing> getCollectionByApiId(String apiId) {
        if (apiId == null || apiId.trim().isEmpty()) {
            throw new NullConstraintViolationException("apiId");
        }

        Optional<CollectionListing> collection = collectionListingRepository.findByCollectionApiId(apiId);
        log.debug("Retrieved collection by API ID: {}, found: {}", apiId, collection.isPresent());
        return collection;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<CollectionListing> getCollectionByApiIdWithDetails(String apiId) {
        if (apiId == null || apiId.trim().isEmpty()) {
            throw new NullConstraintViolationException("apiId");
        }

        Optional<CollectionListing> collectionOpt = collectionListingRepository.findByCollectionApiId(apiId);
        log.debug("Retrieved collection with details by API ID: {}, found: {}", apiId, collectionOpt.isPresent());

        collectionOpt.ifPresent(this::loadCollectionDetails);
        return collectionOpt;
    }

    private void loadCollectionDetails(CollectionListing collection) {
        // Load collection components
        List<CollectionComponent> components = collectionComponentRepository.findByCollectionAndIsActiveTrue(collection);
        collection.setComponents(components);

        // Load component fields and configs for each component
        components.forEach(component -> {
            if (component.getComponent() != null) {
                // Load fields and configs
                List<ComponentField> fields = componentFieldRepository.findByComponentOrderByDisplayPreference(component.getComponent());
                component.getComponent().setFields(fields);

                // Load field configs for each field
                fields.forEach(field -> {
                    List<ComponentFieldConfig> configs = componentFieldConfigRepository.findByComponentField(field);
                    field.setConfigs(configs);
                });

                // Load child components
                loadChildComponents(component.getComponent());
            }
        });
    }

    private void loadChildComponents(ComponentListing component) {
        // Load child components from the database
        List<ComponentComponent> childComponents = componentComponentRepository.findByParentComponentAndIsActiveTrue(component);
        component.setChildComponents(childComponents);

        // Recursively load fields and child components for each child component
        childComponents.forEach(childComponent -> {
            if (childComponent.getChildComponent() != null) {
                // Load fields and configs
                List<ComponentField> fields = componentFieldRepository.findByComponentOrderByDisplayPreference(childComponent.getChildComponent());
                childComponent.getChildComponent().setFields(fields);

                // Load field configs for each field
                fields.forEach(field -> {
                    List<ComponentFieldConfig> configs = componentFieldConfigRepository.findByComponentField(field);
                    field.setConfigs(configs);
                });

                // Recursively load child components
                loadChildComponents(childComponent.getChildComponent());
            }
        });
    }

    @Override
    @Transactional
    public CollectionListing createCollection(CollectionListing collection) {
        // Validate required fields
        if (collection.getCollectionName() == null || collection.getCollectionName().trim().isEmpty()) {
            throw new NullConstraintViolationException("collectionName");
        }

        // Validate category is provided
        if (collection.getCategory() == null || collection.getCategory().getId() == null) {
            throw new NullConstraintViolationException("category");
        }

        // Check for unique constraint violations within the same category
        if (existsByCollectionNameAndCategoryId(collection.getCollectionName(), collection.getCategory().getId())) {
            throw new UniqueConstraintViolationException("collectionName",
                "Collection name '" + collection.getCollectionName() + "' already exists in category ID: " + collection.getCategory().getId());
        }

        // Check for unique API ID if provided
        if (collection.getCollectionApiId() != null && !collection.getCollectionApiId().trim().isEmpty()) {
            if (collectionListingRepository.existsByCollectionApiId(collection.getCollectionApiId())) {
                throw new UniqueConstraintViolationException("collectionApiId", collection.getCollectionApiId());
            }
        }

        // Save the collection
        CollectionListing savedCollection = collectionListingRepository.save(collection);
        log.info("Created new collection with ID: {}", savedCollection.getId());
        return savedCollection;
    }

    @Override
    @Transactional
    public CollectionListing updateCollection(Integer id, CollectionListing collection) {
        // Validate ID
        if (id == null) {
            throw new NullConstraintViolationException("id");
        }

        // Verify the collection exists
        CollectionListing existingCollection = collectionListingRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Collection not found with id: " + id));

        // Validate required fields
        if (collection.getCollectionName() == null || collection.getCollectionName().trim().isEmpty()) {
            throw new NullConstraintViolationException("collectionName");
        }

        // Validate category is provided if being updated
        Integer categoryId = existingCollection.getCategory().getId();
        if (collection.getCategory() != null && collection.getCategory().getId() != null) {
            categoryId = collection.getCategory().getId();
        }

        // Check for unique constraint violations within the same category (only if name or category has changed)
        boolean nameChanged = !collection.getCollectionName().equals(existingCollection.getCollectionName());
        boolean categoryChanged = collection.getCategory() != null &&
                                 collection.getCategory().getId() != null &&
                                 !collection.getCategory().getId().equals(existingCollection.getCategory().getId());

        if (nameChanged || categoryChanged) {
            if (existsByCollectionNameAndCategoryId(collection.getCollectionName(), categoryId)) {
                throw new UniqueConstraintViolationException("collectionName",
                    "Collection name '" + collection.getCollectionName() + "' already exists in category ID: " + categoryId);
            }
        }

        // Check for unique API ID if provided and changed
        if (collection.getCollectionApiId() != null && !collection.getCollectionApiId().trim().isEmpty() &&
                !collection.getCollectionApiId().equals(existingCollection.getCollectionApiId())) {
            if (collectionListingRepository.existsByCollectionApiId(collection.getCollectionApiId())) {
                throw new UniqueConstraintViolationException("collectionApiId", collection.getCollectionApiId());
            }
        }

        // Update fields
        existingCollection.setCollectionName(collection.getCollectionName());

        if (collection.getCollectionDesc() != null) {
            existingCollection.setCollectionDesc(collection.getCollectionDesc());
        }

        if (collection.getAdditionalInformation() != null) {
            existingCollection.setAdditionalInformation(collection.getAdditionalInformation());
        }

        if (collection.getDisclaimerText() != null) {
            existingCollection.setDisclaimerText(collection.getDisclaimerText());
        }

        if (collection.getCollectionApiId() != null) {
            existingCollection.setCollectionApiId(collection.getCollectionApiId());
        }

        // Save the updated collection
        CollectionListing updatedCollection = collectionListingRepository.save(existingCollection);
        log.info("Updated collection with ID: {}", updatedCollection.getId());
        return updatedCollection;
    }

    @Override
    @Transactional
    public void deleteCollection(Integer id) {
        // Validate ID
        if (id == null) {
            throw new NullConstraintViolationException("id");
        }

        // Verify the collection exists
        CollectionListing collection = collectionListingRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Collection not found with id: " + id));

        // Find all collection components associated with this collection
        List<CollectionComponent> components = collectionComponentRepository.findByCollectionAndIsActiveTrue(collection);

        // For each component, delete its fields and field configs
        for (CollectionComponent component : components) {
            // Find all component fields
            List<ComponentField> fields = componentFieldRepository.findByComponent(component.getComponent());

            // For each field, delete its configs first
            for (ComponentField field : fields) {
                List<ComponentFieldConfig> configs = componentFieldConfigRepository.findByComponentField(field);
                if (configs != null && !configs.isEmpty()) {
                    componentFieldConfigRepository.deleteAll(configs);
                    log.debug("Deleted {} configs for component field ID: {}", configs.size(), field.getId());
                }
            }

            // Delete the component fields
            if (!fields.isEmpty()) {
                componentFieldRepository.deleteAll(fields);
                log.debug("Deleted {} fields for component ID: {}", fields.size(), component.getComponent().getId());
            }

            // Delete the collection component
            collectionComponentRepository.delete(component);
            log.debug("Deleted collection component ID: {}", component.getId());
        }

        // Delete the collection
        collectionListingRepository.delete(collection);
        log.info("Deleted collection with ID: {}", id);

        // Verify deletion
        if (collectionListingRepository.existsById(id)) {
            log.error("Failed to delete collection with ID: {}", id);
            throw new RuntimeException("Failed to delete collection with ID: " + id);
        }
    }

    @Override
    public boolean existsByCollectionName(String collectionName) {
        return collectionListingRepository.existsByCollectionName(collectionName);
    }

    @Override
    public boolean existsByCollectionNameAndCategoryId(String collectionName, Integer categoryId) {
        if (collectionName == null || collectionName.trim().isEmpty()) {
            throw new NullConstraintViolationException("collectionName");
        }
        if (categoryId == null) {
            throw new NullConstraintViolationException("categoryId");
        }

        return collectionListingRepository.existsByCollectionNameAndCategoryId(collectionName, categoryId);
    }

    @Override
    public List<CollectionListing> getCollectionsByClientId(Integer clientId) {
        if (clientId == null) {
            throw new NullConstraintViolationException("clientId");
        }

        log.debug("Getting collections for client ID: {}", clientId);
        List<CollectionListing> collections = collectionListingRepository.findByClientId(clientId);
        log.debug("Found {} collections for client ID: {}", collections.size(), clientId);

        return collections;
    }

    @Override
    @Transactional(readOnly = true)
    public List<CollectionListing> getCollectionsByClientIdWithDetails(Integer clientId) {
        if (clientId == null) {
            throw new NullConstraintViolationException("clientId");
        }

        log.debug("Getting collections with details for client ID: {}", clientId);
        List<CollectionListing> collections = collectionListingRepository.findByClientId(clientId);
        log.debug("Found {} collections for client ID: {}", collections.size(), clientId);

        // Load details for each collection
        collections.forEach(this::loadCollectionDetails);

        return collections;
    }

    @Override
    public List<CollectionListing> getCollectionsByCategoryId(Integer categoryId) {
        if (categoryId == null) {
            throw new NullConstraintViolationException("categoryId");
        }

        log.debug("Getting collections for category ID: {}", categoryId);
        List<CollectionListing> collections = collectionListingRepository.findByCategoryId(categoryId);
        log.debug("Found {} collections for category ID: {}", collections.size(), categoryId);

        return collections;
    }
    @Override
    @Transactional(readOnly = true)
    public List<CollectionListing> getCollectionsByClientIdAndCategoryIdWithDetails(Integer clientId, Integer categoryId) {
        if (clientId == null) {
            throw new NullConstraintViolationException("clientId");
        }
        if (categoryId == null) {
            throw new NullConstraintViolationException("categoryId");
        }

        log.debug("Getting collections with details for client ID: {} and category ID: {}", clientId, categoryId);
        List<CollectionListing> collections = collectionListingRepository.findByClientIdAndCategoryIdWithDetails(clientId, categoryId);
        log.debug("Found {} collections for client ID: {} and category ID: {}", collections.size(), clientId, categoryId);

        // Load full details for each collection
        collections.forEach(this::loadCollectionDetails);

        return collections;
    }

    @Override
    @Transactional(readOnly = true)
    public List<CollectionListing> getCollectionsByClientIdAndCategoryNameWithDetails(Integer clientId, String categoryName) {
        if (clientId == null) {
            throw new NullConstraintViolationException("clientId");
        }
        if (categoryName == null || categoryName.trim().isEmpty()) {
            throw new NullConstraintViolationException("categoryName");
        }

        log.debug("Getting collections with details for client ID: {} and category name: {}", clientId, categoryName);

        List<CollectionListing> collections = collectionListingRepository.findByClientIdAndCategoryNameWithDetails(clientId, categoryName);
        log.info("Found {} collections for client ID: {} and category name: {}", collections.size(), clientId, categoryName);

        // Load full details for each collection
        collections.forEach(this::loadCollectionDetails);

        return collections;
    }

    @Override
    @Transactional(readOnly = true)
    public List<CollectionListing> getCollectionsByClientNameAndCategoryNameWithDetails(String clientName, String categoryName) {
        if (clientName == null || clientName.trim().isEmpty()) {
            throw new NullConstraintViolationException("clientName");
        }
        if (categoryName == null || categoryName.trim().isEmpty()) {
            throw new NullConstraintViolationException("categoryName");
        }

        log.info("Getting collections with details for client name: '{}' and category name: '{}'", clientName, categoryName);

        List<CollectionListing> collections = collectionListingRepository.findByClientNameAndCategoryNameWithDetails(clientName, categoryName);
        log.info("Found {} collections for client name: '{}' and category name: '{}'", collections.size(), clientName, categoryName);

        // Load full details for each collection
        collections.forEach(this::loadCollectionDetails);

        return collections;
    }

    @Override
    @Transactional(readOnly = true)
    public List<CollectionListing> getCollectionsByClientNameAndCategoryNameHierarchical(String clientName, String categoryName) {
        if (clientName == null || clientName.trim().isEmpty()) {
            throw new NullConstraintViolationException("clientName");
        }
        if (categoryName == null || categoryName.trim().isEmpty()) {
            throw new NullConstraintViolationException("categoryName");
        }

        log.debug("Getting collections with hierarchical support for client name: {} and category name: {}", clientName, categoryName);

        List<CollectionListing> collections = collectionListingRepository.findByClientNameAndCategoryNameHierarchical(clientName, categoryName);
        log.info("Found {} collections (hierarchical) for client name: {} and category name: {}", collections.size(), clientName, categoryName);

        // Load full details for each collection
        collections.forEach(this::loadCollectionDetails);

        return collections;
    }

}

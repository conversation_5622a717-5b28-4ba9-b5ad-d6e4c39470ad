import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FileText, Folder, Upload } from 'lucide-react';

// Define types for media assets and folders
interface MediaAsset {
  id: number;
  fileName: string;
  originalFileName: string;
  fileType: string;
  fileSize: number;
  publicUrl: string;
  uploadedByUsername?: string;
  createdAt: string;
}

interface MediaFolder {
  id: number;
  folderName: string;
  description?: string;
  mediaCount: number;
  createdByUsername?: string;
  createdAt: string;
}

export default function MediaLibraryBasic() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [assets, setAssets] = useState<MediaAsset[]>([]);
  const [folders, setFolders] = useState<MediaFolder[]>([]);

  // Set document title and load mock data
  useEffect(() => {
    document.title = 'Media Library | R-CMS';
    console.log('MediaLibraryBasic component mounted');
    
    // Mock data
    const mockFolders = [
      {
        id: 1,
        folderName: 'Test Folder 1',
        description: 'Test folder description',
        mediaCount: 2,
        createdByUsername: 'admin',
        createdAt: new Date().toISOString()
      },
      {
        id: 2,
        folderName: 'Test Folder 2',
        description: 'Another test folder',
        mediaCount: 0,
        createdByUsername: 'admin',
        createdAt: new Date().toISOString()
      }
    ];
    
    const mockAssets = [
      {
        id: 1,
        fileName: 'test-image.jpg',
        originalFileName: 'test-image.jpg',
        fileType: 'image/jpeg',
        fileSize: 12345,
        publicUrl: 'https://via.placeholder.com/150',
        uploadedByUsername: 'admin',
        createdAt: new Date().toISOString()
      },
      {
        id: 2,
        fileName: 'test-document.pdf',
        originalFileName: 'test-document.pdf',
        fileType: 'application/pdf',
        fileSize: 54321,
        publicUrl: 'https://example.com/test.pdf',
        uploadedByUsername: 'admin',
        createdAt: new Date().toISOString()
      }
    ];
    
    // Set the mock data with a delay to simulate loading
    setTimeout(() => {
      setFolders(mockFolders);
      setAssets(mockAssets);
      setLoading(false);
    }, 500);
  }, []);

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Media Library (Basic)</h1>
        <Button>
          <Upload className="mr-2 h-4 w-4" />
          Add new assets
        </Button>
      </div>

      {loading ? (
        <div className="flex justify-center items-center p-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      ) : error ? (
        <div className="p-8 space-y-4 border border-destructive/50 rounded-lg bg-destructive/10">
          <h2 className="text-xl font-bold text-destructive">Error</h2>
          <p>{error}</p>
          <Button onClick={() => window.location.reload()} variant="outline">
            Reload Page
          </Button>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Folders */}
          {folders.length > 0 && (
            <div>
              <h2 className="text-xl font-semibold mb-4">Folders</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {folders.map(folder => (
                  <Card key={folder.id} className="cursor-pointer hover:bg-muted/10">
                    <CardHeader className="pb-2">
                      <CardTitle className="flex items-center text-lg">
                        <Folder className="h-5 w-5 mr-2 text-primary" />
                        {folder.folderName}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">{folder.description || 'No description'}</p>
                      <p className="text-xs mt-2">{folder.mediaCount} items</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Assets */}
          {assets.length > 0 && (
            <div>
              <h2 className="text-xl font-semibold mb-4">Files</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {assets.map(asset => (
                  <Card key={asset.id} className="cursor-pointer hover:bg-muted/10">
                    <CardHeader className="pb-2">
                      <CardTitle className="flex items-center text-lg">
                        <FileText className="h-5 w-5 mr-2 text-primary" />
                        {asset.originalFileName}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">{asset.fileType}</p>
                      <p className="text-xs mt-2">{formatFileSize(asset.fileSize)}</p>
                      {asset.fileType.startsWith('image/') && (
                        <div className="mt-2">
                          <img 
                            src={asset.publicUrl} 
                            alt={asset.originalFileName} 
                            className="rounded-md max-h-24 object-cover"
                          />
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Empty state */}
          {folders.length === 0 && assets.length === 0 && (
            <div className="flex flex-col items-center justify-center p-12 border border-dashed rounded-lg bg-muted/20">
              <div className="w-20 h-20 mb-4 flex items-center justify-center rounded-full bg-primary/10">
                <FileText className="h-10 w-10 text-primary" />
              </div>
              <h3 className="text-xl font-medium mb-2">No media found</h3>
              <p className="text-muted-foreground mb-6 text-center max-w-md">
                Upload files to start building your media library
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

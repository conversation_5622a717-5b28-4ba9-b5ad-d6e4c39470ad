package com.cms.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "field_types")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class FieldType extends Auditable {

    @Id
     @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "cms_field_type_seq")
    @SequenceGenerator(name = "cms_field_type_seq", sequenceName = "cms_field_type_seq", initialValue = 100, allocationSize = 1)
    private Integer id;

    @NotBlank(message = "Field type name is required")
    @Column(name = "field_type_name", nullable = false, unique=true)
    private String fieldTypeName;

    @Column(name = "field_type_desc")
    private String fieldTypeDesc;

    @Column(name = "display_name")
    private String displayName;

    @Column(name = "help_text")
    private String helpText;

    @Column(name = "logo_image_path")
    private String logoImagePath;

    @Column(name = "is_active")
    private Boolean isActive = true;
}

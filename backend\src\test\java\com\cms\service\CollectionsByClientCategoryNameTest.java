package com.cms.service;

import com.cms.entity.Category;
import com.cms.entity.Client;
import com.cms.entity.CollectionListing;
import com.cms.repository.CategoryRepository;
import com.cms.repository.ClientRepository;
import com.cms.repository.CollectionListingRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class CollectionsByClientCategoryNameTest {

    @Autowired
    private CollectionListingService collectionListingService;

    @Autowired
    private CollectionListingRepository collectionListingRepository;

    @Autowired
    private CategoryRepository categoryRepository;

    @Autowired
    private ClientRepository clientRepository;

    private Client testClient1;
    private Client testClient2;
    private Category parentCategory1;
    private Category childCategory1;
    private Category parentCategory2;
    private Category childCategory2;
    private Category standaloneCategory;

    @BeforeEach
    void setUp() {
        // Create test clients
        testClient1 = new Client();
        testClient1.setName("Alabama LLC");
        testClient1 = clientRepository.save(testClient1);

        testClient2 = new Client();
        testClient2.setName("Texas Corp");
        testClient2 = clientRepository.save(testClient2);

        // Create categories for client 1
        parentCategory1 = new Category();
        parentCategory1.setCategoryName("LLC");
        parentCategory1.setClient(testClient1);
        parentCategory1 = categoryRepository.save(parentCategory1);

        childCategory1 = new Category();
        childCategory1.setCategoryName("Default");
        childCategory1.setClient(testClient1);
        childCategory1.setParentCategory(parentCategory1);
        childCategory1 = categoryRepository.save(childCategory1);

        // Create categories for client 2
        parentCategory2 = new Category();
        parentCategory2.setCategoryName("LLC"); // Same name as client 1
        parentCategory2.setClient(testClient2);
        parentCategory2 = categoryRepository.save(parentCategory2);

        childCategory2 = new Category();
        childCategory2.setCategoryName("Premium");
        childCategory2.setClient(testClient2);
        childCategory2.setParentCategory(parentCategory2);
        childCategory2 = categoryRepository.save(childCategory2);

        // Create standalone category for client 1
        standaloneCategory = new Category();
        standaloneCategory.setCategoryName("Services");
        standaloneCategory.setClient(testClient1);
        standaloneCategory = categoryRepository.save(standaloneCategory);
    }

    @Test
    void testGetCollectionsByClientNameAndCategoryName_ParentCategory() {
        // Create collections in parent category
        CollectionListing collection1 = new CollectionListing();
        collection1.setCollectionName("Products");
        collection1.setCollectionApiId("alabama_llc_products");
        collection1.setCategory(parentCategory1);
        collectionListingRepository.save(collection1);

        CollectionListing collection2 = new CollectionListing();
        collection2.setCollectionName("Services");
        collection2.setCollectionApiId("alabama_llc_services");
        collection2.setCategory(parentCategory1);
        collectionListingRepository.save(collection2);

        // Test retrieval by client name and parent category name
        List<CollectionListing> collections = collectionListingService
                .getCollectionsByClientNameAndCategoryNameWithDetails("Alabama LLC", "LLC");

        assertEquals(2, collections.size());
        assertTrue(collections.stream().anyMatch(c -> c.getCollectionName().equals("Products")));
        assertTrue(collections.stream().anyMatch(c -> c.getCollectionName().equals("Services")));
    }

    @Test
    void testGetCollectionsByClientNameAndCategoryName_ChildCategory() {
        // Create collections in child category
        CollectionListing collection1 = new CollectionListing();
        collection1.setCollectionName("Documents");
        collection1.setCollectionApiId("alabama_llc_default_documents");
        collection1.setCategory(childCategory1);
        collectionListingRepository.save(collection1);

        // Test retrieval by client name and child category name
        List<CollectionListing> collections = collectionListingService
                .getCollectionsByClientNameAndCategoryNameWithDetails("Alabama LLC", "Default");

        assertEquals(1, collections.size());
        assertEquals("Documents", collections.get(0).getCollectionName());
    }

    @Test
    void testGetCollectionsByClientNameAndCategoryName_ParentCategoryIncludesChildCollections() {
        // Create collection in parent category
        CollectionListing parentCollection = new CollectionListing();
        parentCollection.setCollectionName("Parent Collection");
        parentCollection.setCollectionApiId("alabama_llc_parent");
        parentCollection.setCategory(parentCategory1);
        collectionListingRepository.save(parentCollection);

        // Create collection in child category
        CollectionListing childCollection = new CollectionListing();
        childCollection.setCollectionName("Child Collection");
        childCollection.setCollectionApiId("alabama_llc_default_child");
        childCollection.setCategory(childCategory1);
        collectionListingRepository.save(childCollection);

        // When searching by parent category name, should find collections in both parent and child
        List<CollectionListing> collections = collectionListingService
                .getCollectionsByClientNameAndCategoryNameWithDetails("Alabama LLC", "LLC");

        assertEquals(2, collections.size());
        assertTrue(collections.stream().anyMatch(c -> c.getCollectionName().equals("Parent Collection")));
        assertTrue(collections.stream().anyMatch(c -> c.getCollectionName().equals("Child Collection")));
    }

    @Test
    void testGetCollectionsByClientNameAndCategoryName_DifferentClients() {
        // Create collections for both clients with same category name
        CollectionListing collection1 = new CollectionListing();
        collection1.setCollectionName("Client1 Collection");
        collection1.setCollectionApiId("alabama_llc_collection");
        collection1.setCategory(parentCategory1); // LLC category for Alabama LLC
        collectionListingRepository.save(collection1);

        CollectionListing collection2 = new CollectionListing();
        collection2.setCollectionName("Client2 Collection");
        collection2.setCollectionApiId("texas_corp_collection");
        collection2.setCategory(parentCategory2); // LLC category for Texas Corp
        collectionListingRepository.save(collection2);

        // Test retrieval for client 1
        List<CollectionListing> collections1 = collectionListingService
                .getCollectionsByClientNameAndCategoryNameWithDetails("Alabama LLC", "LLC");
        assertEquals(1, collections1.size());
        assertEquals("Client1 Collection", collections1.get(0).getCollectionName());

        // Test retrieval for client 2
        List<CollectionListing> collections2 = collectionListingService
                .getCollectionsByClientNameAndCategoryNameWithDetails("Texas Corp", "LLC");
        assertEquals(1, collections2.size());
        assertEquals("Client2 Collection", collections2.get(0).getCollectionName());
    }

    @Test
    void testGetCollectionsByClientNameAndCategoryName_StandaloneCategory() {
        // Create collection in standalone category (no parent)
        CollectionListing collection = new CollectionListing();
        collection.setCollectionName("Service Collection");
        collection.setCollectionApiId("alabama_services");
        collection.setCategory(standaloneCategory);
        collectionListingRepository.save(collection);

        // Test retrieval by standalone category name
        List<CollectionListing> collections = collectionListingService
                .getCollectionsByClientNameAndCategoryNameWithDetails("Alabama LLC", "Services");

        assertEquals(1, collections.size());
        assertEquals("Service Collection", collections.get(0).getCollectionName());
    }

    @Test
    void testGetCollectionsByClientNameAndCategoryName_NoResults() {
        // Test with non-existent client
        List<CollectionListing> collections1 = collectionListingService
                .getCollectionsByClientNameAndCategoryNameWithDetails("Non-existent Client", "LLC");
        assertTrue(collections1.isEmpty());

        // Test with non-existent category
        List<CollectionListing> collections2 = collectionListingService
                .getCollectionsByClientNameAndCategoryNameWithDetails("Alabama LLC", "Non-existent Category");
        assertTrue(collections2.isEmpty());
    }

    @Test
    void testGetCollectionsByClientNameAndCategoryName_NullValidation() {
        // Test null client name
        assertThrows(Exception.class, () -> {
            collectionListingService.getCollectionsByClientNameAndCategoryNameWithDetails(null, "LLC");
        });

        // Test empty client name
        assertThrows(Exception.class, () -> {
            collectionListingService.getCollectionsByClientNameAndCategoryNameWithDetails("", "LLC");
        });

        // Test null category name
        assertThrows(Exception.class, () -> {
            collectionListingService.getCollectionsByClientNameAndCategoryNameWithDetails("Alabama LLC", null);
        });

        // Test empty category name
        assertThrows(Exception.class, () -> {
            collectionListingService.getCollectionsByClientNameAndCategoryNameWithDetails("Alabama LLC", "");
        });
    }
}

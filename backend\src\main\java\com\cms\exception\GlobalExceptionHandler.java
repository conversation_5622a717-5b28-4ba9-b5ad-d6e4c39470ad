package com.cms.exception;

import jakarta.persistence.EntityNotFoundException;
import jakarta.validation.ConstraintViolation;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.exception.ConstraintViolationException;
import org.hibernate.exception.DataException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(CircularReferenceException.class)
    public ResponseEntity<ErrorDetails> handleCircularReferenceException(CircularReferenceException ex, WebRequest request) {
        ErrorDetails errorDetails = new ErrorDetails(
                LocalDateTime.now(),
                "Circular reference detected: " + ex.getMessage(),
                request.getDescription(false),
                HttpStatus.BAD_REQUEST.value());
        return new ResponseEntity<>(errorDetails, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<Map<String, String>> handleResourceNotFoundException(ResourceNotFoundException ex, WebRequest request) {
        log.info("Resource not found: {}", ex.getMessage());
        Map<String, String> response = new HashMap<>();
        response.put("message", ex.getMessage());
        return ResponseEntity.status(HttpStatus.NO_CONTENT).body(response);
    }

    @ExceptionHandler(NoContentException.class)
    public ResponseEntity<Map<String, String>> handleNoContentException(NoContentException ex) {
        log.info("No content found: {}", ex.getMessage());
        Map<String, String> response = new HashMap<>();
        response.put("message", ex.getMessage());
        return ResponseEntity.status(HttpStatus.NO_CONTENT).body(response);
    }

    @ExceptionHandler(UniqueConstraintViolationException.class)
    public ResponseEntity<ErrorDetails> handleUniqueConstraintViolation(
            UniqueConstraintViolationException ex, WebRequest request) {
        Map<String, String> errors = new HashMap<>();
        errors.put(ex.getFieldName(), ex.getMessage());

        // Create a more specific message based on the field name
        String message;
        if ("componentName".equals(ex.getFieldName())) {
            message = "Component name already exists: " + ex.getFieldValue();
        } else if ("collectionName".equals(ex.getFieldName())) {
            message = "Collection name already exists: " + ex.getFieldValue();
        } else if ("categoryName".equals(ex.getFieldName())) {
            message = "Category name already exists: " + ex.getFieldValue();
        } else if ("fieldTypeName".equals(ex.getFieldName())) {
            message = "Field type name already exists: " + ex.getFieldValue();
        } else if ("configTypeName".equals(ex.getFieldName())) {
            message = "Config type name already exists: " + ex.getFieldValue();
        } else if ("configName".equals(ex.getFieldName())) {
            message = "Field config name already exists: " + ex.getFieldValue();
        } else {
            message = ex.getFieldName() + " already exists: " + ex.getFieldValue();
        }

        ValidationErrorDetails errorDetails = new ValidationErrorDetails(
                LocalDateTime.now(),
                message,
                request.getDescription(false),
                701, // Custom status code for unique constraint violation
                errors
        );
        // Use a custom status code 701 for already exists
        return ResponseEntity.status(701).body(errorDetails);
    }

    @ExceptionHandler(NullConstraintViolationException.class)
    public ResponseEntity<ErrorDetails> handleNullConstraintViolation(
            NullConstraintViolationException ex, WebRequest request) {
        Map<String, String> errors = new HashMap<>();
        errors.put(ex.getFieldName(), ex.getMessage());

        ValidationErrorDetails errorDetails = new ValidationErrorDetails(
                LocalDateTime.now(),
                "Null Constraint Violation",
                request.getDescription(false),
                700, // Custom status code for null constraint violation
                errors
        );
        return ResponseEntity.status(700).body(errorDetails);
    }

    @ExceptionHandler(ForeignKeyViolationException.class)
    public ResponseEntity<ErrorDetails> handleForeignKeyViolation(
            ForeignKeyViolationException ex, WebRequest request) {
        Map<String, String> errors = new HashMap<>();
        errors.put(ex.getFieldName(), ex.getMessage());

        // Get the current tenant from the thread local context if available
        String currentTenant = "current tenant";
        try {
            // Try to get the tenant ID from the thread local context
            Object tenantContext = Class.forName("com.cms.tenant.TenantContextHolder")
                    .getMethod("getTenantId")
                    .invoke(null);
            if (tenantContext != null) {
                currentTenant = tenantContext.toString();
            }
        } catch (Exception e) {
            log.debug("Could not get tenant context: {}", e.getMessage());
        }

        // Create a more specific message based on the entity name
        String message;
        if ("CollectionListing".equals(ex.getEntityName())) {
            message = String.format("Collection with ID %s does not exist in tenant '%s'. This collection may exist in another tenant schema.",
                    ex.getFieldValue(), currentTenant);
        } else if ("ComponentListing".equals(ex.getEntityName())) {
            message = String.format("Component with ID %s does not exist in tenant '%s'. This component may exist in another tenant schema.",
                    ex.getFieldValue(), currentTenant);
        } else {
            message = String.format("Foreign Key Violation: %s with %s '%s' does not exist in tenant '%s'. This entity may exist in another tenant schema.",
                    ex.getEntityName(), ex.getFieldName(), ex.getFieldValue(), currentTenant);
        }

        ValidationErrorDetails errorDetails = new ValidationErrorDetails(
                LocalDateTime.now(),
                message,
                request.getDescription(false),
                HttpStatus.BAD_REQUEST.value(),
                errors
        );

        log.warn("Foreign key violation: {}", message);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorDetails);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, WebRequest request) {
        Map<String, String> errors = new HashMap<>();

        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);

            // Log the validation error
            log.debug("Validation error for field '{}': {}", fieldName, errorMessage);
        });

        // Always use status code 700 for validation errors
        ValidationErrorDetails errorDetails = new ValidationErrorDetails(
                LocalDateTime.now(),
                "Validation Failed",
                request.getDescription(false),
                700, // Custom status code for validation errors
                errors
        );

        log.warn("Returning status code 700 for validation errors: {}", errors);
        return ResponseEntity.status(700).body(errorDetails);
    }

    @ExceptionHandler(CustomException.class)
    public ResponseEntity<Object> handleCustomException(CustomException ex) {
        Throwable cause = ex.getCause();
        Map<String, Object> errorDetails = new HashMap<>();

        log.error("CustomException caught: {}", ex.getMessage(), ex);

        if (cause instanceof jakarta.validation.ConstraintViolationException validationEx) {
            Set<ConstraintViolation<?>> violations = validationEx.getConstraintViolations();
            StringBuilder errorMessage = new StringBuilder();

            for (ConstraintViolation<?> violation : violations) {
                errorMessage.append(violation.getPropertyPath())
                        .append(" ")
                        .append(violation.getMessage())
                        .append("; ");
            }
            errorDetails.put("error", errorMessage.toString().trim());

            log.warn("ConstraintViolationException handled with 701: {}", errorMessage);
            return ResponseEntity.status(701).body(errorDetails);
        }
        if (cause instanceof ConstraintViolationException) {
            String cleanMessage = extractDetailMessage(cause);
            errorDetails.put("error", cleanMessage);
            log.warn("ConstraintViolationException handled: {}", cleanMessage);
            if (cleanMessage.toLowerCase().contains("is not present in table")) {
                // Foreign key violation - return 204 instead of 404
                return ResponseEntity.status(HttpStatus.NO_CONTENT).body(errorDetails);
            }
            return ResponseEntity.status(700).body(errorDetails);
        }

        if (cause instanceof DataException) {
            String errorMsg = cause.getLocalizedMessage();
            errorDetails.put("error", errorMsg);
            log.warn("DataException (value too long etc.) handled: {}", errorMsg);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorDetails);
        }

        // DataIntegrityViolationException is now handled by a dedicated handler
        if (cause instanceof DataIntegrityViolationException) {
            throw (DataIntegrityViolationException) cause;
        }

        if (cause instanceof EntityNotFoundException) {
            String errorMsg = cause.getMessage();
            errorDetails.put("message", errorMsg);
            log.warn("EntityNotFoundException handled: {}", errorMsg);
            return ResponseEntity.status(HttpStatus.NO_CONTENT).body(errorDetails);
        }

        String fallbackMessage = cause != null ? cause.getMessage() : ex.getMessage();
        errorDetails.put("error", fallbackMessage);
        log.error("Unhandled exception caught: {}", fallbackMessage, ex);
        return new ResponseEntity<>(errorDetails, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    private String extractDetailMessage(Throwable e) {
        while (e != null) {
            String msg = e.getMessage();
            if (msg != null && msg.contains("Detail:")) {
                int start = msg.indexOf("Detail:");
                int end = msg.indexOf("Call getNextException");
                if (end > start) {
                    return msg.substring(start, end).trim();
                }
                return msg.substring(start).trim();
            }
            e = e.getCause();
        }
        return e != null ? e.getLocalizedMessage() : "Unknown error detail";
    }

    @ExceptionHandler(DataIntegrityViolationException.class)
    public ResponseEntity<Object> handleDataIntegrityViolation(DataIntegrityViolationException ex, WebRequest request) {
        log.warn("DataIntegrityViolationException: {}", ex.getMessage());

        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("timestamp", LocalDateTime.now());
        errorDetails.put("message", ex.getMostSpecificCause().getMessage());

        // Check if it's a unique constraint violation
        String errorMessage = ex.getMessage().toLowerCase();
        if (errorMessage.contains("unique constraint") ||
            errorMessage.contains("duplicate key") ||
            errorMessage.contains("unique index")) {

            log.info("Unique constraint violation detected, returning 701 status code");
            errorDetails.put("error", "A record with the same value already exists");
            return ResponseEntity.status(701).body(errorDetails);
        }

        // For other data integrity violations
        errorDetails.put("error", "Data integrity violation");
        return ResponseEntity.status(HttpStatus.CONFLICT).body(errorDetails);
    }

    @ExceptionHandler(EmptyResultDataAccessException.class)
    public ResponseEntity<Void> handleEmptyResultDataAccessException(EmptyResultDataAccessException ex) {
        log.info("No data found: {}", ex.getMessage());
        return ResponseEntity.noContent().build();
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ErrorDetails> handleMethodArgumentTypeMismatch(
            MethodArgumentTypeMismatchException ex, WebRequest request) {
        String errorMessage = String.format("The parameter '%s' of value '%s' could not be converted to type '%s'",
                ex.getName(), ex.getValue(), ex.getRequiredType().getSimpleName());

        ErrorDetails errorDetails = new ErrorDetails(
                LocalDateTime.now(),
                errorMessage,
                request.getDescription(false),
                HttpStatus.BAD_REQUEST.value()
        );
        return new ResponseEntity<>(errorDetails, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorDetails> handleGlobalException(Exception ex, WebRequest request) {
        log.error("Unhandled exception", ex);
        ErrorDetails errorDetails = new ErrorDetails(
                LocalDateTime.now(),
                ex.getMessage(),
                request.getDescription(false),
                HttpStatus.INTERNAL_SERVER_ERROR.value()
        );
        return new ResponseEntity<>(errorDetails, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}

package com.cms.repository;

import com.cms.entity.ComponentListing;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ComponentRepository extends JpaRepository<ComponentListing, Integer> {
    List<ComponentListing> findByIsActiveTrue();
    Optional<ComponentListing> findByComponentApiId(String apiId);
    boolean existsByComponentName(String componentName);
}

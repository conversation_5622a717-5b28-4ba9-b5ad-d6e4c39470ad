-- Remove parent_component_id and category_id from component_listing table
ALTER TABLE component_listing DROP CONSTRAINT IF EXISTS component_listing_parent_component_id_fkey;
ALTER TABLE component_listing DROP CONSTRAINT IF EXISTS component_listing_category_id_fkey;
ALTER TABLE component_listing DROP COLUMN IF EXISTS parent_component_id;
ALTER TABLE component_listing DROP COLUMN IF EXISTS category_id;

-- Remove category_id from collection_listing table
ALTER TABLE collection_listing DROP CONSTRAINT IF EXISTS collection_listing_category_id_fkey;
ALTER TABLE collection_listing DROP COLUMN IF EXISTS category_id;

-- Drop indexes
DROP INDEX IF EXISTS idx_component_listing_category_id;
DROP INDEX IF EXISTS idx_component_listing_parent_component_id;
DROP INDEX IF EXISTS idx_collection_listing_category_id;

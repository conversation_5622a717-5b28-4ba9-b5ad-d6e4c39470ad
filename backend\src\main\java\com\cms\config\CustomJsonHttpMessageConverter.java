package com.cms.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
public class CustomJsonHttpMessageConverter extends MappingJackson2HttpMessageConverter {

    public CustomJsonHttpMessageConverter(ObjectMapper objectMapper) {
        super(objectMapper);
        setSupportedMediaTypes(createSupportedMediaTypes());
    }

    private static List<MediaType> createSupportedMediaTypes() {
        List<MediaType> supportedMediaTypes = new ArrayList<>();
        supportedMediaTypes.add(MediaType.APPLICATION_JSON);
        supportedMediaTypes.add(new MediaType("application", "json", StandardCharsets.UTF_8));
        supportedMediaTypes.add(MediaType.valueOf("application/json;charset=UTF-8"));
        supportedMediaTypes.add(MediaType.valueOf("application/json; charset=UTF-8"));
        supportedMediaTypes.add(MediaType.valueOf("application/json;charset=utf-8"));
        supportedMediaTypes.add(MediaType.valueOf("application/json; charset=utf-8"));
        supportedMediaTypes.add(MediaType.valueOf("*/*"));
        return supportedMediaTypes;
    }
}

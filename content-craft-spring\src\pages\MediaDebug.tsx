import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import axios from 'axios';

interface MediaAsset {
  id: number;
  fileName: string;
  originalFileName: string;
  fileType: string;
  fileSize: number;
  publicUrl: string;
  uploadedByUsername?: string;
  createdAt: string;
}

export default function MediaDebug() {
  const [assets, setAssets] = useState<MediaAsset[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedAsset, setSelectedAsset] = useState<MediaAsset | null>(null);
  const [customUrl, setCustomUrl] = useState('');
  const [imageStatus, setImageStatus] = useState<Record<string, boolean>>({});

  useEffect(() => {
    const fetchAssets = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/api/media/assets');
        console.log('API Response:', response.data);
        setAssets(response.data || []);
        setError(null);
      } catch (err) {
        console.error('Error fetching assets:', err);
        setError('Failed to load media assets');
      } finally {
        setLoading(false);
      }
    };

    fetchAssets();
  }, []);

  const handleImageLoad = (id: string) => {
    setImageStatus(prev => ({ ...prev, [id]: true }));
  };

  const handleImageError = (id: string) => {
    setImageStatus(prev => ({ ...prev, [id]: false }));
  };

  const testUrlFormats = (asset: MediaAsset) => {
    setSelectedAsset(asset);
    setImageStatus({});
  };

  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-6">Media Debug Tool</h1>
      
      <div className="mb-8">
        <p className="text-muted-foreground mb-4">
          This tool helps diagnose image loading issues by testing different URL formats.
        </p>
        <Button onClick={() => window.open('/image-test.html', '_blank')}>
          Open Advanced Image Test Tool
        </Button>
      </div>
      
      {loading ? (
        <div className="flex justify-center items-center p-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      ) : error ? (
        <div className="p-8 space-y-4 border border-destructive/50 rounded-lg bg-destructive/10">
          <h2 className="text-xl font-bold text-destructive">Error</h2>
          <p>{error}</p>
          <Button onClick={() => window.location.reload()} variant="outline">
            Reload Page
          </Button>
        </div>
      ) : (
        <div className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Select an Image to Test</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {assets
                  .filter(asset => asset.fileType.startsWith('image/'))
                  .map(asset => (
                    <div 
                      key={asset.id} 
                      className={`border rounded-md p-4 cursor-pointer ${selectedAsset?.id === asset.id ? 'border-primary bg-primary/5' : ''}`}
                      onClick={() => testUrlFormats(asset)}
                    >
                      <div className="flex items-start">
                        <div className="flex-1">
                          <h3 className="font-medium truncate">{asset.originalFileName}</h3>
                          <p className="text-xs text-muted-foreground">{asset.fileType}</p>
                          <p className="text-xs mt-1">ID: {asset.id}</p>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
              
              {assets.filter(asset => asset.fileType.startsWith('image/')).length === 0 && (
                <div className="text-center p-12 border border-dashed rounded-lg">
                  <p className="text-muted-foreground">No image assets found</p>
                </div>
              )}
            </CardContent>
          </Card>
          
          {selectedAsset && (
            <Card>
              <CardHeader>
                <CardTitle>Testing URL Formats for: {selectedAsset.originalFileName}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-8">
                  {/* Format 1: Direct Public URL */}
                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">1. Direct Public URL</h3>
                    <div className="bg-muted/30 p-2 rounded text-xs font-mono break-all">
                      {selectedAsset.publicUrl}
                    </div>
                    <div className="bg-black/5 p-4 rounded-md flex justify-center">
                      <img 
                        src={selectedAsset.publicUrl} 
                        alt="Format 1"
                        className="max-h-48 object-contain"
                        onLoad={() => handleImageLoad('format1')}
                        onError={() => handleImageError('format1')}
                      />
                    </div>
                    <div className={`text-sm ${imageStatus['format1'] ? 'text-green-600' : 'text-red-600'}`}>
                      {imageStatus['format1'] === undefined ? 'Loading...' : 
                       imageStatus['format1'] ? '✓ Image loaded successfully' : '✗ Failed to load image'}
                    </div>
                  </div>
                  
                  {/* Format 2: API Content Endpoint */}
                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">2. API Content Endpoint</h3>
                    <div className="bg-muted/30 p-2 rounded text-xs font-mono break-all">
                      {`/api/media/assets/${selectedAsset.id}/content`}
                    </div>
                    <div className="bg-black/5 p-4 rounded-md flex justify-center">
                      <img 
                        src={`/api/media/assets/${selectedAsset.id}/content`} 
                        alt="Format 2"
                        className="max-h-48 object-contain"
                        onLoad={() => handleImageLoad('format2')}
                        onError={() => handleImageError('format2')}
                      />
                    </div>
                    <div className={`text-sm ${imageStatus['format2'] ? 'text-green-600' : 'text-red-600'}`}>
                      {imageStatus['format2'] === undefined ? 'Loading...' : 
                       imageStatus['format2'] ? '✓ Image loaded successfully' : '✗ Failed to load image'}
                    </div>
                  </div>
                  
                  {/* Format 3: Uploads Directory */}
                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">3. Uploads Directory</h3>
                    <div className="bg-muted/30 p-2 rounded text-xs font-mono break-all">
                      {`/uploads/${selectedAsset.fileName}`}
                    </div>
                    <div className="bg-black/5 p-4 rounded-md flex justify-center">
                      <img 
                        src={`/uploads/${selectedAsset.fileName}`} 
                        alt="Format 3"
                        className="max-h-48 object-contain"
                        onLoad={() => handleImageLoad('format3')}
                        onError={() => handleImageError('format3')}
                      />
                    </div>
                    <div className={`text-sm ${imageStatus['format3'] ? 'text-green-600' : 'text-red-600'}`}>
                      {imageStatus['format3'] === undefined ? 'Loading...' : 
                       imageStatus['format3'] ? '✓ Image loaded successfully' : '✗ Failed to load image'}
                    </div>
                  </div>
                  
                  {/* Format 4: Custom URL */}
                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">4. Custom URL</h3>
                    <div className="flex space-x-2">
                      <Input 
                        value={customUrl} 
                        onChange={(e) => setCustomUrl(e.target.value)}
                        placeholder="Enter a custom URL format (use {id} and {fileName} as placeholders)"
                      />
                      <Button 
                        onClick={() => {
                          const url = customUrl
                            .replace('{id}', selectedAsset.id.toString())
                            .replace('{fileName}', selectedAsset.fileName);
                          setCustomUrl(url);
                        }}
                      >
                        Test
                      </Button>
                    </div>
                    {customUrl && (
                      <>
                        <div className="bg-muted/30 p-2 rounded text-xs font-mono break-all">
                          {customUrl}
                        </div>
                        <div className="bg-black/5 p-4 rounded-md flex justify-center">
                          <img 
                            src={customUrl} 
                            alt="Format 4"
                            className="max-h-48 object-contain"
                            onLoad={() => handleImageLoad('format4')}
                            onError={() => handleImageError('format4')}
                          />
                        </div>
                        <div className={`text-sm ${imageStatus['format4'] ? 'text-green-600' : 'text-red-600'}`}>
                          {imageStatus['format4'] === undefined ? 'Loading...' : 
                          imageStatus['format4'] ? '✓ Image loaded successfully' : '✗ Failed to load image'}
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
}

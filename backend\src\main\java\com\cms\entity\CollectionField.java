package com.cms.entity;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIdentityReference;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "collection_fields")
@Getter
@Setter
@ToString(exclude = {"collection", "dependentOn"})
@NoArgsConstructor
@AllArgsConstructor
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id", scope = CollectionField.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CollectionField extends Auditable {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "cms_colleciton_field_seq")
    @SequenceGenerator(name = "cms_colleciton_field_seq", sequenceName = "cms_colleciton_field_seq", initialValue = 100, allocationSize = 1)
    private Integer id;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "collection_id")
    private CollectionListing collection;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "field_type_id")
    private FieldType fieldType;

    @Column(name = "display_preference")
    private Integer displayPreference;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dependent_on")
    private CollectionField dependentOn;

    @Column(name = "additional_information", columnDefinition = "TEXT")
    private String additionalInformation;

    @OneToMany(mappedBy = "collectionField", cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JsonIdentityReference(alwaysAsId = true)
    private List<CollectionFieldConfig> configs = new ArrayList<>();
}

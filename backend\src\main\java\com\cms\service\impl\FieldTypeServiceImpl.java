package com.cms.service.impl;

import com.cms.entity.FieldType;
import com.cms.exception.NoContentException;
import com.cms.exception.NullConstraintViolationException;
import com.cms.exception.ResourceNotFoundException;
import com.cms.exception.UniqueConstraintViolationException;
import com.cms.repository.FieldTypeRepository;
import com.cms.service.FieldTypeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class FieldTypeServiceImpl implements FieldTypeService {

    private final FieldTypeRepository fieldTypeRepository;

    @Override
    public List<FieldType> getAllFieldTypes() {
        List<FieldType> fieldTypes = fieldTypeRepository.findAll();
        log.debug("Retrieved {} field types", fieldTypes.size());
        return fieldTypes;
    }

    @Override
    public List<FieldType> getActiveFieldTypes() {
        List<FieldType> activeFieldTypes = fieldTypeRepository.findByIsActiveTrue();
        log.debug("Retrieved {} active field types", activeFieldTypes.size());
        return activeFieldTypes;
    }

    @Override
    public Optional<FieldType> getFieldTypeById(Integer id) {
        return fieldTypeRepository.findById(id);
    }

    @Override
    @Transactional
    public FieldType createFieldType(FieldType fieldType) {
        // Validate required fields
        if (fieldType.getFieldTypeName() == null || fieldType.getFieldTypeName().trim().isEmpty()) {
            throw new NullConstraintViolationException("fieldTypeName");
        }

        // Check for unique constraint violations
        if (existsByFieldTypeName(fieldType.getFieldTypeName())) {
            throw new UniqueConstraintViolationException("fieldTypeName", fieldType.getFieldTypeName());
        }

        // Set default values if not provided
        if (fieldType.getIsActive() == null) {
            fieldType.setIsActive(true);
        }

        // Save the field type
        FieldType savedFieldType = fieldTypeRepository.save(fieldType);
        log.info("Created new field type with ID: {}", savedFieldType.getId());
        return savedFieldType;
    }

    @Override
    @Transactional
    public FieldType updateFieldType(Integer id, FieldType fieldType) {
        // Verify the field type exists
        FieldType existingFieldType = fieldTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("FieldType not found with id: " + id));

        // Validate required fields
        if (fieldType.getFieldTypeName() == null || fieldType.getFieldTypeName().trim().isEmpty()) {
            throw new NullConstraintViolationException("fieldTypeName");
        }

        // Check for unique constraint violations (only if name has changed)
        if (!fieldType.getFieldTypeName().equals(existingFieldType.getFieldTypeName()) &&
                existsByFieldTypeName(fieldType.getFieldTypeName())) {
            throw new UniqueConstraintViolationException("fieldTypeName", fieldType.getFieldTypeName());
        }

        // Update fields
        existingFieldType.setFieldTypeName(fieldType.getFieldTypeName());

        if (fieldType.getFieldTypeDesc() != null) {
            existingFieldType.setFieldTypeDesc(fieldType.getFieldTypeDesc());
        }

        if (fieldType.getDisplayName() != null) {
            existingFieldType.setDisplayName(fieldType.getDisplayName());
        }

        if (fieldType.getHelpText() != null) {
            existingFieldType.setHelpText(fieldType.getHelpText());
        }

        if (fieldType.getLogoImagePath() != null) {
            existingFieldType.setLogoImagePath(fieldType.getLogoImagePath());
        }

        if (fieldType.getIsActive() != null) {
            existingFieldType.setIsActive(fieldType.getIsActive());
        }

        // Save the updated field type
        FieldType updatedFieldType = fieldTypeRepository.save(existingFieldType);
        log.info("Updated field type with ID: {}", updatedFieldType.getId());
        return updatedFieldType;
    }

    @Override
    @Transactional
    public void deleteFieldType(Integer id) {
        // Verify the field type exists
        FieldType fieldType = fieldTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("FieldType not found with id: " + id));

        // Soft delete by setting isActive to false
        fieldType.setIsActive(false);
        fieldTypeRepository.save(fieldType);

        log.info("Soft deleted field type with ID: {}", id);
    }

    @Override
    public boolean existsByFieldTypeName(String fieldTypeName) {
        return fieldTypeRepository.existsByFieldTypeName(fieldTypeName);
    }

    @Override
    public boolean existsById(Integer id) {
        return fieldTypeRepository.existsById(id);
    }
}

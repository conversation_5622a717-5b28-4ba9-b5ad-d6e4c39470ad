package com.cms.controller;

import com.cms.dto.MediaDTO;
import com.cms.entity.Media;
import com.cms.service.MediaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.nio.file.Path;
import java.util.List;

@RestController
@RequestMapping("/media")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Media", description = "Media API")
public class MediaController {

    private final MediaService mediaService;
    private final Path fileStorageLocation;

    @PostMapping("/upload")
    @Operation(summary = "Upload a file", description = "Upload a file to the media library")
    public ResponseEntity<MediaDTO> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "folderId", required = false) Integer folderId,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "altText", required = false) String altText,
            @RequestParam(value = "isPublic", required = false, defaultValue = "false") Boolean isPublic) {

        MediaDTO mediaDTO = mediaService.storeFile(file, folderId, description, altText, isPublic);

        return ResponseEntity.ok(mediaDTO);
    }

    @GetMapping("/assets/getAll")
    @Operation(summary = "Get all media", description = "Get all media with pagination")
    public ResponseEntity<Page<MediaDTO>> getAllMedia(
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "20") int size,
            @RequestParam(value = "sort", defaultValue = "createdAt") String sort,
            @RequestParam(value = "direction", defaultValue = "DESC") String direction) {

        Sort.Direction sortDirection = Sort.Direction.fromString(direction);
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

        Page<MediaDTO> media = mediaService.getAllMedia(pageable);

        return ResponseEntity.ok(media);
    }

    @GetMapping("/assets/getById/{id}")
    @Operation(summary = "Get media by ID", description = "Get a media file by its ID")
    public ResponseEntity<MediaDTO> getMediaById(@PathVariable Integer id) {
        MediaDTO mediaDTO = mediaService.getMediaById(id);

        return ResponseEntity.ok(mediaDTO);
    }

    @GetMapping("/assets/getByFolderId/{folderId}")
    @Operation(summary = "Get media by folder ID", description = "Get all media in a folder")
    public ResponseEntity<List<MediaDTO>> getMediaByFolder(@PathVariable Integer folderId) {
        List<MediaDTO> media = mediaService.getMediaByFolder(folderId);

        return ResponseEntity.ok(media);
    }

    @GetMapping("/assets/search")
    @Operation(summary = "Search media", description = "Search media by file name")
    public ResponseEntity<Page<MediaDTO>> searchMedia(
            @RequestParam("query") String query,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "20") int size) {

        Pageable pageable = PageRequest.of(page, size);

        Page<MediaDTO> media = mediaService.searchMedia(query, pageable);

        return ResponseEntity.ok(media);
    }

    @PutMapping("/assets/update/{id}")
    @Operation(summary = "Update media", description = "Update media metadata")
    public ResponseEntity<MediaDTO> updateMedia(
            @PathVariable Integer id,
            @RequestParam(value = "fileName", required = false) String fileName,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "altText", required = false) String altText,
            @RequestParam(value = "isPublic", required = false) Boolean isPublic,
            @RequestParam(value = "folderId", required = false) Integer folderId) {

        MediaDTO mediaDTO = mediaService.updateMedia(id, fileName, description, altText, isPublic, folderId);

        return ResponseEntity.ok(mediaDTO);
    }

    @GetMapping("/assets/{id}/content")
    @Operation(summary = "Get media content by ID", description = "Get a media file content by its ID with inline disposition")
    public void getMediaContentById(@PathVariable Integer id, HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            // Get media by ID
            MediaDTO mediaDTO = mediaService.getMediaById(id);

            // Extract path components from publicUrl
            String publicUrl = mediaDTO.getPublicUrl();
            String[] pathParts = publicUrl.split("/files/");
            if (pathParts.length < 2) {
                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Invalid file path in public URL: " + publicUrl);
                return;
            }

            String filePath = pathParts[1]; // This should be year/month/filename
            Path path = fileStorageLocation.resolve(filePath);

            // Log the file path for debugging
            log.debug("Serving file content by ID at path: {}", path.toAbsolutePath());

            java.io.File file = path.toFile();

            if (!file.exists()) {
                log.error("File not found for content by ID: {}", path.toAbsolutePath());
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "File not found: " + filePath);
                return;
            }

            // Try to determine file's content type
            String contentType = request.getServletContext().getMimeType(file.getAbsolutePath());

            // Fallback to the default content type if type could not be determined
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            // Set response headers
            response.setContentType(contentType);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + mediaDTO.getOriginalFileName() + "\"");

            // Add ORB prevention headers
            response.setHeader("Cross-Origin-Resource-Policy", "cross-origin");
            response.setHeader("Cross-Origin-Embedder-Policy", "unsafe-none");
            response.setHeader("Cross-Origin-Opener-Policy", "unsafe-none");

            // Add cache control headers
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");

            // Copy the file to the response output stream
            try (java.io.InputStream in = new java.io.FileInputStream(file);
                 java.io.OutputStream out = response.getOutputStream()) {

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
            }
        } catch (Exception e) {
            log.error("Error serving file content by ID: {}", e.getMessage(), e);
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Error serving file content: " + e.getMessage());
        }
    }

    @GetMapping("/assets/{id}/download")
    @Operation(summary = "Download media by ID", description = "Download a media file by its ID with attachment disposition")
    public void downloadMediaById(@PathVariable Integer id, HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            // Get media by ID
            MediaDTO mediaDTO = mediaService.getMediaById(id);

            // Extract path components from publicUrl
            String publicUrl = mediaDTO.getPublicUrl();
            String[] pathParts = publicUrl.split("/files/");
            if (pathParts.length < 2) {
                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Invalid file path in public URL: " + publicUrl);
                return;
            }

            String filePath = pathParts[1]; // This should be year/month/filename
            Path path = fileStorageLocation.resolve(filePath);

            // Log the file path for debugging
            log.debug("Downloading file by ID at path: {}", path.toAbsolutePath());

            java.io.File file = path.toFile();

            if (!file.exists()) {
                log.error("File not found for download by ID: {}", path.toAbsolutePath());
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "File not found: " + filePath);
                return;
            }

            // Try to determine file's content type
            String contentType = request.getServletContext().getMimeType(file.getAbsolutePath());

            // Fallback to the default content type if type could not be determined
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            // Set response headers
            response.setContentType(contentType);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + mediaDTO.getOriginalFileName() + "\"");

            // Add ORB prevention headers
            response.setHeader("Cross-Origin-Resource-Policy", "cross-origin");
            response.setHeader("Cross-Origin-Embedder-Policy", "unsafe-none");
            response.setHeader("Cross-Origin-Opener-Policy", "unsafe-none");

            // Add cache control headers
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");

            // Copy the file to the response output stream
            try (java.io.InputStream in = new java.io.FileInputStream(file);
                 java.io.OutputStream out = response.getOutputStream()) {

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
            }
        } catch (Exception e) {
            log.error("Error serving download file by ID: {}", e.getMessage(), e);
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Error serving download file: " + e.getMessage());
        }
    }

    @DeleteMapping("/assets/deleteById/{id}")
    @Operation(summary = "Delete media", description = "Delete a media file")
    public ResponseEntity<Void> deleteMedia(@PathVariable Integer id) {
        mediaService.deleteMedia(id);

        return ResponseEntity.noContent().build();
    }

    @PostMapping("/assets/replace/{id}")
    @Operation(summary = "Replace media file", description = "Replace a media file while keeping the same URL")
    public ResponseEntity<MediaDTO> replaceFile(
            @PathVariable Integer id,
            @RequestParam("file") MultipartFile file) {

        MediaDTO mediaDTO = mediaService.replaceFile(id, file);

        return ResponseEntity.ok(mediaDTO);
    }

    @PostMapping("/assets/share/{id}")
    @Operation(summary = "Generate share link", description = "Generate a shareable link for a media file")
    public ResponseEntity<String> generateShareLink(@PathVariable Integer id) {
        String shareUrl = mediaService.generateShareToken(id);

        return ResponseEntity.ok(shareUrl);
    }

    @GetMapping("/share/{token}")
    @Operation(summary = "Access shared media", description = "Access a media file using a share token")
    public void getSharedMedia(
            @PathVariable String token,
            HttpServletRequest request,
            HttpServletResponse response) throws IOException {

        try {
            // Get media by share token
            Media media = mediaService.getMediaByShareToken(token);

            // Load file as Resource
            Path filePath = fileStorageLocation.resolve(media.getFilePath());

            // Log the file path for debugging
            log.debug("Accessing shared file at path: {}", filePath.toAbsolutePath());

            java.io.File file = filePath.toFile();

            if (!file.exists()) {
                log.error("Shared file not found: {}", filePath.toAbsolutePath());
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "File not found: " + media.getFilePath());
                return;
            }

            // Try to determine file's content type
            String contentType = request.getServletContext().getMimeType(file.getAbsolutePath());

            // Fallback to the default content type if type could not be determined
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            // Set response headers
            response.setContentType(contentType);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + media.getOriginalFileName() + "\"");

            // Add ORB prevention headers
            response.setHeader("Cross-Origin-Resource-Policy", "cross-origin");
            response.setHeader("Cross-Origin-Embedder-Policy", "unsafe-none");
            response.setHeader("Cross-Origin-Opener-Policy", "unsafe-none");

            // Add cache control headers
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");

            // Copy the file to the response output stream
            try (java.io.InputStream in = new java.io.FileInputStream(file);
                 java.io.OutputStream out = response.getOutputStream()) {

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
            }
        } catch (Exception e) {
            log.error("Error serving shared file: {}", e.getMessage(), e);
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Error serving shared file: " + e.getMessage());
        }
    }

    @GetMapping("/download/{year}/{month}/{fileName:.+}")
    @Operation(summary = "Download media file", description = "Download a media file by its path with attachment disposition")
    public void downloadMediaFile(
            @PathVariable String year,
            @PathVariable String month,
            @PathVariable String fileName,
            HttpServletRequest request,
            HttpServletResponse response) throws IOException {

        try {
            // Construct file path
            String filePath = year + "/" + month + "/" + fileName;
            Path path = fileStorageLocation.resolve(filePath);

            // Log the file path for debugging
            log.debug("Downloading file at path: {}", path.toAbsolutePath());

            java.io.File file = path.toFile();

            if (!file.exists()) {
                log.error("File not found for download: {}", path.toAbsolutePath());
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "File not found: " + filePath);
                return;
            }

            // Try to determine file's content type
            String contentType = request.getServletContext().getMimeType(file.getAbsolutePath());

            // Fallback to the default content type if type could not be determined
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            // Set response headers
            response.setContentType(contentType);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"");

            // Add ORB prevention headers
            response.setHeader("Cross-Origin-Resource-Policy", "cross-origin");
            response.setHeader("Cross-Origin-Embedder-Policy", "unsafe-none");
            response.setHeader("Cross-Origin-Opener-Policy", "unsafe-none");

            // Add cache control headers
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");

            // Copy the file to the response output stream
            try (java.io.InputStream in = new java.io.FileInputStream(file);
                 java.io.OutputStream out = response.getOutputStream()) {

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
            }
        } catch (Exception e) {
            log.error("Error serving download file: {}", e.getMessage(), e);
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Error serving download file: " + e.getMessage());
        }
    }

    @GetMapping("/files/{year}/{month}/{fileName:.+}")
    @Operation(summary = "Access media file", description = "Access a media file by its path")
    public void getMediaFile(
            @PathVariable String year,
            @PathVariable String month,
            @PathVariable String fileName,
            HttpServletRequest request,
            HttpServletResponse response) throws IOException {

        try {
            // Construct file path
            String filePath = year + "/" + month + "/" + fileName;
            Path path = fileStorageLocation.resolve(filePath);

            // Log the file path for debugging
            log.debug("Accessing file at path: {}", path.toAbsolutePath());

            java.io.File file = path.toFile();

            if (!file.exists()) {
                log.error("File not found: {}", path.toAbsolutePath());
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "File not found: " + filePath);
                return;
            }

            // Try to determine file's content type
            String contentType = request.getServletContext().getMimeType(file.getAbsolutePath());

            // Fallback to the default content type if type could not be determined
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            // Set response headers
            response.setContentType(contentType);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + fileName + "\"");

            // Add ORB prevention headers
            response.setHeader("Cross-Origin-Resource-Policy", "cross-origin");
            response.setHeader("Cross-Origin-Embedder-Policy", "unsafe-none");
            response.setHeader("Cross-Origin-Opener-Policy", "unsafe-none");

            // Add cache control headers
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");

            // Copy the file to the response output stream
            try (java.io.InputStream in = new java.io.FileInputStream(file);
                 java.io.OutputStream out = response.getOutputStream()) {

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
            }
        } catch (Exception e) {
            log.error("Error serving file: {}", e.getMessage(), e);
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Error serving file: " + e.getMessage());
        }
    }
}

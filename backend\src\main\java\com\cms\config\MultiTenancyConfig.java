package com.cms.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

@Configuration
public class MultiTenancyConfig {

    @Autowired
    private DataSource dataSource;

    @Bean(name = "currentTenantIdentifierResolver")
    public CurrentTenantIdentifierResolverImpl currentTenantIdentifierResolver() {
        return new CurrentTenantIdentifierResolverImpl();
    }

    @Bean(name = "multiTenantConnectionProvider")
    public MultiTenantConnectionProviderImpl multiTenantConnectionProvider() {
        MultiTenantConnectionProviderImpl connectionProvider = new MultiTenantConnectionProviderImpl();
        connectionProvider.setDataSource(dataSource);
        return connectionProvider;
    }
}

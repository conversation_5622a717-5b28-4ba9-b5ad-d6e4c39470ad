package com.cms.service.impl;

import com.cms.entity.ComponentFieldConfig;
import com.cms.exception.ForeignKeyViolationException;
import com.cms.exception.ResourceNotFoundException;
import com.cms.repository.ComponentFieldConfigRepository;
import com.cms.repository.ComponentFieldRepository;
import com.cms.repository.FieldConfigRepository;
import com.cms.service.ComponentFieldConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class ComponentFieldConfigServiceImpl implements ComponentFieldConfigService {

    private final ComponentFieldConfigRepository componentFieldConfigRepository;

    @Override
    public List<ComponentFieldConfig> getAllComponentFieldConfigs() {
        return componentFieldConfigRepository.findAll();
    }

    @Override
    public List<ComponentFieldConfig> getComponentFieldConfigsByComponentFieldId(Integer componentFieldId) {
        return componentFieldConfigRepository.findByComponentFieldId(componentFieldId);
    }

    @Override
    public Optional<ComponentFieldConfig> getComponentFieldConfigById(Integer id) {
        return componentFieldConfigRepository.findById(id);
    }

    @Override
    public ComponentFieldConfig createComponentFieldConfig(ComponentFieldConfig componentFieldConfig) {
        // Create a new config object to avoid transient entity issues
        ComponentFieldConfig newConfig = new ComponentFieldConfig();

        // Set the component field by loading it from the repository
        if (componentFieldConfig.getComponentField() != null && componentFieldConfig.getComponentField().getId() != null) {
            // Verify component field exists and throw ForeignKeyViolationException if not
            newConfig.setComponentField(componentFieldRepository.findById(componentFieldConfig.getComponentField().getId())
                .orElseThrow(() -> new ForeignKeyViolationException("ComponentField", "id",
                    componentFieldConfig.getComponentField().getId())));
        } else {
            throw new ForeignKeyViolationException("ComponentField", "id", null);
        }

        // Handle the field config
        if (componentFieldConfig.getFieldConfig() != null) {
            // If the field config has an ID, try to load it from the repository
            if (componentFieldConfig.getFieldConfig().getId() != null) {
                // Verify field config exists and throw ForeignKeyViolationException if not
                newConfig.setFieldConfig(fieldConfigRepository.findById(componentFieldConfig.getFieldConfig().getId())
                    .orElseThrow(() -> new ForeignKeyViolationException("FieldConfig", "id",
                        componentFieldConfig.getFieldConfig().getId())));
            }
            // If the field config doesn't have an ID but has a name, look it up by name
            else if (componentFieldConfig.getFieldConfig().getConfigName() != null) {
                String configName = componentFieldConfig.getFieldConfig().getConfigName();
                System.out.println("Looking for field config with name: " + configName);

                // Try to find it using the repository
                List<com.cms.entity.FieldConfig> existingConfigs = fieldConfigRepository.findByConfigName(configName);

                if (!existingConfigs.isEmpty()) {
                    newConfig.setFieldConfig(existingConfigs.get(0));
                    System.out.println("Found existing field config with ID: " + existingConfigs.get(0).getId() + ", name: " + existingConfigs.get(0).getConfigName());
                } else {
                    System.err.println("No field config found with name: " + configName);
                }
            }
        }

        // Copy the value
        newConfig.setFieldConfigValue(componentFieldConfig.getFieldConfigValue());

        // Save the new config
        return componentFieldConfigRepository.save(newConfig);
    }

    @Autowired
    private FieldConfigRepository fieldConfigRepository;

    @Autowired
    private ComponentFieldRepository componentFieldRepository;

    @Override
    public List<ComponentFieldConfig> createComponentFieldConfigs(List<ComponentFieldConfig> componentFieldConfigs) {
        List<ComponentFieldConfig> savedConfigs = new ArrayList<>();

        for (ComponentFieldConfig config : componentFieldConfigs) {
            try {
                // Create a new config object to avoid transient entity issues
                ComponentFieldConfig newConfig = new ComponentFieldConfig();

                // Set the component field by loading it from the repository
                if (config.getComponentField() != null && config.getComponentField().getId() != null) {
                    // Verify component field exists and throw ForeignKeyViolationException if not
                    newConfig.setComponentField(componentFieldRepository.findById(config.getComponentField().getId())
                        .orElseThrow(() -> new ForeignKeyViolationException("ComponentField", "id",
                            config.getComponentField().getId())));
                } else {
                    throw new ForeignKeyViolationException("ComponentField", "id", null);
                }

                // Handle the field config
                if (config.getFieldConfig() != null) {
                    // If the field config has an ID, try to load it from the repository
                    if (config.getFieldConfig().getId() != null) {
                        // Verify field config exists and throw ForeignKeyViolationException if not
                        newConfig.setFieldConfig(fieldConfigRepository.findById(config.getFieldConfig().getId())
                            .orElseThrow(() -> new ForeignKeyViolationException("FieldConfig", "id",
                                config.getFieldConfig().getId())));
                    }
                    // If the field config doesn't have an ID but has a name, look it up by name
                    else if (config.getFieldConfig().getConfigName() != null) {
                        String configName = config.getFieldConfig().getConfigName();
                        System.out.println("Looking for field config with name: " + configName);

                        // Try to find it using the repository
                        List<com.cms.entity.FieldConfig> existingConfigs = fieldConfigRepository.findByConfigName(configName);

                        if (!existingConfigs.isEmpty()) {
                            newConfig.setFieldConfig(existingConfigs.get(0));
                            System.out.println("Found existing field config with ID: " + existingConfigs.get(0).getId() + ", name: " + existingConfigs.get(0).getConfigName());
                        } else {
                            System.err.println("No field config found with name: " + configName);
                        }
                    }
                }

                // Copy the value
                newConfig.setFieldConfigValue(config.getFieldConfigValue());

                // Save the new config
                savedConfigs.add(componentFieldConfigRepository.save(newConfig));
            } catch (ForeignKeyViolationException e) {
                // Rethrow ForeignKeyViolationException to be handled by the controller
                throw e;
            } catch (Exception e) {
                // Log the error and continue with the next config
                System.err.println("Error saving field config: " + e.getMessage());
                e.printStackTrace();
            }
        }

        return savedConfigs;
    }

    @Override
    public ComponentFieldConfig updateComponentFieldConfig(Integer id, ComponentFieldConfig componentFieldConfig) {
        ComponentFieldConfig existingConfig = componentFieldConfigRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("ComponentFieldConfig not found with id: " + id));

        existingConfig.setComponentField(componentFieldConfig.getComponentField());
        existingConfig.setFieldConfig(componentFieldConfig.getFieldConfig());
        existingConfig.setFieldConfigValue(componentFieldConfig.getFieldConfigValue());

        return componentFieldConfigRepository.save(existingConfig);
    }

    @Override
    public void deleteComponentFieldConfig(Integer id) {
        ComponentFieldConfig config = componentFieldConfigRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("ComponentFieldConfig not found with id: " + id));
        componentFieldConfigRepository.delete(config);
    }
}

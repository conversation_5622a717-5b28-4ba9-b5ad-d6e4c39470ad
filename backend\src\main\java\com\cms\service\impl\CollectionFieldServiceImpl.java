package com.cms.service.impl;

import com.cms.entity.CollectionField;
import com.cms.entity.CollectionFieldConfig;
import com.cms.entity.CollectionListing;
import com.cms.entity.FieldType;
import com.cms.exception.ForeignKeyViolationException;
import com.cms.exception.NoContentException;
import com.cms.exception.NullConstraintViolationException;
import com.cms.exception.ResourceNotFoundException;
import com.cms.repository.CollectionFieldRepository;
import com.cms.repository.CollectionListingRepository;
import com.cms.repository.FieldTypeRepository;
import com.cms.service.CollectionFieldConfigService;
import com.cms.service.CollectionFieldService;
import com.cms.service.CollectionOrderingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
@Slf4j
public class CollectionFieldServiceImpl implements CollectionFieldService {

    @Autowired
    private CollectionFieldRepository collectionFieldRepository;

    @Autowired
    private CollectionListingRepository collectionListingRepository;

    @Autowired
    private CollectionFieldConfigService collectionFieldConfigService;

    @Autowired
    private FieldTypeRepository fieldTypeRepository;

    @Autowired
    private CollectionOrderingService collectionOrderingService;

    @Override
    public CollectionField getCollectionFieldById(Integer id) {
        return collectionFieldRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("CollectionField not found with id: " + id));
    }

    @Override
    public List<CollectionField> getAllCollectionFields() {
        List<CollectionField> fields = collectionFieldRepository.findAll();
        log.debug("Retrieved {} collection fields", fields.size());
        return fields;
    }

    @Override
    public List<CollectionField> getCollectionFieldsByCollectionId(Integer collectionId) {
        // Verify collection exists
        if (!collectionListingRepository.existsById(collectionId)) {
            throw new ResourceNotFoundException("Collection not found with id: " + collectionId);
        }

        List<CollectionField> fields = collectionFieldRepository.findByCollectionId(collectionId);
        log.debug("Retrieved {} fields for collection ID: {}", fields.size(), collectionId);
        return fields;
    }

    @Override
    @Transactional
    public CollectionField createCollectionField(CollectionField collectionField) {
        // Validate field type
        if (collectionField.getFieldType() == null || collectionField.getFieldType().getId() == null) {
            throw new NullConstraintViolationException("fieldType");
        }

        // Verify field type exists
        FieldType fieldType = fieldTypeRepository.findById(collectionField.getFieldType().getId())
                .orElseThrow(() -> new ForeignKeyViolationException("FieldType", "id",
                        collectionField.getFieldType().getId()));
        collectionField.setFieldType(fieldType);

        // Set the collection reference if provided
        if (collectionField.getCollection() == null || collectionField.getCollection().getId() == null) {
            throw new NullConstraintViolationException("collection");
        }

        // Verify collection exists
        CollectionListing collection = collectionListingRepository.findById(collectionField.getCollection().getId())
                .orElseThrow(() -> new ForeignKeyViolationException("CollectionListing", "id",
                        collectionField.getCollection().getId()));
        collectionField.setCollection(collection);

        // Set display_preference to be the max value for this collection + 10
        // This considers both components and fields
        Integer collectionId = collection.getId();
        Integer nextDisplayPreference = collectionOrderingService.getNextDisplayPreference(collectionId);
        collectionField.setDisplayPreference(nextDisplayPreference);

        log.debug("Setting display_preference to {} for collection {}",
                collectionField.getDisplayPreference(), collectionId);

        // Process field configurations if they exist (set the back-reference before saving)
        if (collectionField.getConfigs() != null && !collectionField.getConfigs().isEmpty()) {
            log.info("Processing {} field configurations for collection field", collectionField.getConfigs().size());

            for (CollectionFieldConfig config : collectionField.getConfigs()) {
                // Set the reference to the field (will be saved automatically due to cascade)
                config.setCollectionField(collectionField);
                log.debug("Added config for field config ID: {} with value: {}",
                    config.getFieldConfig() != null ? config.getFieldConfig().getId() : "NULL",
                    config.getFieldConfigValue());
            }
        } else {
            log.info("No field configurations to process");
        }

        // Save the collection field (this will also save configurations due to cascade = CascadeType.ALL)
        CollectionField savedField = collectionFieldRepository.save(collectionField);
        log.info("Successfully saved {} field configurations via cascade",
            savedField.getConfigs() != null ? savedField.getConfigs().size() : 0);
        log.info("Created new collection field with ID: {}", savedField.getId());
        return savedField;
    }

    @Override
    @Transactional
    public CollectionField updateCollectionField(Integer id, CollectionField collectionField) {
        // Verify the field exists
        CollectionField existingField = getCollectionFieldById(id);
        
        // Only update additionalInformation if it's not null
        if (collectionField.getAdditionalInformation() != null) {
            existingField.setAdditionalInformation(collectionField.getAdditionalInformation());
        } else {
            // Log that we're keeping the existing additionalInformation
            log.info("Keeping existing additionalInformation for field {}: {}", 
                    id, existingField.getAdditionalInformation());
        }
        
        // Update other fields...
        if (collectionField.getFieldType() != null && collectionField.getFieldType().getId() != null) {
            FieldType fieldType = fieldTypeRepository.findById(collectionField.getFieldType().getId())
                    .orElseThrow(() -> new ForeignKeyViolationException("FieldType", "id",
                            collectionField.getFieldType().getId()));
            existingField.setFieldType(fieldType);
        }
        
        // Update display preference if provided
        if (collectionField.getDisplayPreference() != null) {
            existingField.setDisplayPreference(collectionField.getDisplayPreference());
        }
        
        // Update collection if provided
        if (collectionField.getCollection() != null && collectionField.getCollection().getId() != null) {
            CollectionListing collection = collectionListingRepository.findById(collectionField.getCollection().getId())
                    .orElseThrow(() -> new ForeignKeyViolationException("CollectionListing", "id",
                            collectionField.getCollection().getId()));
            existingField.setCollection(collection);
        }
        
        // Update dependent field if provided
        existingField.setDependentOn(collectionField.getDependentOn());
        
        return collectionFieldRepository.save(existingField);
    }

    @Override
    @Transactional
    public void deleteCollectionField(Integer id) {
        // Verify the field exists
        CollectionField field = getCollectionFieldById(id);

        // Delete all associated configs first
        List<CollectionFieldConfig> configs = collectionFieldConfigService.getCollectionFieldConfigsByCollectionFieldId(id);
        for (CollectionFieldConfig config : configs) {
            collectionFieldConfigService.deleteCollectionFieldConfig(config.getId());
        }

        // Then delete the field
        collectionFieldRepository.delete(field);

        // Verify deletion
        if (collectionFieldRepository.existsById(id)) {
            log.error("Failed to delete collection field with ID: {}", id);
            throw new RuntimeException("Failed to delete collection field with ID: " + id);
        }

        log.info("Successfully deleted collection field with ID: {}", id);
    }

    @Override
    public Integer getNextAvailableId() {
        // Get the maximum ID currently in use
        Integer maxId = collectionFieldRepository.findMaxId();

        // If no records exist, start with ID 1, otherwise use max + 1
        return maxId != null ? maxId + 1 : 1;
    }
}

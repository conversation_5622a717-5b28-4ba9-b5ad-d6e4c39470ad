package com.cms.controller;

import com.cms.entity.CollectionListing;
import com.cms.service.CollectionListingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/public/collections")
@RequiredArgsConstructor
@Tag(name = "Public Collection", description = "Public Collection API")
public class PublicCollectionController {

    private final CollectionListingService collectionListingService;

    @GetMapping("/getAll")
    @Operation(summary = "Get all collections (public)", description = "Returns a list of all collections without authentication")
    public ResponseEntity<List<CollectionListing>> getAllCollections() {
        return ResponseEntity.ok(collectionListingService.getAllCollections());
    }
}

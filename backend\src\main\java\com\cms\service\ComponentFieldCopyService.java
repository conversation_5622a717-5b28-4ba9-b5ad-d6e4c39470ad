package com.cms.service;

import com.cms.entity.CollectionComponent;
import com.cms.entity.ComponentField;
import com.cms.entity.CollectionField;

import java.util.List;

/**
 * Service for copying component fields to collection fields
 */
public interface ComponentFieldCopyService {
    
    /**
     * Copy fields from a component to a collection
     * 
     * @param collectionComponent The collection component containing the component and collection
     * @return List of created collection fields
     */
    List<CollectionField> copyComponentFieldsToCollection(CollectionComponent collectionComponent);
    
    /**
     * Copy a single component field to a collection field
     * 
     * @param componentField The component field to copy
     * @param collectionComponent The collection component containing the collection
     * @return The created collection field
     */
    CollectionField copyComponentFieldToCollectionField(ComponentField componentField, CollectionComponent collectionComponent);
}

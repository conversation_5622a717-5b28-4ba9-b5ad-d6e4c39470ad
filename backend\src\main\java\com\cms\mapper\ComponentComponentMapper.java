package com.cms.mapper;

import com.cms.dto.ComponentComponentDTO;
import com.cms.entity.ComponentComponent;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class ComponentComponentMapper {

    public ComponentComponentDTO toDTO(ComponentComponent entity) {
        return new ComponentComponentDTO(entity);
    }

    public List<ComponentComponentDTO> toDTOList(List<ComponentComponent> entities) {
        return entities.stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }

    public ComponentComponent toEntity(ComponentComponentDTO dto) {
        return dto.toEntity();
    }

    public List<ComponentComponent> toEntityList(List<ComponentComponentDTO> dtos) {
        return dtos.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }
}

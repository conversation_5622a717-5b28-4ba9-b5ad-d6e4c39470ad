# Multi-Tenant Login Guide

This guide explains how to use the login functionality with the multi-tenant system in the CMS application.

## Overview

The CMS application supports multi-tenancy using a schema-based approach with PostgreSQL. Each tenant has its own schema in the database, providing data isolation while sharing the same application code.

When logging in, users can specify which tenant they want to access by using the format `username@tenantId`.

## Login with Tenant Identification

Users can authenticate with a tenant identifier in their username using the format `username@tenantId`.

### Examples:

1. <PERSON>gin with tenant identifier:
   ```
   POST /api/auth/login
   {
     "username": "john@tenant1",
     "password": "password123"
   }
   ```
   This will authenticate the user "john" in the tenant1 schema.

2. Login without tenant identifier (uses default tenant):
   ```
   POST /api/auth/login
   {
     "username": "jane",
     "password": "password123"
   }
   ```
   This will authenticate the user "jane" in the default "public" schema.

## How It Works

1. When a login request is received, the system extracts the tenant identifier from the username.
2. The tenant context is set for the current thread.
3. The system looks up the user in the specified tenant schema.
4. If authentication is successful, a JWT token is generated that includes the tenant information.
5. All subsequent API calls using this token will automatically use the correct tenant schema.

## API Access with Tenant Context

There are two ways to specify the tenant context for API calls after login:

1. **Using the JWT token from login**:
   When you login with `username@tenantId`, the tenant context is stored in the JWT token. All subsequent API calls using this token will automatically use the correct tenant schema.

2. **Using the X-TenantID header**:
   ```
   GET /api/collections
   X-TenantID: tenant1
   Authorization: Bearer <jwt_token>
   ```
   This will retrieve data from the tenant1 schema, regardless of the tenant in the JWT token.

## Important Notes

1. The tenant identifier in the username (`username@tenantId`) is only used for authentication and setting the tenant context. The actual username stored in the database is just the part before the @ symbol.

2. The X-TenantID header takes precedence over the tenant identifier in the JWT token.

3. If no tenant is specified (either in the username or X-TenantID header), the default "public" tenant is used.

4. Each tenant has its own schema with identical table structures, providing complete data isolation between tenants.

## Troubleshooting

If you encounter login issues with tenant-specific users:

1. Verify the tenant exists in the `tenants` table in the public schema.
2. Ensure the tenant's `is_active` flag is set to true.
3. Check that the user exists in the correct tenant schema.
4. Verify you're using the correct format: `username@tenantId`.

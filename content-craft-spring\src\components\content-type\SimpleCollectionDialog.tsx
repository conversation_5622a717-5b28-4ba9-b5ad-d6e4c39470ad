import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2 } from 'lucide-react';
import { categoriesApi } from '@/lib/api';

interface SimpleCollectionDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (data: any) => void;
}

export default function SimpleCollectionDialog({
  open,
  onClose,
  onSave
}: SimpleCollectionDialogProps) {
  const [name, setName] = useState('');
  const [apiIdSingular, setApiIdSingular] = useState('');
  const [apiIdPlural, setApiIdPlural] = useState('');
  const [categoryId, setCategoryId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [categories, setCategories] = useState<any[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(false);

  // Handle name change to auto-generate API IDs
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setName(value);

    // Auto-generate API IDs based on the name
    if (value) {
      const apiId = value.toLowerCase().replace(/[^a-z0-9]/g, '_');
      setApiIdSingular(apiId);
      setApiIdPlural(apiId + 's');
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted');

    // Validate form
    if (!name || !apiIdSingular || !apiIdPlural) {
      console.error('Form validation failed');
      return;
    }

    setIsLoading(true);
    console.log('Setting loading state');

    try {
      // Create the data object
      const data = {
        name,
        apiIdSingular,
        apiIdPlural,
        categoryId,
        draftAndPublish: false,
        isInternationally: false
      };

      console.log('Submitting data:', data);

      // Call the onSave function provided by the parent component
      onSave(data);

      // Note: We don't close the dialog here - the parent component will handle that
      // This prevents the dialog from closing before the API call completes
    } catch (error) {
      console.error('Error submitting form:', error);
      setIsLoading(false);
    }
  };

  // Fetch categories when dialog opens
  useEffect(() => {
    if (open) {
      // Reset form fields
      setName('');
      setApiIdSingular('');
      setApiIdPlural('');
      setCategoryId('');
      setIsLoading(false);

      // Fetch categories from the database using the API service
      setLoadingCategories(true);
      console.log('Fetching categories...');

      // Use a timeout to ensure we can see the loading state
      setTimeout(() => {
        categoriesApi.getAll()
          .then(response => {
            console.log('Categories API response:', response);
            console.log('Categories fetched:', response.data);

            // Check if we have valid data
            if (Array.isArray(response.data)) {
              setCategories(response.data);
              console.log('Categories set in state:', response.data);
            } else {
              console.warn('Categories data is not an array:', response.data);
              // Set some default categories for testing
              const defaultCategories = [
                { id: 1, categoryName: 'Basic' },
                { id: 2, categoryName: 'Advanced' },
                { id: 3, categoryName: 'Media' }
              ];
              setCategories(defaultCategories);
              console.log('Using default categories:', defaultCategories);
            }
          })
          .catch(error => {
            console.error('Error fetching categories:', error);
            // Set some default categories for testing
            const defaultCategories = [
              { id: 1, categoryName: 'Basic' },
              { id: 2, categoryName: 'Advanced' },
              { id: 3, categoryName: 'Media' }
            ];
            setCategories(defaultCategories);
            console.log('Using default categories due to error:', defaultCategories);
          })
          .finally(() => {
            setLoadingCategories(false);
            console.log('Category loading completed');
          });
      }, 500); // Short delay to show loading state
    }
  }, [open]);

  // Prevent dialog from closing on outside click
  const handleOpenChange = (isOpen: boolean) => {
    // Only allow closing through the Cancel button
    if (!isOpen) {
      console.log('Dialog close attempted');
      // Don't close the dialog automatically
      // onClose();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[600px]" onEscapeKeyDown={(e) => e.preventDefault()}>
        <DialogHeader className="flex flex-row items-center">
          <div className="flex items-center">
            <div className="flex items-center justify-center w-8 h-8 mr-2 bg-primary text-primary-foreground rounded">
              CT
            </div>
            <DialogTitle>Create a collection type</DialogTitle>
          </div>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <div className="space-y-4 py-4">
            <div>
              <h3 className="text-lg font-medium">Configurations</h3>
              <p className="text-sm text-muted-foreground">A type for modeling data</p>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Display name</Label>
                <Input
                  id="name"
                  placeholder="e.g. Restaurant, Article, Product..."
                  value={name}
                  onChange={handleNameChange}
                  required
                />
                <p className="text-sm text-muted-foreground">
                  The UID is used to generate the API routes and databases tables/collections.
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="categoryId">Select Category</Label>
                {loadingCategories ? (
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">Loading categories...</span>
                  </div>
                ) : (
                  <Select value={categoryId} onValueChange={setCategoryId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">None</SelectItem>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id.toString()}>
                          {category.categoryName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
                <p className="text-sm text-muted-foreground">
                  Choose a category for this collection
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="apiIdSingular">API ID (Singular)</Label>
                  <Input
                    id="apiIdSingular"
                    placeholder="e.g. restaurant"
                    value={apiIdSingular}
                    onChange={(e) => setApiIdSingular(e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="apiIdPlural">API ID (Plural)</Label>
                  <Input
                    id="apiIdPlural"
                    placeholder="e.g. restaurants"
                    value={apiIdPlural}
                    onChange={(e) => setApiIdPlural(e.target.value)}
                    required
                  />
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                console.log('Cancel button clicked');
                onClose();
              }}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !name || !apiIdSingular || !apiIdPlural}
            >
              {isLoading ? 'Creating...' : 'Create collection'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

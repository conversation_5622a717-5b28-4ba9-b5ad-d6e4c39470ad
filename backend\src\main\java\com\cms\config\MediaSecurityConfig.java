package com.cms.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfiguration;

/**
 * Special security configuration for media endpoints to prevent ORB blocking issues.
 * This configuration has higher precedence than the main security config.
 */
@Configuration
@EnableWebSecurity
public class MediaSecurityConfig {

    @Bean
    @Order(1) // Higher precedence than the main security config
    public SecurityFilterChain mediaFilterChain(HttpSecurity http) throws Exception {
        // Configure security specifically for media endpoints
        http
            // Disable CSRF for media endpoints
            .csrf(csrf -> csrf.disable())

            // Configure CORS for media endpoints
            .cors(cors -> {
                CorsConfiguration corsConfig = new CorsConfiguration();
                corsConfig.addAllowedOrigin("*");
                corsConfig.addAllowedMethod("*");
                corsConfig.addAllowedHeader("*");
                corsConfig.setAllowCredentials(false);
                corsConfig.addExposedHeader("*");
                cors.configurationSource(request -> corsConfig);
            })

            // Configure request matchers for media endpoints
            .securityMatcher("/media/**", "/api/media/**")

            // Authorize all requests to media endpoints
            .authorizeHttpRequests(auth -> auth
                .anyRequest().permitAll()
            )

            // Disable session creation for media endpoints
            .sessionManagement(session -> session
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            );

        return http.build();
    }
}

package com.cms.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ComponentDetailsDTO {
    private Integer id;
    private String componentName;
    private String componentDisplayName;
    private String componentApiId;
    private Boolean isActive;
    private String getUrl;
    private String postUrl;
    private String updateUrl;
    private List<FieldDTO> fields = new ArrayList<>();
}

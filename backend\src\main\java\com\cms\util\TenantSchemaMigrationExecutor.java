package com.cms.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.FileCopyUtils;

import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;

/**
 * Utility class to execute SQL migration scripts for all tenant schemas.
 * This class runs automatically when the application starts.
 */
@Component
public class TenantSchemaMigrationExecutor implements CommandLineRunner {

    private static final Logger log = LoggerFactory.getLogger(TenantSchemaMigrationExecutor.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void run(String... args) {
        try {
            log.info("Executing SQL migration script to add category_id column to all tenant schemas...");
            
            // Load the SQL script from the classpath
            ClassPathResource resource = new ClassPathResource("db/migration/V2__add_category_id_to_all_tenant_schemas.sql");
            String sql = readResourceAsString(resource);
            
            // Execute the SQL script
            jdbcTemplate.execute(sql);
            
            log.info("Migration completed successfully.");
        } catch (Exception e) {
            log.error("Error executing migration script: {}", e.getMessage(), e);
        }
    }
    
    private String readResourceAsString(ClassPathResource resource) throws IOException {
        try (Reader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8)) {
            return FileCopyUtils.copyToString(reader);
        }
    }
}

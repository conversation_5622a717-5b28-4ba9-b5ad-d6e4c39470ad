# Multi-Tenant System Troubleshooting Guide

This guide provides solutions for common issues with the multi-tenant system.

## Username Uniqueness Issue

### Problem

Users cannot register with the same username in different tenant schemas because of unique constraints on the username and email columns.

### Solution

1. **Update the User entity**: Remove the unique constraints from the User entity:

```java
// Before
@Entity
@Table(name = "users", uniqueConstraints = {
        @UniqueConstraint(columnNames = "username"),
        @UniqueConstraint(columnNames = "email")
})
public class User extends Auditable {
    // ...
    @Column(name = "username", nullable = false, unique = true)
    private String username;
    
    @Column(name = "email", nullable = false, unique = true)
    private String email;
    // ...
}

// After
@Entity
@Table(name = "users")
public class User extends Auditable {
    // ...
    @Column(name = "username", nullable = false)
    private String username;
    
    @Column(name = "email", nullable = false)
    private String email;
    // ...
}
```

2. **Update the database schema**: Execute the SQL migration script to remove the unique constraints from all tenant schemas:

```sql
-- Function to drop constraints in all schemas
CREATE OR REPLACE FUNCTION drop_unique_constraints_in_all_schemas() RETURNS void AS $$
DECLARE
    schema_name text;
BEGIN
    -- Loop through all schemas except system schemas
    FOR schema_name IN 
        SELECT nspname FROM pg_namespace 
        WHERE nspname NOT LIKE 'pg_%' AND nspname != 'information_schema'
    LOOP
        -- Drop unique constraint on username if it exists
        BEGIN
            EXECUTE format('ALTER TABLE %I.users DROP CONSTRAINT IF EXISTS users_username_key', schema_name);
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error dropping username constraint in schema %: %', schema_name, SQLERRM;
        END;
        
        -- Drop unique constraint on email if it exists
        BEGIN
            EXECUTE format('ALTER TABLE %I.users DROP CONSTRAINT IF EXISTS users_email_key', schema_name);
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error dropping email constraint in schema %: %', schema_name, SQLERRM;
        END;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Execute the function
SELECT drop_unique_constraints_in_all_schemas();

-- Drop the function after use
DROP FUNCTION drop_unique_constraints_in_all_schemas();
```

3. **Restart the application**: After making these changes, restart the application to apply the changes.

## Tenant Context Not Being Propagated

### Problem

The tenant context is not being properly propagated to the database connection during certain operations.

### Solution

1. **Use the UserService**: Always use the UserService for user-related operations instead of directly using the UserRepository:

```java
// Before
boolean usernameExists = userRepository.existsByUsername(username);

// After
boolean usernameExists = userService.existsByUsername(username);
```

2. **Explicitly set the tenant context**: When performing tenant-specific operations, explicitly set the tenant context:

```java
// Save the current tenant context
String currentTenant = TenantContextHolder.getTenantId();

try {
    // Set the tenant context to the specified tenant
    TenantContextHolder.setTenantId(tenantId);
    
    // Perform tenant-specific operations
    // ...
} finally {
    // Restore the original tenant context
    TenantContextHolder.setTenantId(currentTenant);
}
```

## Database Schema Not Being Created

### Problem

The tenant schema is not being created when a user registers with a new tenant.

### Solution

1. **Check the TenantService**: Make sure the TenantService is properly creating the schema:

```java
@Transactional
public Tenant createTenant(Tenant tenant) {
    if (tenantRepository.existsBySchemaName(tenant.getSchemaName())) {
        throw new IllegalArgumentException("Tenant with schema name " + tenant.getSchemaName() + " already exists");
    }

    // Create the schema
    jdbcTemplate.execute(String.format("CREATE SCHEMA IF NOT EXISTS %s", tenant.getSchemaName()));

    // Initialize the schema with tables
    initSchema(tenant.getSchemaName());

    // Save the tenant
    return tenantRepository.save(tenant);
}
```

2. **Check the database connection**: Make sure the database connection has the necessary permissions to create schemas.

3. **Check the logs**: Look for any errors in the logs related to schema creation.

## User Data Not Being Saved in the Correct Schema

### Problem

User data is not being saved in the correct tenant schema.

### Solution

1. **Use the UserService**: Always use the UserService for user-related operations:

```java
// Before
userRepository.save(user);

// After
userService.createUser(user);
```

2. **Check the tenant context**: Make sure the tenant context is properly set before saving the user:

```java
// Set the tenant context
TenantContextHolder.setTenantId(tenantSchemaName);

// Save the user
userService.createUser(user);
```

3. **Check the MultiTenantConnectionProvider**: Make sure the MultiTenantConnectionProvider is properly setting the schema for database connections:

```java
@Override
public Connection getConnection(String tenantIdentifier) throws SQLException {
    final Connection connection = getAnyConnection();
    try {
        if (tenantIdentifier != null) {
            connection.createStatement().execute(String.format("SET search_path TO %s", tenantIdentifier));
        } else {
            connection.createStatement().execute(String.format("SET search_path TO %s", DEFAULT_TENANT_ID));
        }
    } catch (SQLException e) {
        throw new HibernateException("Could not alter JDBC connection to specified schema [" + tenantIdentifier + "]", e);
    }
    return connection;
}
```

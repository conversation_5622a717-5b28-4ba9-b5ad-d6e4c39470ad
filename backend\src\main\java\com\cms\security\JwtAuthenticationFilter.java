package com.cms.security;

import com.cms.config.TenantContextHolder;
import com.cms.util.TenantUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

@Component
@RequiredArgsConstructor
@Slf4j
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtTokenProvider tokenProvider;
    private final UserDetailsService userDetailsService;
    private final TenantUtils tenantUtils;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        String requestURI = request.getRequestURI();
        log.debug("Processing request: {}", requestURI);

        try {
            String jwt = getJwtFromRequest(request);

            // Check for X-TenantID header first (highest priority)
            String headerTenantId = request.getHeader("X-TenantID");
            if (StringUtils.hasText(headerTenantId)) {
                log.info("Setting tenant from header: {}", headerTenantId);
                TenantContextHolder.forceTenantContext(headerTenantId);
                log.debug("Tenant context after setting from header: {}", TenantContextHolder.getTenantId());
            }

            if (StringUtils.hasText(jwt)) {
                // Extract tenant from JWT token (second priority)
                if (!StringUtils.hasText(headerTenantId)) {
                    String tokenTenant = tokenProvider.getTenantFromToken(jwt);
                    if (StringUtils.hasText(tokenTenant)) {
                        log.info("Setting tenant from JWT token: {}", tokenTenant);
                        TenantContextHolder.forceTenantContext(tokenTenant);
                        log.debug("Tenant context after setting from token: {}", TenantContextHolder.getTenantId());
                    }
                }

                // Only proceed with authentication if not already authenticated
                if (SecurityContextHolder.getContext().getAuthentication() == null) {
                    try {
                        String usernameWithTenant = tokenProvider.getUsernameFromToken(jwt);
                        log.debug("Username from token: {}", usernameWithTenant);

                        // The tenant context is already set above, so loadUserByUsername will use the correct tenant
                        UserDetails userDetails = userDetailsService.loadUserByUsername(usernameWithTenant);

                        if (tokenProvider.validateToken(jwt, userDetails)) {
                            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                                    userDetails, null, userDetails.getAuthorities());
                            authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                            SecurityContextHolder.getContext().setAuthentication(authentication);
                            log.info("Authentication set for user: {}, tenant: {}",
                                    usernameWithTenant, TenantContextHolder.getTenantId());
                        }
                    } catch (Exception e) {
                        log.warn("Could not set user authentication: {}", e.getMessage());
                    }
                }
            }
        } catch (Exception ex) {
            log.error("Could not set user authentication in security context", ex);
        }

        // Log the current tenant context before proceeding
        log.debug("Current tenant context before proceeding with request: {}", TenantContextHolder.getTenantId());

        // Continue with the filter chain
        filterChain.doFilter(request, response);

        // Don't clear tenant context here - it should be managed by TenantFilter
    }

    private String getJwtFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}

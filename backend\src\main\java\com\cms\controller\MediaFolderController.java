package com.cms.controller;

import com.cms.dto.MediaFolderDTO;
import com.cms.exception.NullConstraintViolationException;
import com.cms.exception.UniqueConstraintViolationException;
import com.cms.service.MediaFolderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/media/folders")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Media Folders", description = "Media Folders API")
public class MediaFolderController {

    private final MediaFolderService folderService;

    @PostMapping(value = "/create", consumes = {"application/json", "application/json;charset=UTF-8", "application/json; charset=UTF-8", "application/json;charset=utf-8", "application/json; charset=utf-8", "*/*"})
    @Operation(summary = "Create folder", description = "Create a new media folder")
    public ResponseEntity<MediaFolderDTO> createFolder(
            @RequestParam("folderName") String folderName,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "parentId", required = false) Integer parentId) {
        try {
            // Validate folder name
            if (folderName == null || folderName.trim().isEmpty()) {
                throw new NullConstraintViolationException("folderName");
            }

            MediaFolderDTO folderDTO = folderService.createFolder(folderName, description, parentId);

            // Return 201 Created status code instead of 200 OK
            return ResponseEntity.status(HttpStatus.CREATED).body(folderDTO);
        } catch (UniqueConstraintViolationException ex) {
            // Let the global exception handler handle this to return 701 status code
            throw ex;
        } catch (NullConstraintViolationException ex) {
            // Let the global exception handler handle this to return 700 status code
            throw ex;
        }
    }

    @GetMapping("/getAll")
    @Operation(summary = "Get all root folders", description = "Get all root folders")
    public ResponseEntity<List<MediaFolderDTO>> getRootFolders() {
        List<MediaFolderDTO> folders = folderService.getRootFolders();

        return ResponseEntity.ok(folders);
    }

    @GetMapping("/getById/{id}")
    @Operation(summary = "Get folder by ID", description = "Get a folder by its ID")
    public ResponseEntity<MediaFolderDTO> getFolderById(@PathVariable Integer id) {
        MediaFolderDTO folderDTO = folderService.getFolderById(id);

        return ResponseEntity.ok(folderDTO);
    }

    @GetMapping("/getSubfolders/{parentId}")
    @Operation(summary = "Get subfolders", description = "Get all subfolders of a folder")
    public ResponseEntity<List<MediaFolderDTO>> getSubfolders(@PathVariable Integer parentId) {
        List<MediaFolderDTO> folders = folderService.getSubfolders(parentId);

        return ResponseEntity.ok(folders);
    }

    @PutMapping("/update/{id}")
    @Operation(summary = "Update folder", description = "Update a folder")
    public ResponseEntity<MediaFolderDTO> updateFolder(
            @PathVariable Integer id,
            @RequestParam(value = "folderName", required = false) String folderName,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "parentId", required = false) Integer parentId) {
        try {
            // If folderName is provided but empty, throw NullConstraintViolationException
            if (folderName != null && folderName.trim().isEmpty()) {
                throw new NullConstraintViolationException("folderName");
            }

            MediaFolderDTO folderDTO = folderService.updateFolder(id, folderName, description, parentId);
            return ResponseEntity.ok(folderDTO);
        } catch (UniqueConstraintViolationException ex) {
            // Let the global exception handler handle this to return 701 status code
            throw ex;
        } catch (NullConstraintViolationException ex) {
            // Let the global exception handler handle this to return 700 status code
            throw ex;
        }
    }

    @DeleteMapping("/deleteById/{id}")
    @Operation(summary = "Delete folder", description = "Delete a folder and all its contents")
    public ResponseEntity<Void> deleteFolder(@PathVariable Integer id) {
        folderService.deleteFolder(id);

        return ResponseEntity.noContent().build();
    }

    @GetMapping("/search")
    @Operation(summary = "Search folders", description = "Search folders by name")
    public ResponseEntity<List<MediaFolderDTO>> searchFolders(@RequestParam("query") String query) {
        List<MediaFolderDTO> folders = folderService.searchFolders(query);

        return ResponseEntity.ok(folders);
    }
}

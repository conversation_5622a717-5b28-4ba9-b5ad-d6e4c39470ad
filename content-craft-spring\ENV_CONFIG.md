# Environment Configuration Guide

This document explains how to configure the environment settings for the Content Craft CMS application.

## Environment Files

The application uses the following environment files:

1. `.env` - Default environment variables (fallback)
2. `.env.development` - Development-specific variables
3. `.env.production` - Production-specific variables
4. `.env.local` - Local overrides (gitignored, for personal developer settings)

## Priority Order

Environment variables are loaded in the following order (highest priority last):

1. `.env` (base/default)
2. `.env.development` or `.env.production` (based on mode)
3. `.env.local` (local overrides)

## Key Environment Variables

### API URL Configuration

The `VITE_API_URL` variable is used to specify the backend API endpoint:

```
# For local development
VITE_API_URL=http://localhost:8072/api

# For production with specific IP
VITE_API_URL=http://************:8072/api
```

### Debug Mode

The `VITE_DEBUG` variable controls debug logging:

```
# Enable debug logging
VITE_DEBUG=true

# Disable debug logging (for production)
VITE_DEBUG=false
```

## Dynamic API URL Resolution

The application uses a multi-step process to determine the API URL:

1. URL parameter (`?apiUrl=...`) - Highest priority
2. Environment variable (`VITE_API_URL`) - Second priority
3. Dynamic detection based on hostname - Lowest priority

## Deployment Configuration

When deploying to production:

1. Build the application with the production environment:
   ```
   npm run build
   ```

2. To use a specific IP address, update the `.env.production` file:
   ```
   VITE_API_URL=http://your-server-ip:8072/api
   ```

## Local Development

For local development, you can create a `.env.local` file with your specific settings:

```
# Override the API URL for your local setup
VITE_API_URL=http://localhost:8072/api

# Enable additional debug features
VITE_EXTENDED_DEBUG=true
```

This file is gitignored and won't affect other developers.

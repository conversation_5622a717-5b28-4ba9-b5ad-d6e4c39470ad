# User Login Status Tracking

This document describes the user login status tracking feature implemented in the CMS application.

## Overview

The application now tracks whether users are currently logged in or not using a database field. This allows administrators to see who is currently active in the system.

## Database Schema

### Users Table
The `users` table has been updated to include:
- `is_logged_in` BOOLEAN DEFAULT false - Tracks whether the user is currently logged in

### Index
An index has been added for performance:
- `idx_users_is_logged_in` - Index on the `is_logged_in` column

## API Endpoints

### Login Status Tracking
- **POST /auth/login** - Sets `is_logged_in = true` on successful login
- **POST /auth/logout** - Sets `is_logged_in = false` on logout

### Admin Endpoints
- **GET /auth/logged-in-users** - Returns all currently logged-in users

#### Response Format
```json
[
  {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "isActive": true,
    "isLoggedIn": true
  }
]
```

## Implementation Details

### UserService Methods
- `updateLoginStatus(username, isLoggedIn)` - Updates the login status for a user
- `getLoggedInUsers()` - Returns all users with `is_logged_in = true`

### UserRepository Methods
- `findByIsLoggedIn(Boolean isLoggedIn)` - Finds users by login status

## Usage Examples

### Check who's currently logged in
```bash
curl -X GET http://localhost:8071/auth/logged-in-users \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Database Query
```sql
-- Get all logged-in users
SELECT username, email, created_at 
FROM users 
WHERE is_logged_in = true;

-- Count logged-in users
SELECT COUNT(*) as logged_in_count 
FROM users 
WHERE is_logged_in = true;
```

## Testing

Run the login status tests:
```bash
mvn test -Dtest=UserLoginStatusTest
```

## Notes

- The `is_logged_in` field is automatically set to `false` for all new users
- Existing users will have `is_logged_in = false` by default
- The feature works across all tenant schemas in the multi-tenant setup
- Login status is updated in real-time during login/logout operations

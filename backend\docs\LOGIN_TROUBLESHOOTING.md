# Login Troubleshooting Guide

## 🚨 "Bad Credentials" Error - Common Causes & Solutions

### Problem: Getting "Login failed - Bad credentials" error

This error typically occurs when the login format doesn't match the expected tenant-aware format.

## 🔍 Root Cause

With the new **email domain-based tenant management**, users are stored in tenant-specific schemas, but the login system requires the format `username@tenant_schema` to know which schema to search.

## ✅ Solutions

### 1. **Use Correct Login Format**

#### If you registered with email domain:
- **Registered as**: `admin` with email `<EMAIL>`
- **Login as**: `admin@acme_com` (note: dots become underscores)

#### Examples:
```
Registration Email → Tenant Schema → Login Format
<EMAIL> → acme_com → admin@acme_com
<EMAIL> → company_org → john@company_org
<EMAIL> → my_startup_io → user@my_startup_io
```

### 2. **Check Your Tenant Schema Name**

Use the new API endpoint to find your correct login format:

```bash
curl -X POST http://localhost:8071/api/auth/get-login-format \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "admin"
  }'
```

Response:
```json
{
  "email": "<EMAIL>",
  "username": "admin",
  "tenantSchema": "acme_com",
  "loginFormat": "admin@acme_com",
  "message": "Use this format to login: admin@acme_com"
}
```

### 3. **Verify Tenant Exists**

Check if your tenant was created properly:

```bash
curl -X POST http://localhost:8071/api/auth/list-users-in-tenant \
  -H "Content-Type: application/json" \
  -d '{
    "tenantSchemaName": "acme_com"
  }'
```

## 🧪 Testing Steps

### Step 1: Register a User
```bash
curl -X POST http://localhost:8071/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "email": "<EMAIL>",
    "password": "admin123"
  }'
```

### Step 2: Check Login Format
```bash
curl -X POST http://localhost:8071/api/auth/get-login-format \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "admin"
  }'
```

### Step 3: Login with Correct Format
```bash
curl -X POST http://localhost:8071/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin@acme_com",
    "password": "admin123"
  }'
```

## 🔧 Common Mistakes

### ❌ Wrong: Using email as username
```json
{
  "username": "<EMAIL>",
  "password": "admin123"
}
```

### ❌ Wrong: Using just username without tenant
```json
{
  "username": "admin",
  "password": "admin123"
}
```

### ✅ Correct: Using username@tenant_schema format
```json
{
  "username": "admin@acme_com",
  "password": "admin123"
}
```

## 🎯 Domain to Schema Conversion Rules

| Email Domain | Tenant Schema | Login Username |
|--------------|---------------|----------------|
| `acme.com` | `acme_com` | `user@acme_com` |
| `company.org` | `company_org` | `user@company_org` |
| `my-startup.io` | `my_startup_io` | `user@my_startup_io` |
| `test-site.co.uk` | `test_site_co_uk` | `user@test_site_co_uk` |

## 🔍 Debug Information

### Check Application Logs
Look for these log entries in the backend:
```
Login attempt for username: admin@acme_com
Setting tenant context to: acme_com
Login successful for user: admin@acme_com
```

### Check Database
Verify user exists in correct schema:
```sql
-- Check if tenant exists
SELECT * FROM tenants WHERE schema_name = 'acme_com';

-- Check if user exists in tenant schema
SET search_path TO acme_com;
SELECT * FROM users WHERE username = 'admin';
```

## 🚀 Quick Fix for Your Current Issue

Based on your screenshot showing "Purav" as username:

1. **If you registered with email like `<EMAIL>`:**
   - Login as: `purav@company_com`

2. **If you registered with email like `<EMAIL>`:**
   - Login as: `purav@acme_com`

3. **To find your exact tenant schema:**
   ```bash
   curl -X POST http://localhost:8071/api/auth/get-login-format \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "username": "purav"
     }'
   ```

## 📞 Still Having Issues?

1. **Check the browser console** for detailed error messages
2. **Check backend logs** for authentication attempts
3. **Use the test HTML interface** at `backend/test-email-domain-tenants.html`
4. **Verify your registration** was successful by listing users in the tenant

## 🔄 Migration from Old System

If you have users from the old system (before email domain-based tenants):
- Old format still works: `username@explicit_tenant`
- New users use email domain-derived tenants
- Both systems coexist seamlessly

package com.cms.service.impl;

import com.cms.dto.MediaDTO;
import com.cms.entity.Media;
import com.cms.entity.MediaFolder;
import com.cms.entity.User;
import com.cms.exception.ResourceNotFoundException;
import com.cms.repository.MediaFolderRepository;
import com.cms.repository.MediaRepository;
import com.cms.repository.UserRepository;
import com.cms.service.MediaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class MediaServiceImpl implements MediaService {

    private final MediaRepository mediaRepository;
    private final MediaFolderRepository folderRepository;
    private final UserRepository userRepository;
    private final Path fileStorageLocation;
    private final String mediaBaseUrl;
    private final String serverExternalUrl;

    @Override
    @Transactional
    public MediaDTO storeFile(MultipartFile file, Integer folderId, String description, String altText, Boolean isPublic) {
        // Get current user
        User currentUser = getCurrentUser();

        // Normalize file name
        String originalFileName = StringUtils.cleanPath(Objects.requireNonNull(file.getOriginalFilename()));

        // Generate a unique file name to prevent conflicts
        String fileName = UUID.randomUUID().toString() + "_" + originalFileName;

        try {
            // Check if the file's name contains invalid characters
            if (fileName.contains("..")) {
                throw new RuntimeException("Filename contains invalid path sequence: " + originalFileName);
            }

            // Get folder if provided
            MediaFolder folder = null;
            if (folderId != null) {
                folder = folderRepository.findById(folderId)
                        .orElseThrow(() -> new ResourceNotFoundException("Folder not found with id: " + folderId));
            }

            // Create subfolder for the current year and month
            LocalDateTime now = LocalDateTime.now();
            String yearMonth = now.getYear() + "/" + String.format("%02d", now.getMonthValue());
            Path targetLocation = fileStorageLocation.resolve(yearMonth);
            Files.createDirectories(targetLocation);

            // Copy file to the target location (replacing existing file with the same name)
            Path targetPath = targetLocation.resolve(fileName);
            Files.copy(file.getInputStream(), targetPath, StandardCopyOption.REPLACE_EXISTING);

            // Create media entity
            Media media = new Media();
            media.setFileName(fileName);
            media.setOriginalFileName(originalFileName);
            media.setFilePath(yearMonth + "/" + fileName);
            media.setFileType(file.getContentType());
            media.setFileSize(file.getSize());
            media.setDescription(description);
            media.setAltText(altText);
            media.setIsPublic(isPublic != null ? isPublic : false);
            media.setFolder(folder);
            media.setUploadedBy(currentUser);

            // Generate public URL
            String publicUrl = mediaBaseUrl + "/" + yearMonth + "/" + fileName;
            media.setPublicUrl(publicUrl);

            // Save media entity
            Media savedMedia = mediaRepository.save(media);

            // Return DTO
            return mapToDTO(savedMedia);
        } catch (IOException ex) {
            throw new RuntimeException("Could not store file " + fileName, ex);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public MediaDTO getMediaById(Integer id) {
        Media media = mediaRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Media not found with id: " + id));

        return mapToDTO(media);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<MediaDTO> getAllMedia(Pageable pageable) {
        return mediaRepository.findAll(pageable)
                .map(this::mapToDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MediaDTO> getMediaByFolder(Integer folderId) {
        if (folderId == null) {
            // Return media without a folder
            return mediaRepository.findAll().stream()
                    .filter(media -> media.getFolder() == null)
                    .map(this::mapToDTO)
                    .collect(Collectors.toList());
        }

        MediaFolder folder = folderRepository.findById(folderId)
                .orElseThrow(() -> new ResourceNotFoundException("Folder not found with id: " + folderId));

        return mediaRepository.findByFolder(folder).stream()
                .map(this::mapToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<MediaDTO> searchMedia(String query, Pageable pageable) {
        return mediaRepository.findByFileNameContainingIgnoreCase(query, pageable)
                .map(this::mapToDTO);
    }

    @Override
    @Transactional
    public MediaDTO updateMedia(Integer id, String fileName, String description, String altText, Boolean isPublic, Integer folderId) {
        Media media = mediaRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Media not found with id: " + id));

        // Update fields if provided
        if (StringUtils.hasText(fileName)) {
            media.setFileName(fileName);
        }

        if (description != null) {
            media.setDescription(description);
        }

        if (altText != null) {
            media.setAltText(altText);
        }

        if (isPublic != null) {
            media.setIsPublic(isPublic);
        }

        if (folderId != null) {
            MediaFolder folder = folderRepository.findById(folderId)
                    .orElseThrow(() -> new ResourceNotFoundException("Folder not found with id: " + folderId));
            media.setFolder(folder);
        }

        Media updatedMedia = mediaRepository.save(media);

        return mapToDTO(updatedMedia);
    }

    @Override
    @Transactional
    public void deleteMedia(Integer id) {
        Media media = mediaRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Media not found with id: " + id));

        // Delete the file from the file system
        try {
            Path filePath = fileStorageLocation.resolve(media.getFilePath());
            Files.deleteIfExists(filePath);
        } catch (IOException ex) {
            log.error("Error deleting file: {}", media.getFilePath(), ex);
        }

        // Delete the media entity
        mediaRepository.delete(media);
    }

    @Override
    @Transactional
    public String generateShareToken(Integer id) {
        Media media = mediaRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Media not found with id: " + id));

        // Generate a unique token
        String token = UUID.randomUUID().toString();
        media.setShareToken(token);

        mediaRepository.save(media);

        // Generate share URL with the external IP address
        return serverExternalUrl + "/api/media/share/" + token;
    }

    @Override
    @Transactional(readOnly = true)
    public Media getMediaByShareToken(String token) {
        return mediaRepository.findByShareToken(token)
                .orElseThrow(() -> new ResourceNotFoundException("Invalid or expired share token"));
    }

    @Override
    @Transactional
    public MediaDTO replaceFile(Integer id, MultipartFile file) {
        // Get current user
        User currentUser = getCurrentUser();

        // Find the existing media
        Media media = mediaRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Media not found with id: " + id));

        // Normalize file name
        String originalFileName = StringUtils.cleanPath(Objects.requireNonNull(file.getOriginalFilename()));

        try {
            // Check if the file's name contains invalid characters
            if (originalFileName.contains("..")) {
                throw new RuntimeException("Filename contains invalid path sequence: " + originalFileName);
            }

            // Extract the existing file path components
            String existingPath = media.getFilePath();
            Path existingFilePath = fileStorageLocation.resolve(existingPath);

            // Delete the existing file
            Files.deleteIfExists(existingFilePath);

            // Copy the new file to the same location
            Files.copy(file.getInputStream(), existingFilePath, StandardCopyOption.REPLACE_EXISTING);

            // Update media entity with new file information
            media.setOriginalFileName(originalFileName);
            media.setFileType(file.getContentType());
            media.setFileSize(file.getSize());
            // Note: modifiedBy and modifiedAt will be updated automatically by Spring Data JPA auditing

            // Save updated media entity
            Media updatedMedia = mediaRepository.save(media);

            // Return DTO
            return mapToDTO(updatedMedia);
        } catch (IOException ex) {
            throw new RuntimeException("Could not replace file for media with id: " + id, ex);
        }
    }

    private MediaDTO mapToDTO(Media media) {
        MediaDTO dto = new MediaDTO();
        dto.setId(media.getId());
        dto.setFileName(media.getFileName());
        dto.setOriginalFileName(media.getOriginalFileName());
        dto.setFileType(media.getFileType());
        dto.setFileSize(media.getFileSize());
        dto.setWidth(media.getWidth());
        dto.setHeight(media.getHeight());
        dto.setDuration(media.getDuration());
        dto.setAltText(media.getAltText());
        dto.setDescription(media.getDescription());
        dto.setPublicUrl(media.getPublicUrl());
        dto.setIsPublic(media.getIsPublic());

        if (media.getFolder() != null) {
            dto.setFolderId(media.getFolder().getId());
            dto.setFolderName(media.getFolder().getFolderName());
        }

        if (media.getUploadedBy() != null) {
            dto.setUploadedByUsername(media.getUploadedBy().getUsername());
        }

        dto.setCreatedAt(media.getCreatedAt());
        dto.setModifiedAt(media.getModifiedAt());

        if (media.getShareToken() != null) {
            dto.setShareUrl(serverExternalUrl + "/api/media/share/" + media.getShareToken());
        }

        return dto;
    }

    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        // Check if the user is anonymous
        if (authentication == null || "anonymousUser".equals(authentication.getName())) {
            // For anonymous users, try to find a default system user
            return userRepository.findByUsername("admin")
                    .orElse(null); // Return null if no admin user exists
        }

        String username = authentication.getName();

        return userRepository.findByUsername(username)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with username: " + username));
    }
}

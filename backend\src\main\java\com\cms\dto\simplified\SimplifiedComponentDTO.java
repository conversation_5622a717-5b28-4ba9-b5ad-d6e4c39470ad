package com.cms.dto.simplified;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SimplifiedComponentDTO {
    private Integer id;
    private SimplifiedComponentDetailsDTO component;
    private Integer displayPreference;
    private Boolean isRepeatable;
    private Integer minRepeatOccurrences;
    private Integer maxRepeatOccurrences;
    private Boolean isActive;
}

package com.cms.security;

import com.cms.config.TenantContextHolder;
import com.cms.entity.User;
import com.cms.service.UserService;
import com.cms.util.TenantUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserDetailsServiceImpl implements UserDetailsService {

    private final UserService userService;
    private final TenantUtils tenantUtils;

    @Override
    @Transactional
    public UserDetails loadUserByUsername(String usernameWithTenant) throws UsernameNotFoundException {
        log.info("Loading user by username: {}", usernameWithTenant);

        try {
            // Parse the username and set the tenant context
            String username = tenantUtils.parseUsernameAndSetTenant(usernameWithTenant);
            String tenantId = TenantContextHolder.getTenantId();
            log.info("Parsed username: {}, tenant context set to: {}", username, tenantId);

            // Find the user by the username part only using the UserService
            log.info("Searching for user '{}' in tenant: {}", username, tenantId);
            User user = userService.findByUsername(username)
                    .orElseThrow(() -> {
                        log.warn("User not found with username: {} in tenant: {}", username, tenantId);
                        return new UsernameNotFoundException("User not found with username: " + username + " in tenant: " + tenantId);
                    });

            log.info("User found: {}, ID: {}, Active: {}, LoggedIn: {}",
                    user.getUsername(), user.getId(), user.getIsActive(), user.getIsLoggedIn());

            // Check if user is active (but don't check is_logged_in as that's just for tracking)
            if (!user.getIsActive()) {
                log.warn("User {} is not active", username);
                throw new UsernameNotFoundException("User account is disabled: " + username);
            }

            return new org.springframework.security.core.userdetails.User(
                    // Store the original username with tenant for later use
                    usernameWithTenant,
                    user.getPassword(),
                    user.getIsActive(),
                    true,
                    true,
                    true,
                    new ArrayList<>()
            );
        } catch (Exception e) {
            log.error("Error during user authentication for: {}", usernameWithTenant, e);
            throw e;
        }
    }
}

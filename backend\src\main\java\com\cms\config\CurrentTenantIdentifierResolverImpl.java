package com.cms.config;

import org.hibernate.context.spi.CurrentTenantIdentifierResolver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Implementation of Hibernate's CurrentTenantIdentifierResolver.
 * This class is responsible for resolving the current tenant identifier for Hibernate.
 */
public class CurrentTenantIdentifierResolverImpl implements CurrentTenantIdentifierResolver<String> {

    private static final Logger log = LoggerFactory.getLogger(CurrentTenantIdentifierResolverImpl.class);

    /**
     * Resolve the current tenant identifier.
     * This method is called by Hibernate to determine which tenant schema to use for database operations.
     *
     * @return The current tenant identifier
     */
    @Override
    public String resolveCurrentTenantIdentifier() {
        String tenantId = TenantContextHolder.getTenantId();
        log.info("Resolving current tenant identifier: {}, thread: {}",
                tenantId, Thread.currentThread().getName());

        // Add a stack trace for debugging
        if (log.isDebugEnabled()) {
            StringBuilder stackTrace = new StringBuilder("Tenant resolution stack trace:\n");
            for (StackTraceElement element : Thread.currentThread().getStackTrace()) {
                stackTrace.append("\t").append(element.toString()).append("\n");
            }
            log.debug(stackTrace.toString());
        }

        return tenantId;
    }

    /**
     * Validate existing current sessions.
     * This method is called by Hibernate to determine if existing sessions should be validated.
     *
     * @return true if existing sessions should be validated, false otherwise
     */
    @Override
    public boolean validateExistingCurrentSessions() {
        return true;
    }
}

-- =============================================
-- FIELD TYPES TABLE INITIALIZATION
-- =============================================

-- Create sequence for FieldType entity if it doesn't exist
CREATE SEQUENCE IF NOT EXISTS cms_field_type_seq START WITH 100 INCREMENT BY 1;

-- Create the field_types table if it doesn't exist
CREATE TABLE IF NOT EXISTS field_types (
    id INT PRIMARY KEY,
    field_type_name VARCHAR(255) NOT NULL UNIQUE,
    field_type_desc TEXT,
    display_name VARCHAR(255),
    help_text TEXT,
    logo_image_path VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_by VARCHAR(50),
    created_at TIMESTAMP,
    modified_by VARCHAR(50),
    modified_at TIMESTAMP
);

-- Create index for field_type_name for faster lookups
CREATE INDEX IF NOT EXISTS idx_field_types_name ON field_types(field_type_name);
CREATE INDEX IF NOT EXISTS idx_field_types_is_active ON field_types(is_active);

-- =============================================
-- CONFIG TYPES TABLE INITIALIZATION
-- =============================================

-- Create the config_types table if it doesn't exist
CREATE TABLE IF NOT EXISTS config_types (
    id INT PRIMARY KEY,
    config_type_name VARCHAR(255) NOT NULL UNIQUE,
    config_type_desc TEXT,
    display_name VARCHAR(255),
    additional_info TEXT,
    disclaimer_text TEXT,
    placeholder_text TEXT,
    is_active BOOLEAN DEFAULT true,
    created_by VARCHAR(50),
    created_at TIMESTAMP,
    modified_by VARCHAR(50),
    modified_at TIMESTAMP
);

-- Create sequence for ConfigType entity if it doesn't exist
CREATE SEQUENCE IF NOT EXISTS cms_config_type_seq START WITH 100 INCREMENT BY 1;

-- Create indexes for config_types table
CREATE INDEX IF NOT EXISTS idx_config_types_name ON config_types(config_type_name);
CREATE INDEX IF NOT EXISTS idx_config_types_is_active ON config_types(is_active);

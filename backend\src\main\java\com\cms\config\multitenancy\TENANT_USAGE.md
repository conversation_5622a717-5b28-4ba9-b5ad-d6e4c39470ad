# Multi-Tenant System Usage Guide

This guide explains how to use the multi-tenant system in the CMS application.

## Overview

The CMS application supports multi-tenancy using a schema-based approach with PostgreSQL. Each tenant has its own schema in the database, providing data isolation while sharing the same application code.

## User Registration with Tenant Identification

Users can register with a tenant identifier in their username using the format `username@tenantId`.

### Examples:

1. Register a user in a specific tenant:
   ```
   POST /api/auth/register
   {
     "username": "john@tenant1",
     "email": "<EMAIL>",
     "password": "password123"
   }
   ```
   This will:
   - Create the tenant "tenant1" if it doesn't exist
   - Create a user "john" in the tenant1 schema

2. Register a user in the default tenant:
   ```
   POST /api/auth/register
   {
     "username": "jane",
     "email": "<EMAIL>",
     "password": "password123"
   }
   ```
   This will create a user "jane" in the default "public" schema.

## Authentication with Tenant Identification

Users can authenticate with a tenant identifier in their username using the format `username@tenantId`.

### Examples:

1. <PERSON>gin with tenant identifier:
   ```
   POST /api/auth/login
   {
     "username": "john@tenant1",
     "password": "password123"
   }
   ```
   This will authenticate the user "john" in the tenant1 schema.

2. Login without tenant identifier (uses default tenant):
   ```
   POST /api/auth/login
   {
     "username": "jane",
     "password": "password123"
   }
   ```
   This will authenticate the user "jane" in the default "public" schema.

## API Access with Tenant Context

There are two ways to specify the tenant context for API calls:

1. **Using the X-TenantID header**:
   ```
   GET /api/collections
   X-TenantID: tenant1
   Authorization: Bearer <jwt_token>
   ```
   This will retrieve data from the tenant1 schema.

2. **Using the tenant identifier in the JWT token**:
   When you login with `username@tenantId`, the tenant context is stored in the JWT token. All subsequent API calls using this token will automatically use the correct tenant schema.

## Tenant Management

Administrators can manage tenants using the tenant management API:

1. Create a new tenant:
   ```
   POST /api/tenants
   {
     "name": "Tenant 2",
     "schemaName": "tenant2",
     "description": "Second tenant"
   }
   ```

2. List all tenants:
   ```
   GET /api/tenants
   ```

3. Delete a tenant:
   ```
   DELETE /api/tenants/{id}
   ```

## Important Notes

1. The tenant identifier in the username (`username@tenantId`) is only used for authentication and setting the tenant context. The actual username stored in the database is just the part before the @ symbol.

2. The X-TenantID header takes precedence over the tenant identifier in the JWT token.

3. If no tenant is specified (either in the username or X-TenantID header), the default "public" tenant is used.

4. Each tenant has its own schema with identical table structures, providing complete data isolation between tenants.

5. When a new tenant is created, its schema is initialized with the same tables and initial data as the public schema.

package com.cms.controller;

import com.cms.dto.SimpleFieldConfigDTO;
import com.cms.entity.FieldConfig;
import com.cms.exception.ResourceNotFoundException;
import com.cms.exception.UniqueConstraintViolationException;
import com.cms.service.FieldConfigService;
import com.cms.service.FieldTypeService;

import java.util.stream.Collectors;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/field-configs")
@RequiredArgsConstructor
@Tag(name = "Field Config", description = "Field Config API")
public class FieldConfigController {

    private final FieldConfigService fieldConfigService;
    private final FieldTypeService fieldTypeService;

    @GetMapping("/getAll")
    @Operation(summary = "Get all field configs", description = "Returns a list of all field configs")
    public ResponseEntity<List<FieldConfig>> getAllFieldConfigs() {
        return ResponseEntity.ok(fieldConfigService.getAllFieldConfigs());
    }

    @GetMapping("/getAllActive")
    @Operation(summary = "Get active field configs", description = "Returns a list of active field configs or 204 No Content if none are active")
    public ResponseEntity<?> getActiveFieldConfigs() {
        List<FieldConfig> activeFieldConfigs = fieldConfigService.getActiveFieldConfigs();

        if (activeFieldConfigs.isEmpty()) {
            return ResponseEntity.noContent().build();
        }

        return ResponseEntity.ok(activeFieldConfigs);
    }

    @GetMapping("/getByFieldTypeId/{fieldTypeId}")
    @Operation(summary = "Get active simplified field configs by field type",
              description = "Returns a list of active simplified field configs (configName and valueType) for a specific field type. Only configurations with isActive=true are included. Returns 204 No Content if field type doesn't exist or has no active configs.")
    public ResponseEntity<?> getFieldConfigsByFieldType(@PathVariable Integer fieldTypeId) {
        // First check if the field type exists
        if (!fieldTypeService.existsById(fieldTypeId)) {
            return ResponseEntity.noContent().build();
        }

        // Get all field configs for this field type
        List<FieldConfig> fieldConfigs = fieldConfigService.getFieldConfigsByFieldTypeId(fieldTypeId);

        // Convert to simplified DTOs and filter out inactive configurations
        List<SimpleFieldConfigDTO> simplifiedConfigs = fieldConfigs.stream()
            .filter(config -> config.getIsActive()) // Only include active configurations
            .map(config -> new SimpleFieldConfigDTO(config.getId(), config.getConfigName(), config.getValueType()))
            .collect(Collectors.toList());

        // Return 204 No Content if there are no active configurations
        if (simplifiedConfigs.isEmpty()) {
            return ResponseEntity.noContent().build();
        }

        return ResponseEntity.ok(simplifiedConfigs);
    }

    @GetMapping("/getById/{id}")
    @Operation(summary = "Get field config by ID", description = "Returns a field config by its ID")
    public ResponseEntity<FieldConfig> getFieldConfigById(@PathVariable Integer id) {
        return fieldConfigService.getFieldConfigById(id)
                .map(ResponseEntity::ok)
                .orElseThrow(() -> new ResourceNotFoundException("Field config not found with id: " + id));
    }

    @PostMapping("/create")
    @Operation(summary = "Create a new field config", description = "Creates a new field config")
    public ResponseEntity<FieldConfig> createFieldConfig(@Valid @RequestBody FieldConfig fieldConfig) {
        if (fieldConfigService.existsByConfigName(fieldConfig.getConfigName())) {
            throw new UniqueConstraintViolationException("configName", fieldConfig.getConfigName());
        }
        return ResponseEntity.status(HttpStatus.CREATED).body(fieldConfigService.createFieldConfig(fieldConfig));
    }

    @PutMapping("/update/{id}")
    @Operation(summary = "Update a field config", description = "Updates an existing field config")
    public ResponseEntity<FieldConfig> updateFieldConfig(@PathVariable Integer id, @Valid @RequestBody FieldConfig fieldConfig) {
        return ResponseEntity.ok(fieldConfigService.updateFieldConfig(id, fieldConfig));
    }

    @DeleteMapping("/deleteById/{id}")
    @Operation(summary = "Delete a field config", description = "Soft deletes a field config by setting isActive to false")
    public ResponseEntity<Void> deleteFieldConfig(@PathVariable Integer id) {
        fieldConfigService.deleteFieldConfig(id);
        return ResponseEntity.noContent().build();
    }
}

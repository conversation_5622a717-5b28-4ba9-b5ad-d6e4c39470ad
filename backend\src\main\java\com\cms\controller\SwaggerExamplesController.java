package com.cms.controller;

import com.cms.dto.ComponentDetailsDTO;
import com.cms.dto.FieldDTO;
import com.cms.entity.CollectionListing;
import com.cms.entity.ContentEntry;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Controller to provide example payloads for Swagger documentation
 */
@RestController
@RequestMapping("/swagger-examples")
@Tag(name = "Swagger Examples", description = "Example payloads for API documentation")
public class SwaggerExamplesController {

    @GetMapping("/component")
    @Operation(summary = "Get component example", description = "Returns an example component payload with child components for API documentation")
    public ResponseEntity<Map<String, Object>> getComponentExample() {
        Map<String, Object> component = new HashMap<>();
        component.put("id", 100);
        component.put("componentName", "Example Component");
        component.put("componentDisplayName", "Example Component Display");
        component.put("componentApiId", "example_component");
        component.put("isActive", true);
        component.put("getUrl", "/api/example");
        component.put("postUrl", "/api/example/create");
        component.put("updateUrl", "/api/example/update");

        // Add child components
        List<Map<String, Object>> childComponents = new ArrayList<>();

        Map<String, Object> childComponent1 = new HashMap<>();
        childComponent1.put("id", 101);

        Map<String, Object> childComponentDetails1 = new HashMap<>();
        childComponentDetails1.put("id", 102);
        childComponentDetails1.put("componentName", "Child Component 1");
        childComponentDetails1.put("componentDisplayName", "Child Component 1 Display");
        childComponentDetails1.put("componentApiId", "child_component_1");
        childComponentDetails1.put("isActive", true);

        childComponent1.put("childComponent", childComponentDetails1);
        childComponent1.put("displayPreference", 1);
        childComponent1.put("isRepeatable", false);
        childComponent1.put("isActive", true);

        Map<String, Object> childComponent2 = new HashMap<>();
        childComponent2.put("id", 103);

        Map<String, Object> childComponentDetails2 = new HashMap<>();
        childComponentDetails2.put("id", 104);
        childComponentDetails2.put("componentName", "Child Component 2");
        childComponentDetails2.put("componentDisplayName", "Child Component 2 Display");
        childComponentDetails2.put("componentApiId", "child_component_2");
        childComponentDetails2.put("isActive", true);

        childComponent2.put("childComponent", childComponentDetails2);
        childComponent2.put("displayPreference", 2);
        childComponent2.put("isRepeatable", true);
        childComponent2.put("minRepeatOccurrences", 1);
        childComponent2.put("maxRepeatOccurrences", 5);
        childComponent2.put("isActive", true);

        childComponents.add(childComponent1);
        childComponents.add(childComponent2);

        component.put("childComponents", childComponents);
        component.put("fields", new ArrayList<>());

        return ResponseEntity.ok(component);
    }

    @GetMapping("/component-create")
    @Operation(summary = "Get component creation example", description = "Returns an example component creation payload without fields")
    public ResponseEntity<Map<String, Object>> getComponentCreateExample() {
        Map<String, Object> example = new HashMap<>();

        example.put("componentName", "Example Component");
        example.put("componentDisplayName", "Example Component Display");
        example.put("componentApiId", "example_component");
        example.put("isActive", true);
        example.put("getUrl", "/api/example");
        example.put("postUrl", "/api/example/create");
        example.put("updateUrl", "/api/example/update");

        return ResponseEntity.ok(example);
    }

    @GetMapping("/collection")
    @Operation(summary = "Get collection example", description = "Returns an example collection payload for API documentation")
    public ResponseEntity<CollectionListing> getCollectionExample() {
        CollectionListing collection = new CollectionListing();
        collection.setId(100);
        collection.setCollectionName("Example Collection");
        collection.setCollectionDesc("This is an example collection");
        collection.setCollectionApiId("example_collection");
        collection.setAdditionalInformation("Additional information about the collection");
        collection.setDisclaimerText("Disclaimer text for the collection");

        return ResponseEntity.ok(collection);
    }

    @GetMapping("/collection-create")
    @Operation(summary = "Get collection creation example", description = "Returns an example collection creation payload")
    public ResponseEntity<Map<String, Object>> getCollectionCreateExample() {
        Map<String, Object> example = new HashMap<>();

        example.put("collectionName", "Example Collection");
        example.put("collectionDesc", "This is an example collection");
        example.put("collectionApiId", "example_collection");
        example.put("additionalInformation", "Additional information about the collection");
        example.put("disclaimerText", "Disclaimer text for the collection");



        return ResponseEntity.ok(example);
    }

    @GetMapping("/content-entry")
    @Operation(summary = "Get content entry example", description = "Returns an example content entry payload for API documentation")
    public ResponseEntity<ContentEntry> getContentEntryExample() {
        ContentEntry entry = new ContentEntry();

        CollectionListing collection = new CollectionListing();
        collection.setId(100);
        entry.setCollection(collection);

        entry.setDataJson("{\"title\":\"Example Title\",\"content\":\"Example content text\",\"author\":\"John Doe\"}");

        return ResponseEntity.ok(entry);
    }

    @GetMapping("/content-entry-create")
    @Operation(summary = "Get content entry creation example", description = "Returns an example content entry creation payload")
    public ResponseEntity<Map<String, Object>> getContentEntryCreateExample() {
        Map<String, Object> example = new HashMap<>();

        Map<String, Object> collection = new HashMap<>();
        collection.put("id", 100);
        example.put("collection", collection);

        example.put("dataJson", "{\"title\":\"Example Title\",\"content\":\"Example content text\",\"author\":\"John Doe\"}");

        return ResponseEntity.ok(example);
    }

    @GetMapping("/component-details")
    @Operation(summary = "Get component details example", description = "Returns an example component details DTO")
    public ResponseEntity<ComponentDetailsDTO> getComponentDetailsExample() {
        ComponentDetailsDTO dto = new ComponentDetailsDTO();
        dto.setId(100);
        dto.setComponentName("Example Component");
        dto.setComponentDisplayName("Example Component Display");
        dto.setComponentApiId("example_component");
        dto.setIsActive(true);



        List<FieldDTO> fields = new ArrayList<>();
        // Fields are not included as per user preference
        dto.setFields(fields);

        return ResponseEntity.ok(dto);
    }

    @GetMapping("/collection-component")
    @Operation(summary = "Get collection component example", description = "Returns an example collection component payload")
    public ResponseEntity<Map<String, Object>> getCollectionComponentExample() {
        Map<String, Object> example = new HashMap<>();

        Map<String, Object> collection = new HashMap<>();
        collection.put("id", 100);
        example.put("collection", collection);

        Map<String, Object> component = new HashMap<>();
        component.put("id", 101);
        example.put("component", component);

        example.put("isRepeatable", false);
        example.put("minRepeatOccurrences", 0);
        example.put("maxRepeatOccurrences", 0);

        return ResponseEntity.ok(example);
    }
}

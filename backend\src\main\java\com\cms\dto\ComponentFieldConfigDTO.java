package com.cms.dto;

import com.cms.entity.ComponentField;
import com.cms.entity.ComponentFieldConfig;
import com.cms.entity.FieldConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ComponentFieldConfigDTO {
    private Integer id;
    private ComponentFieldWithComponentDTO componentField;
    private FieldConfig fieldConfig;
    private String fieldConfigValue;
    private String createdBy;
    private LocalDateTime createdAt;
    private String modifiedBy;
    private LocalDateTime modifiedAt;
    public static ComponentFieldConfigDTO fromEntity(ComponentFieldConfig entity) {
        if (entity == null) {
            return null;
        }

        ComponentFieldConfigDTO dto = new ComponentFieldConfigDTO();
        dto.setId(entity.getId());

        // Convert ComponentField to ComponentFieldWithComponentDTO
        if (entity.getComponentField() != null) {
            dto.setComponentField(ComponentFieldWithComponentDTO.fromEntity(entity.getComponentField()));
        }
        dto.setFieldConfig(entity.getFieldConfig());
        dto.setFieldConfigValue(entity.getFieldConfigValue());
        dto.setCreatedBy(entity.getCreatedBy());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setModifiedBy(entity.getModifiedBy());
        dto.setModifiedAt(entity.getModifiedAt());

        return dto;
    }
}

package com.cms.service;

import com.cms.entity.Tenant;
import com.cms.repository.TenantRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
public class TenantServiceTest {

    @Autowired
    private TenantService tenantService;

    @Autowired
    private TenantRepository tenantRepository;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Test
    @Transactional
    public void testCreateTenant() {
        // Create a unique schema name for testing
        String schemaName = "test_tenant_" + System.currentTimeMillis();
        
        // Create a new tenant
        Tenant tenant = new Tenant();
        tenant.setName("Test Tenant");
        tenant.setSchemaName(schemaName);
        tenant.setDescription("Test tenant for unit testing");
        tenant.setIsActive(true);
        
        // Save the tenant
        Tenant savedTenant = tenantService.createTenant(tenant);
        
        // Verify the tenant was saved
        assertNotNull(savedTenant.getId());
        assertEquals(schemaName, savedTenant.getSchemaName());
        
        // Verify the schema was created
        List<Map<String, Object>> schemas = jdbcTemplate.queryForList(
                "SELECT schema_name FROM information_schema.schemata WHERE schema_name = ?", 
                schemaName);
        
        assertFalse(schemas.isEmpty(), "Schema should be created");
        
        // Verify tables were created in the schema
        List<Map<String, Object>> tables = jdbcTemplate.queryForList(
                "SELECT table_name FROM information_schema.tables WHERE table_schema = ?",
                schemaName);
        
        assertFalse(tables.isEmpty(), "Tables should be created in the schema");
        
        // Clean up - delete the tenant and schema
        tenantService.deleteTenant(savedTenant.getId());
        
        // Verify the schema was deleted
        schemas = jdbcTemplate.queryForList(
                "SELECT schema_name FROM information_schema.schemata WHERE schema_name = ?", 
                schemaName);
        
        assertTrue(schemas.isEmpty(), "Schema should be deleted");
    }
}

package com.cms.service.impl;

import com.cms.config.TenantContextHolder;
import com.cms.entity.Client;
import com.cms.exception.ResourceNotFoundException;
import com.cms.exception.UniqueConstraintViolationException;
import com.cms.repository.ClientRepository;
import com.cms.service.ClientService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Implementation of ClientService
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ClientServiceImpl implements ClientService {

    private final ClientRepository clientRepository;

    @Override
    public List<Client> getAllClients() {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Getting all clients for tenant: {}", currentTenant);
        
        List<Client> clients = clientRepository.findAll();
        log.info("Found {} clients for tenant: {}", clients.size(), currentTenant);
        
        return clients;
    }

    @Override
    public Optional<Client> getClientById(Integer id) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Getting client with ID: {} for tenant: {}", id, currentTenant);
        
        return clientRepository.findById(id);
    }

    @Override
    public Optional<Client> getClientByName(String name) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Getting client with name: {} for tenant: {}", name, currentTenant);
        
        return clientRepository.findByName(name);
    }

    @Override
    @Transactional
    public Client createClient(Client client) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Creating client with name: {} for tenant: {}", client.getName(), currentTenant);
        
        // Check if client with the same name already exists
        if (existsByName(client.getName())) {
            throw new UniqueConstraintViolationException("name", client.getName());
        }
        
        return clientRepository.save(client);
    }

    @Override
    @Transactional
    public Client updateClient(Integer id, Client client) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Updating client with ID: {} for tenant: {}", id, currentTenant);
        
        Client existingClient = clientRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Client not found with id: " + id));
        
        // Check if the new name conflicts with an existing client (other than this one)
        if (!existingClient.getName().equals(client.getName()) && existsByName(client.getName())) {
            throw new UniqueConstraintViolationException("name", client.getName());
        }
        
        existingClient.setName(client.getName());
        
        return clientRepository.save(existingClient);
    }

    @Override
    @Transactional
    public void deleteClient(Integer id) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Deleting client with ID: {} for tenant: {}", id, currentTenant);
        
        Client client = clientRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Client not found with id: " + id));
        
        clientRepository.delete(client);
    }

    @Override
    public boolean existsByName(String name) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Checking if client with name: {} exists for tenant: {}", name, currentTenant);
        
        return clientRepository.existsByName(name);
    }
}

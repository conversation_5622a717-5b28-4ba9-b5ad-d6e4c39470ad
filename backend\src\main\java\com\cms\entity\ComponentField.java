package com.cms.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "component_fields")
@Getter
@Setter
@ToString(exclude = {"component", "dependentOn", "configs"})
@EqualsAndHashCode(callSuper = true, exclude = {"component", "dependentOn", "configs"})
@NoArgsConstructor
@AllArgsConstructor
public class ComponentField extends Auditable {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "cms_component_field_seq")
    @SequenceGenerator(name = "cms_component_field_seq", sequenceName = "cms_component_field_seq", initialValue = 100, allocationSize = 1)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "component_id")
    @JsonBackReference(value = "component-fields")
    private ComponentListing component;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "field_type_id")
    private FieldType fieldType;

    @Column(name = "display_preference")
    private Integer displayPreference;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dependent_on")
    private ComponentField dependentOn;

    @Column(name = "additional_information", columnDefinition = "TEXT")
    private String additionalInformation;

    @OneToMany(mappedBy = "componentField", cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JsonManagedReference(value = "field-configs")
    private List<ComponentFieldConfig> configs = new ArrayList<>();
}

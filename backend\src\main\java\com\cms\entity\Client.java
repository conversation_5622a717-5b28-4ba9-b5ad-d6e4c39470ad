package com.cms.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * Entity class representing a client in the system.
 */
@Entity
@Table(name = "clients")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class Client extends Auditable {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "cms_client_seq")
    @SequenceGenerator(name = "cms_client_seq", sequenceName = "cms_client_seq", initialValue = 100, allocationSize = 1)
    private Integer id;

    @NotBlank(message = "Client name is required")
    @Column(name = "name", nullable = false)
    private String name;
    
    // Note: We don't need to explicitly define the audit fields (createdBy, createdAt, modifiedBy, modifiedAt)
    // as they are inherited from the Auditable class and will be automatically managed by Spring Data JPA's
    // auditing capabilities through the AuditingEntityListener
}

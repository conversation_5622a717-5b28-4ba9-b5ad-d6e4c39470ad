package com.cms.service;

import com.cms.entity.CollectionComponent;

import java.util.List;

/**
 * Service for managing collection components
 */
public interface CollectionComponentService {
    
    /**
     * Get a collection component by ID
     * 
     * @param id The component ID
     * @return The collection component
     */
    CollectionComponent getCollectionComponentById(Integer id);
    
    /**
     * Get all collection components
     * 
     * @return List of all collection components
     */
    List<CollectionComponent> getAllCollectionComponents();
    
    /**
     * Get collection components by collection ID
     * 
     * @param collectionId The collection ID
     * @return List of collection components
     */
    List<CollectionComponent> getCollectionComponentsByCollectionId(Integer collectionId);
    
    /**
     * Create a new collection component
     * 
     * @param collectionComponent The collection component to create
     * @return The created collection component
     */
    CollectionComponent createCollectionComponent(CollectionComponent collectionComponent);
    
    /**
     * Update a collection component
     * 
     * @param id The component ID
     * @param collectionComponent The updated collection component
     * @return The updated collection component
     */
    CollectionComponent updateCollectionComponent(Integer id, CollectionComponent collectionComponent);
    
    /**
     * Delete a collection component
     * 
     * @param id The component ID
     */
    void deleteCollectionComponent(Integer id);
    
    /**
     * Get the next available ID for a collection component
     * 
     * @return The next available ID
     */
    Integer getNextAvailableId();
}

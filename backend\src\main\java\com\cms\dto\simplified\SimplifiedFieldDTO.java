package com.cms.dto.simplified;

import com.fasterxml.jackson.annotation.JsonUnwrapped;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SimplifiedFieldDTO {
    private Integer id;
    private SimplifiedFieldTypeDTO fieldType;
    private Integer displayPreference;

    @JsonUnwrapped
    private Map<String, Map<String, Object>> configs = new HashMap<>();
}

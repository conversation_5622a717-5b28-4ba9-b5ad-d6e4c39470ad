package com.cms.repository;

import com.cms.entity.CollectionListing;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CollectionListingRepository extends JpaRepository<CollectionListing, Integer> {
    Optional<CollectionListing> findByCollectionApiId(String collectionApiId);
    boolean existsByCollectionName(String collectionName);
    boolean existsByCollectionApiId(String collectionApiId);

    /**
     * Check if a collection with the given name exists in the specified category
     *
     * @param collectionName the name of the collection
     * @param categoryId the ID of the category
     * @return true if a collection with the name exists in the category, false otherwise
     */
    boolean existsByCollectionNameAndCategoryId(String collectionName, Integer categoryId);

    /**
     * Find a collection by name and category ID
     *
     * @param collectionName the name of the collection
     * @param categoryId the ID of the category
     * @return Optional containing the collection if found
     */
    Optional<CollectionListing> findByCollectionNameAndCategoryId(String collectionName, Integer categoryId);

    /**
     * Find all collections that belong to categories associated with a specific client
     *
     * @param clientId the ID of the client
     * @return list of collections
     */
    @Query("SELECT cl FROM CollectionListing cl WHERE cl.category.client.id = :clientId")
    List<CollectionListing> findByClientId(@Param("clientId") Integer clientId);

    /**
     * Find all collections that belong to a specific category
     *
     * @param categoryId the ID of the category
     * @return list of collections
     */
    List<CollectionListing> findByCategoryId(Integer categoryId);

    /**
     * Find all collections by client ID with full detail (joins resolved)
     *
     * @param clientId the ID of the client
     * @return list of collections with details
     */
    @Query("SELECT c FROM CollectionListing c WHERE c.category.client.id = :clientId")
    List<CollectionListing> findByClientIdWithDetails(@Param("clientId") Integer clientId);

    /**
     * Find all collections by client ID and category ID with full detail (joins resolved)
     *
     * @param clientId   the ID of the client
     * @param categoryId the ID of the category
     * @return list of collections with details
     */
    @Query("SELECT c FROM CollectionListing c WHERE c.category.client.id = :clientId AND c.category.id = :categoryId")
    List<CollectionListing> findByClientIdAndCategoryIdWithDetails(
            @Param("clientId") Integer clientId,
            @Param("categoryId") Integer categoryId
    );

    /**
     * Find all collections by client ID and category name with full detail (joins resolved)
     * This includes collections in both the specified category and its child categories
     *
     * @param clientId     the ID of the client
     * @param categoryName the name of the category
     * @return list of collections with details
     */
    @Query("SELECT c FROM CollectionListing c WHERE c.category.client.id = :clientId AND " +
           "(c.category.categoryName = :categoryName OR " +
           "c.category.parentCategory.categoryName = :categoryName)")
    List<CollectionListing> findByClientIdAndCategoryNameWithDetails(
            @Param("clientId") Integer clientId,
            @Param("categoryName") String categoryName
    );

    /**
     * Find all collections by client name and category name with full detail (joins resolved)
     * This includes collections in both the specified category and its child categories
     *
     * @param clientName   the name of the client
     * @param categoryName the name of the category
     * @return list of collections with details
     */
    @Query("SELECT c FROM CollectionListing c " +
           "LEFT JOIN FETCH c.category cat " +
           "LEFT JOIN FETCH cat.client client " +
           "LEFT JOIN FETCH cat.parentCategory parent " +
           "LEFT JOIN FETCH parent.client parentClient " +
           "WHERE " +
           "(" +
           "  (client.name IS NOT NULL AND TRIM(UPPER(client.name)) = TRIM(UPPER(:clientName))) OR " +
           "  (parentClient.name IS NOT NULL AND TRIM(UPPER(parentClient.name)) = TRIM(UPPER(:clientName)))" +
           ") AND " +
           "(" +
           "  TRIM(UPPER(cat.categoryName)) = TRIM(UPPER(:categoryName)) OR " +
           "  (parent.categoryName IS NOT NULL AND TRIM(UPPER(parent.categoryName)) = TRIM(UPPER(:categoryName)))" +
           ")")
    List<CollectionListing> findByClientNameAndCategoryNameWithDetails(
            @Param("clientName") String clientName,
            @Param("categoryName") String categoryName
    );

    /**
     * Find all collections by client name and category name with hierarchical support
     * This uses a more comprehensive approach to handle deeper category hierarchies
     *
     * @param clientName   the name of the client
     * @param categoryName the name of the category
     * @return list of collections with details
     */
    @Query("SELECT DISTINCT c FROM CollectionListing c " +
           "JOIN c.category cat " +
           "JOIN cat.client client " +
           "WHERE client.name = :clientName AND " +
           "(cat.categoryName = :categoryName OR " +
           "EXISTS (SELECT 1 FROM Category parent WHERE parent.categoryName = :categoryName AND " +
           "(cat.parentCategory = parent OR cat = parent)))")
    List<CollectionListing> findByClientNameAndCategoryNameHierarchical(
            @Param("clientName") String clientName,
            @Param("categoryName") String categoryName
    );

    /**
     * Simple debug query to find collections by client name only
     */
    @Query("SELECT c FROM CollectionListing c " +
           "LEFT JOIN FETCH c.category cat " +
           "LEFT JOIN FETCH cat.client client " +
           "LEFT JOIN FETCH cat.parentCategory parent " +
           "LEFT JOIN FETCH parent.client parentClient " +
           "WHERE " +
           "(client.name IS NOT NULL AND TRIM(UPPER(client.name)) = TRIM(UPPER(:clientName))) OR " +
           "(parentClient.name IS NOT NULL AND TRIM(UPPER(parentClient.name)) = TRIM(UPPER(:clientName)))")
    List<CollectionListing> findByClientNameDebug(@Param("clientName") String clientName);

    /**
     * Simple debug query to find collections by category name only
     */
    @Query("SELECT c FROM CollectionListing c " +
           "LEFT JOIN FETCH c.category cat " +
           "WHERE TRIM(UPPER(cat.categoryName)) = TRIM(UPPER(:categoryName))")
    List<CollectionListing> findByCategoryNameDebug(@Param("categoryName") String categoryName);
}

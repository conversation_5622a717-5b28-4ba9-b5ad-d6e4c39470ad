import React, { useState, useEffect } from 'react';
import { useAuthStore } from '@/lib/store';
import { authApi } from '@/lib/api';
import { getTenantFromToken, getUsernameFromToken } from '@/lib/jwt';

export default function AuthDebug() {
  const { user, token, isAuthenticated, login, logout } = useAuthStore();
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [testResults, setTestResults] = useState<any>({});

  useEffect(() => {
    updateDebugInfo();
  }, [user, token, isAuthenticated]);

  const updateDebugInfo = () => {
    const localStorageToken = localStorage.getItem('cms_token');
    
    let tokenInfo = null;
    if (token) {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        tokenInfo = {
          username: getUsernameFromToken(token),
          tenant: getTenantFromToken(token),
          issuedAt: new Date(payload.iat * 1000).toISOString(),
          expiresAt: new Date(payload.exp * 1000).toISOString(),
          age: Math.floor((Date.now() - (payload.iat * 1000)) / 1000),
        };
      } catch (e) {
        tokenInfo = { error: 'Failed to parse token' };
      }
    }

    setDebugInfo({
      authStore: {
        isAuthenticated,
        hasUser: !!user,
        hasToken: !!token,
        user: user,
      },
      localStorage: {
        hasToken: !!localStorageToken,
        tokenMatches: token === localStorageToken,
      },
      token: tokenInfo,
      currentPath: window.location.pathname,
      timestamp: new Date().toISOString(),
    });
  };

  const testLogin = async () => {
    try {
      setTestResults({ ...testResults, login: 'Testing...' });
      
      const response = await authApi.login('admin@public', 'password');
      const token = response.data.accessToken;
      
      // Extract info from token
      const username = getUsernameFromToken(token);
      const tenant = getTenantFromToken(token);
      
      const user = {
        id: username,
        username: username,
        email: '',
        tenant: tenant
      };
      
      // Save to store
      login(token, user);
      localStorage.setItem('cms_token', token);
      
      setTestResults({ 
        ...testResults, 
        login: 'Success',
        loginResponse: response.data,
        extractedUsername: username,
        extractedTenant: tenant,
      });
      
      updateDebugInfo();
    } catch (error: any) {
      setTestResults({ 
        ...testResults, 
        login: `Error: ${error.message}`,
        loginError: error.response?.data || error.message,
      });
    }
  };

  const testLogout = async () => {
    try {
      setTestResults({ ...testResults, logout: 'Testing...' });
      
      await authApi.logout();
      logout();
      localStorage.removeItem('cms_token');
      
      setTestResults({ ...testResults, logout: 'Success' });
      updateDebugInfo();
    } catch (error: any) {
      setTestResults({ 
        ...testResults, 
        logout: `Error: ${error.message}`,
        logoutError: error.response?.data || error.message,
      });
    }
  };

  const clearAll = () => {
    logout();
    localStorage.clear();
    setTestResults({});
    updateDebugInfo();
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Authentication Debug Page</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Debug Info */}
        <div className="bg-gray-100 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">Current State</h2>
          <pre className="text-xs overflow-auto bg-white p-3 rounded border">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
          <button 
            onClick={updateDebugInfo}
            className="mt-2 px-3 py-1 bg-blue-500 text-white rounded text-sm"
          >
            Refresh
          </button>
        </div>

        {/* Test Results */}
        <div className="bg-gray-100 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">Test Results</h2>
          <pre className="text-xs overflow-auto bg-white p-3 rounded border">
            {JSON.stringify(testResults, null, 2)}
          </pre>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="mt-6 space-x-4">
        <button 
          onClick={testLogin}
          className="px-4 py-2 bg-green-500 text-white rounded"
        >
          Test Login
        </button>
        <button 
          onClick={testLogout}
          className="px-4 py-2 bg-red-500 text-white rounded"
        >
          Test Logout
        </button>
        <button 
          onClick={clearAll}
          className="px-4 py-2 bg-gray-500 text-white rounded"
        >
          Clear All
        </button>
      </div>

      {/* Instructions */}
      <div className="mt-6 bg-yellow-100 p-4 rounded-lg">
        <h3 className="font-semibold mb-2">How to Use:</h3>
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>Check the "Current State" to see authentication status</li>
          <li>Click "Test Login" to perform a login</li>
          <li>Watch the state change and check for any automatic logout</li>
          <li>Click "Test Logout" to perform a logout</li>
          <li>Use "Clear All" to reset everything</li>
        </ol>
        <p className="mt-2 text-sm text-gray-600">
          This page helps debug the automatic logout issue by showing exactly what happens during login/logout.
        </p>
      </div>
    </div>
  );
}

package com.cms.service;

import com.cms.entity.User;
import com.cms.repository.UserRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class UserLoginStatusTest {

    @Autowired
    private UserService userService;

    @Autowired
    private UserRepository userRepository;

    @Test
    public void testUserLoginStatusDefaultValue() {
        // Create a new user
        User user = new User();
        user.setUsername("testuser");
        user.setEmail("<EMAIL>");
        user.setPassword("password");
        
        // Save the user
        User savedUser = userRepository.save(user);
        
        // Verify that is_logged_in defaults to false
        assertNotNull(savedUser.getIsLoggedIn());
        assertFalse(savedUser.getIsLoggedIn());
    }

    @Test
    public void testUpdateLoginStatus() {
        // Create a new user
        User user = new User();
        user.setUsername("logintest");
        user.setEmail("<EMAIL>");
        user.setPassword("password");
        user.setIsLoggedIn(false);
        
        User savedUser = userRepository.save(user);
        
        // Test updating login status to true
        userService.updateLoginStatus("logintest", true);
        
        // Verify the status was updated
        User updatedUser = userRepository.findByUsername("logintest").orElse(null);
        assertNotNull(updatedUser);
        assertTrue(updatedUser.getIsLoggedIn());
        
        // Test updating login status to false
        userService.updateLoginStatus("logintest", false);
        
        // Verify the status was updated
        updatedUser = userRepository.findByUsername("logintest").orElse(null);
        assertNotNull(updatedUser);
        assertFalse(updatedUser.getIsLoggedIn());
    }

    @Test
    public void testGetLoggedInUsers() {
        // Create two users - one logged in, one not
        User user1 = new User();
        user1.setUsername("loggedin");
        user1.setEmail("<EMAIL>");
        user1.setPassword("password");
        user1.setIsLoggedIn(true);
        
        User user2 = new User();
        user2.setUsername("loggedout");
        user2.setEmail("<EMAIL>");
        user2.setPassword("password");
        user2.setIsLoggedIn(false);
        
        userRepository.save(user1);
        userRepository.save(user2);
        
        // Get logged-in users
        var loggedInUsers = userService.getLoggedInUsers();
        
        // Should contain at least the logged-in user
        assertTrue(loggedInUsers.stream().anyMatch(u -> u.getUsername().equals("loggedin")));
        assertFalse(loggedInUsers.stream().anyMatch(u -> u.getUsername().equals("loggedout")));
    }
}

# Dashboard Troubleshooting Guide

## 🚨 Dashboard Not Loading / Empty Dashboard Issue

### Problem Description
The dashboard appears empty or shows loading state indefinitely, even though the user is successfully logged in.

## 🔍 Root Causes

### 1. **Empty Tenant Schema**
- **Cause**: New tenant with no collections or content entries
- **Symptom**: Dashboard shows 0 for all stats
- **Solution**: This is normal for new tenants

### 2. **API Endpoint Issues**
- **Cause**: Collections/Content Entries API returning 204 No Content
- **Symptom**: API calls succeed but return empty data
- **Solution**: Create some test data

### 3. **Tenant Context Issues**
- **Cause**: User logged in but tenant context not properly set
- **Symptom**: API calls go to wrong schema or default schema
- **Solution**: Check JWT token and tenant extraction

### 4. **Authentication Issues**
- **Cause**: JWT token missing or invalid
- **Symptom**: API calls fail with 401/403 errors
- **Solution**: Re-login or check token validity

## ✅ Solutions

### Solution 1: Check Debug Information
The dashboard now includes a debug panel showing:
- Current user
- Current tenant
- Token status
- Collections count

Look for this information at the bottom of the dashboard.

### Solution 2: Create Test Data
If you're in a new tenant, create some test content:

1. **Create a Content Type**:
   - Click "Create Your First Content Type" button
   - Or navigate to `/content-types/create`

2. **Add Some Content**:
   - After creating a content type, add some entries
   - Navigate to Content Manager

### Solution 3: Check Browser Console
Open browser developer tools (F12) and check for:

```javascript
// Look for these console messages:
"Fetching collections from API"
"Collections API response: ..."
"Current user: ..."
"Current token: Present/Missing"
```

### Solution 4: Test API Endpoints Manually

#### Test Collections API:
```bash
# Check if collections endpoint works
curl http://localhost:8071/api/collections/getAll \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### Test Content Entries API:
```bash
# Check if content entries endpoint works
curl http://localhost:8071/api/content-entries/getAll \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Solution 5: Refresh Dashboard Data
Click the "Refresh Data" button on the dashboard to force reload all data.

## 🧪 Testing Steps

### Step 1: Verify Login
1. Check that you're logged in (username shows in top-right)
2. Check debug panel shows correct user and tenant
3. Verify token is present

### Step 2: Check API Responses
1. Open browser developer tools
2. Go to Network tab
3. Refresh dashboard
4. Look for API calls to:
   - `/api/collections/getAll`
   - `/api/content-entries/getAll`

### Step 3: Check Response Status
- **200 OK with data**: Normal operation
- **204 No Content**: Empty tenant (normal for new tenants)
- **401 Unauthorized**: Token issue
- **403 Forbidden**: Permission issue
- **500 Internal Server Error**: Backend issue

### Step 4: Create Test Content
If APIs return empty data:
1. Click "Create Your First Content Type"
2. Create a simple content type (e.g., "Blog Post")
3. Add some fields (title, content)
4. Save the content type
5. Return to dashboard - should now show 1 collection

## 🔧 Backend Checks

### Check Database
```sql
-- Check if tenant exists
SELECT * FROM tenants WHERE schema_name = 'your_tenant_schema';

-- Check collections in tenant schema
SET search_path TO your_tenant_schema;
SELECT * FROM collections;

-- Check content entries in tenant schema
SELECT * FROM content_entries;
```

### Check Backend Logs
Look for these log entries:
```
Login successful for user: username@tenant_schema
Setting tenant context to: tenant_schema
Fetching collections for tenant: tenant_schema
```

## 🎯 Expected Behavior

### For New Tenants:
- Dashboard shows 0 collections, 0 content entries
- Welcome message appears
- "Create Your First Content Type" button visible
- Debug panel shows correct user/tenant info

### For Existing Tenants:
- Dashboard shows actual counts
- Stats cards display real numbers
- No welcome message
- Debug panel shows data

## 🚨 Common Issues & Fixes

### Issue 1: "Unknown" User/Tenant in Debug Panel
**Cause**: JWT token not properly decoded
**Fix**: 
1. Check JWT token format
2. Verify token contains user/tenant claims
3. Re-login if necessary

### Issue 2: Token Present but API Calls Fail
**Cause**: Token expired or invalid
**Fix**:
1. Check token expiration
2. Re-login to get fresh token
3. Check backend token validation

### Issue 3: API Returns 204 No Content
**Cause**: Empty tenant schema (normal for new tenants)
**Fix**:
1. Create content types and entries
2. Or verify you're in the correct tenant

### Issue 4: Dashboard Stuck in Loading State
**Cause**: API calls hanging or failing
**Fix**:
1. Check network connectivity
2. Verify backend is running
3. Check for JavaScript errors in console

## 📞 Quick Diagnostic Commands

### Check if Backend is Running:
```bash
curl http://localhost:8071/api/health
```

### Check Authentication:
```bash
curl http://localhost:8071/api/auth/me \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Check Collections Endpoint:
```bash
curl http://localhost:8071/api/collections/getAll \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -v
```

## 🎉 Success Indicators

### Dashboard Working Correctly:
- ✅ Debug panel shows correct user/tenant
- ✅ Token status shows "Present"
- ✅ API calls return 200 or 204 status
- ✅ Stats show actual counts (even if 0)
- ✅ No JavaScript errors in console
- ✅ Welcome message for new tenants OR stats for existing tenants

The dashboard is now much more robust and will provide clear feedback about what's happening, making it easier to diagnose and fix any issues!

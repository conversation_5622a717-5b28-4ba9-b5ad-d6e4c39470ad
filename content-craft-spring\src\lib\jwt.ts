/**
 * JWT token utilities for decoding and extracting information from JWT tokens
 */

interface JwtPayload {
  sub: string;        // Subject (username)
  tenant?: string;    // Tenant identifier
  exp: number;        // Expiration time
  iat: number;        // Issued at time
  [key: string]: any; // Allow any other claims
}

/**
 * Decode a JWT token without verification
 * This is safe for frontend use since we're just reading the payload
 *
 * @param token JWT token string
 * @returns Decoded payload or null if invalid
 */
export function decodeJwt(token: string): JwtPayload | null {
  try {
    // JWT tokens have three parts: header.payload.signature
    const parts = token.split('.');
    if (parts.length !== 3) {
      console.error('Invalid JWT token format');
      return null;
    }

    // Decode the payload (middle part)
    const payload = parts[1];
    const base64 = payload.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );

    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Error decoding JWT token:', error);
    return null;
  }
}

/**
 * Extract tenant information from a JWT token
 *
 * @param token JWT token string
 * @returns Tenant identifier or null if not found
 */
export function getTenantFromToken(token: string): string | null {
  try {
    const payload = decodeJwt(token);
    if (!payload) {
      console.warn('Could not decode JWT token for tenant extraction');
      return null;
    }

    // First check if tenant is explicitly in the claims
    if (payload.tenant) {
      console.log('Tenant found in JWT claims:', payload.tenant);
      return payload.tenant;
    }

    // Then check if it's in the subject (username@tenant)
    const subject = payload.sub;
    if (subject && subject.includes('@')) {
      const [, tenant] = subject.split('@', 2);
      console.log('Tenant extracted from JWT subject:', tenant);
      return tenant;
    }

    // Default to public if no tenant found
    console.log('No tenant found in JWT token, defaulting to public');
    return 'public';
  } catch (error) {
    console.error('Error extracting tenant from token:', error);
    return 'public'; // Default to public on error
  }
}

/**
 * Extract username from a JWT token
 *
 * @param token JWT token string
 * @returns Username without tenant part
 */
export function getUsernameFromToken(token: string): string | null {
  try {
    const payload = decodeJwt(token);
    if (!payload) return null;

    const subject = payload.sub;
    if (!subject) return null;

    // Remove tenant part if present
    if (subject.includes('@')) {
      return subject.split('@', 2)[0];
    }

    return subject;
  } catch (error) {
    console.error('Error extracting username from token:', error);
    return null;
  }
}

/**
 * Check if a JWT token is expired
 *
 * @param token JWT token string
 * @returns true if token is expired, false otherwise
 */
export function isTokenExpired(token: string): boolean {
  try {
    const payload = decodeJwt(token);
    if (!payload) return true;

    const now = Math.floor(Date.now() / 1000);
    return payload.exp < now;
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true;
  }
}

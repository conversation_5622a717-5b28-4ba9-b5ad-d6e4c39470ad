package com.cms.controller;

import com.cms.dto.ComponentFieldDTO;
import com.cms.entity.ComponentField;
import com.cms.exception.ForeignKeyViolationException;
import com.cms.exception.NoContentException;
import com.cms.exception.ResourceNotFoundException;
import com.cms.mapper.ComponentFieldMapper;
import com.cms.service.ComponentFieldService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/component-fields")
@RequiredArgsConstructor
@Tag(name = "Component Field Management", description = "Component Field Management API")
public class ComponentFieldController {

    private final ComponentFieldService componentFieldService;
    private final ComponentFieldMapper componentFieldMapper;

    @GetMapping("/getAll")
    @Operation(
        summary = "Get all component fields",
        description = "Returns a list of all component fields",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "List of component fields retrieved successfully",
                content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ComponentFieldDTO.class),
                    examples = {
                        @ExampleObject(
                            value = "[{\"id\":100,\"component\":{\"id\":100},\"fieldType\":{\"id\":100,\"fieldTypeName\":\"text\",\"displayName\":\"Text Field\"},\"displayPreference\":1,\"additionalInformation\":\"Example field description\"}]"
                        )
                    }
                )
            ),
            @ApiResponse(
                responseCode = "204",
                description = "No component fields found",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<List<ComponentFieldDTO>> getAllComponentFields() {
        List<ComponentField> fields = componentFieldService.getAllComponentFields();
        if (fields.isEmpty()) {
            throw new NoContentException("No component fields found");
        }
        List<ComponentFieldDTO> fieldDTOs = componentFieldMapper.toDTOList(fields);
        return ResponseEntity.ok(fieldDTOs);
    }

    @GetMapping("/getById/{id}")
    @Operation(
        summary = "Get component field by ID",
        description = "Returns a component field by its ID",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "Component field retrieved successfully",
                content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ComponentFieldDTO.class),
                    examples = {
                        @ExampleObject(
                            value = "{\"id\":100,\"component\":{\"id\":100},\"fieldType\":{\"id\":100,\"fieldTypeName\":\"text\",\"displayName\":\"Text Field\"},\"displayPreference\":1,\"additionalInformation\":\"Example field description\"}"
                        )
                    }
                )
            ),
            @ApiResponse(
                responseCode = "404",
                description = "Component field not found",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<ComponentFieldDTO> getComponentFieldById(@PathVariable Integer id) {
        ComponentField field = componentFieldService.getComponentFieldById(id);
        if (field == null) {
            throw new ResourceNotFoundException("Component field not found with id: " + id);
        }
        ComponentFieldDTO fieldDTO = componentFieldMapper.toDTO(field);
        return ResponseEntity.ok(fieldDTO);
    }

    @GetMapping("/getByComponentId/{componentId}")
    @Operation(summary = "Get component fields by component ID", description = "Returns a list of component fields for a specific component")
    public ResponseEntity<List<ComponentFieldDTO>> getComponentFieldsByComponentId(@PathVariable Integer componentId) {
        List<ComponentField> fields = componentFieldService.getComponentFieldsByComponentId(componentId);
        if (fields.isEmpty()) {
            throw new NoContentException("No fields found for component with ID: " + componentId);
        }
        List<ComponentFieldDTO> fieldDTOs = componentFieldMapper.toDTOList(fields);
        return ResponseEntity.ok(fieldDTOs);
    }

    @GetMapping("/getNextId")
    @Operation(summary = "Get next available ID", description = "Returns the next available ID for a component field")
    public ResponseEntity<Map<String, Integer>> getNextId() {
        Integer nextId = componentFieldService.getNextAvailableId();
        Map<String, Integer> response = new HashMap<>();
        response.put("nextId", nextId);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/create")
    @Operation(
        summary = "Create a new component field",
        description = "Creates a new component field",
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "Component field object to be created",
            content = @Content(
                mediaType = "application/json",
                examples = {
                    @ExampleObject(
                        name = "Standard Component Field",
                        summary = "Standard component field creation example",
                        value = "{\"component\":{\"id\":100},\"fieldType\":{\"id\":100},\"displayPreference\":1,\"additionalInformation\":\"Example field description\"}"
                    )
                },
                schema = @Schema(implementation = ComponentField.class)
            )
        ),
        responses = {
            @ApiResponse(
                responseCode = "201",
                description = "Component field created successfully",
                content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ComponentFieldDTO.class)
                )
            ),
            @ApiResponse(
                responseCode = "400",
                description = "Invalid input or foreign key constraint violation (component or field type does not exist)",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<ComponentFieldDTO> createComponentField(@Valid @RequestBody ComponentField componentField) {
        try {
            ComponentField createdField = componentFieldService.createComponentField(componentField);
            ComponentFieldDTO fieldDTO = componentFieldMapper.toDTO(createdField);
            return new ResponseEntity<>(fieldDTO, HttpStatus.CREATED);
        } catch (ForeignKeyViolationException ex) {
            // Return 400 Bad Request for foreign key violations
            throw ex; // Let the global exception handler handle it with 400 status
        }
    }

    @PutMapping("/update/{id}")
    @Operation(
        summary = "Update a component field",
        description = "Updates an existing component field",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "Component field updated successfully",
                content = @Content(mediaType = "application/json")
            ),
            @ApiResponse(
                responseCode = "400",
                description = "Invalid input or foreign key constraint violation (component or field type does not exist)",
                content = @Content(mediaType = "application/json")
            ),
            @ApiResponse(
                responseCode = "404",
                description = "Component field not found",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<ComponentFieldDTO> updateComponentField(@PathVariable Integer id, @Valid @RequestBody ComponentField componentField) {
        try {
            ComponentField updatedField = componentFieldService.updateComponentField(id, componentField);
            ComponentFieldDTO fieldDTO = componentFieldMapper.toDTO(updatedField);
            return ResponseEntity.ok(fieldDTO);
        } catch (ForeignKeyViolationException ex) {
            // Return 400 Bad Request for foreign key violations
            throw ex; // Let the global exception handler handle it with 400 status
        }
    }

    @DeleteMapping("/deleteById/{id}")
    @Operation(summary = "Delete a component field", description = "Deletes a component field by its ID")
    public ResponseEntity<Void> deleteComponentField(@PathVariable Integer id) {
        componentFieldService.deleteComponentField(id);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/reorderFields/{componentId}")
    @Operation(
        summary = "Reorder component fields",
        description = "Updates the display preferences of component fields based on the provided order",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "Fields reordered successfully",
                content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ComponentFieldDTO.class),
                    examples = {
                        @ExampleObject(
                            value = "[{\"id\":100,\"component\":{\"id\":100},\"fieldType\":{\"id\":100,\"fieldTypeName\":\"text\",\"displayName\":\"Text Field\"},\"displayPreference\":10}]"
                        )
                    }
                )
            ),
            @ApiResponse(
                responseCode = "404",
                description = "Component not found",
                content = @Content
            )
        }
    )
    public ResponseEntity<List<ComponentFieldDTO>> reorderFields(
            @PathVariable Integer componentId,
            @RequestBody Map<String, List<Integer>> requestBody) {

        List<Integer> fieldIds = requestBody.get("fieldIds");
        if (fieldIds == null || fieldIds.isEmpty()) {
            return ResponseEntity.badRequest().build();
        }

        List<ComponentField> reorderedFields = componentFieldService.reorderComponentFields(componentId, fieldIds);
        List<ComponentFieldDTO> result = reorderedFields.stream()
                .map(componentFieldMapper::toDTO)
                .toList();

        return ResponseEntity.ok(result);
    }
}

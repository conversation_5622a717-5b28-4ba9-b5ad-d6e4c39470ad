.component-badge {
  display: inline-block;
  margin-right: 8px;
  margin-bottom: 8px;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  background-color: #f0f5ff;
  border: 1px solid #d6e4ff;
  color: #1890ff;
}

.component-badge.repeatable {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #52c41a;
}

.component-relationship-container {
  margin-top: 8px;
}

.text-muted {
  color: rgba(0, 0, 0, 0.45);
}

.small {
  font-size: 12px;
}

.component-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.component-card:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.component-card .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 0 16px;
}

.component-card .ant-card-body {
  padding: 16px;
}

.component-actions {
  display: flex;
  justify-content: flex-end;
}

.component-actions button {
  margin-left: 8px;
}

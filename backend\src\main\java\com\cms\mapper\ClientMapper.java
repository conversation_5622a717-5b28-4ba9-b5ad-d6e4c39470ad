package com.cms.mapper;

import com.cms.dto.ClientDTO;
import com.cms.entity.Client;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for converting between Client entity and ClientDTO
 */
@Component
public class ClientMapper {

    /**
     * Convert a Client entity to a ClientDTO
     * 
     * @param entity Client entity
     * @return ClientDTO
     */
    public ClientDTO toDTO(Client entity) {
        if (entity == null) {
            return null;
        }
        
        ClientDTO dto = new ClientDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setCreatedBy(entity.getCreatedBy());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setModifiedBy(entity.getModifiedBy());
        dto.setModifiedAt(entity.getModifiedAt());
        
        return dto;
    }
    
    /**
     * Convert a list of Client entities to a list of ClientDTOs
     * 
     * @param entities List of Client entities
     * @return List of ClientDTOs
     */
    public List<ClientDTO> toDTOList(List<Client> entities) {
        if (entities == null) {
            return null;
        }
        
        return entities.stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }
}

Write-Host "Testing API endpoints..."

# Test the login endpoint
Write-Host "`nTesting login endpoint..."
try {
    $loginResponse = Invoke-RestMethod -Method POST -Uri "http://localhost:8071/api/auth/login" -Body (@{
        username = "admin"
        password = "admin"
    } | ConvertTo-Json) -ContentType "application/json" -ErrorAction Stop
    
    Write-Host "Login successful!"
    Write-Host "Response: $($loginResponse | ConvertTo-Json -Depth 5)"
} catch {
    Write-Host "Login failed with error: $_"
    Write-Host "Status code: $($_.Exception.Response.StatusCode.value__)"
    
    try {
        $errorDetails = $_.ErrorDetails.Message | ConvertFrom-Json
        Write-Host "Error details: $($errorDetails | ConvertTo-Json -Depth 5)"
    } catch {
        Write-Host "Error details not available or not in JSON format"
    }
}

# Test the register endpoint
Write-Host "`nTesting register endpoint..."
try {
    $randomSuffix = Get-Random
    $registerResponse = Invoke-RestMethod -Method POST -Uri "http://localhost:8071/api/auth/register" -Body (@{
        username = "testuser$randomSuffix"
        email = "testuser$<EMAIL>"
        password = "password123"
    } | ConvertTo-Json) -ContentType "application/json" -ErrorAction Stop
    
    Write-Host "Registration successful!"
    Write-Host "Response: $registerResponse"
} catch {
    Write-Host "Registration failed with error: $_"
    Write-Host "Status code: $($_.Exception.Response.StatusCode.value__)"
    
    try {
        $errorDetails = $_.ErrorDetails.Message | ConvertFrom-Json
        Write-Host "Error details: $($errorDetails | ConvertTo-Json -Depth 5)"
    } catch {
        Write-Host "Error details not available or not in JSON format"
        Write-Host "Raw error: $($_.ErrorDetails.Message)"
    }
}

# Test the register endpoint with a different URL
Write-Host "`nTesting register endpoint with /auth/signup URL..."
try {
    $randomSuffix = Get-Random
    $registerResponse = Invoke-RestMethod -Method POST -Uri "http://localhost:8071/api/auth/signup" -Body (@{
        username = "testuser$randomSuffix"
        email = "testuser$<EMAIL>"
        password = "password123"
    } | ConvertTo-Json) -ContentType "application/json" -ErrorAction Stop
    
    Write-Host "Registration successful!"
    Write-Host "Response: $registerResponse"
} catch {
    Write-Host "Registration failed with error: $_"
    Write-Host "Status code: $($_.Exception.Response.StatusCode.value__)"
    
    try {
        $errorDetails = $_.ErrorDetails.Message | ConvertFrom-Json
        Write-Host "Error details: $($errorDetails | ConvertTo-Json -Depth 5)"
    } catch {
        Write-Host "Error details not available or not in JSON format"
        Write-Host "Raw error: $($_.ErrorDetails.Message)"
    }
}

Write-Host "`nAPI testing completed."

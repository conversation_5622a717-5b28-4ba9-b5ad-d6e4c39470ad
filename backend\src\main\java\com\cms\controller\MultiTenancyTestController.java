package com.cms.controller;

import com.cms.config.TenantContextHolder;
import com.cms.entity.Tenant;
import com.cms.entity.User;
import com.cms.service.TenantService;
import com.cms.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Controller to test multi-tenancy functionality.
 * This controller should be used for testing purposes only and should be secured or removed in production.
 */
@RestController
@RequestMapping("/test/multi-tenancy")
@RequiredArgsConstructor
@Slf4j
public class MultiTenancyTestController {

    private final TenantService tenantService;
    private final UserService userService;
    private final PasswordEncoder passwordEncoder;

    /**
     * Test creating a user with the same username in different tenants.
     * This endpoint creates two tenants and a user with the same username in each tenant.
     */
    @PostMapping("/test-username-uniqueness")
    public ResponseEntity<Map<String, Object>> testUsernameUniqueness(
            @RequestParam String username,
            @RequestParam String tenant1,
            @RequestParam String tenant2) {
        
        log.info("Testing username uniqueness with username: {}, tenant1: {}, tenant2: {}", 
                username, tenant1, tenant2);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // Create tenant 1 if it doesn't exist
            if (!tenantService.getTenantBySchemaName(tenant1).isPresent()) {
                Tenant newTenant1 = new Tenant();
                newTenant1.setName(tenant1);
                newTenant1.setSchemaName(tenant1);
                newTenant1.setDescription("Test tenant 1");
                newTenant1.setIsActive(true);
                tenantService.createTenant(newTenant1);
                result.put("tenant1Created", true);
            } else {
                result.put("tenant1Created", false);
            }
            
            // Create tenant 2 if it doesn't exist
            if (!tenantService.getTenantBySchemaName(tenant2).isPresent()) {
                Tenant newTenant2 = new Tenant();
                newTenant2.setName(tenant2);
                newTenant2.setSchemaName(tenant2);
                newTenant2.setDescription("Test tenant 2");
                newTenant2.setIsActive(true);
                tenantService.createTenant(newTenant2);
                result.put("tenant2Created", true);
            } else {
                result.put("tenant2Created", false);
            }
            
            // Create user in tenant 1
            User user1 = new User();
            user1.setUsername(username);
            user1.setEmail(username + "@" + tenant1 + ".com");
            user1.setPassword(passwordEncoder.encode("password"));
            user1.setIsActive(true);
            
            // Set tenant context to tenant 1
            TenantContextHolder.setTenantId(tenant1);
            
            // Check if user already exists in tenant 1
            boolean user1Exists = userService.existsByUsername(username);
            
            if (!user1Exists) {
                // Create user in tenant 1
                userService.createUser(user1);
                result.put("user1Created", true);
            } else {
                result.put("user1Created", false);
                result.put("user1ExistsInTenant1", true);
            }
            
            // Create user in tenant 2
            User user2 = new User();
            user2.setUsername(username);
            user2.setEmail(username + "@" + tenant2 + ".com");
            user2.setPassword(passwordEncoder.encode("password"));
            user2.setIsActive(true);
            
            // Set tenant context to tenant 2
            TenantContextHolder.setTenantId(tenant2);
            
            // Check if user already exists in tenant 2
            boolean user2Exists = userService.existsByUsername(username);
            
            if (!user2Exists) {
                // Create user in tenant 2
                userService.createUser(user2);
                result.put("user2Created", true);
            } else {
                result.put("user2Created", false);
                result.put("user2ExistsInTenant2", true);
            }
            
            // Verify that the users exist in their respective tenants
            boolean user1ExistsInTenant1 = userService.existsByUsername(username, tenant1);
            boolean user2ExistsInTenant2 = userService.existsByUsername(username, tenant2);
            boolean user1ExistsInTenant2 = userService.existsByUsername(username, tenant2);
            boolean user2ExistsInTenant1 = userService.existsByUsername(username, tenant1);
            
            result.put("user1ExistsInTenant1", user1ExistsInTenant1);
            result.put("user2ExistsInTenant2", user2ExistsInTenant2);
            result.put("user1ExistsInTenant2", user1ExistsInTenant2);
            result.put("user2ExistsInTenant1", user2ExistsInTenant1);
            
            result.put("success", true);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error testing username uniqueness: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        } finally {
            // Clear tenant context
            TenantContextHolder.clear();
        }
    }
}

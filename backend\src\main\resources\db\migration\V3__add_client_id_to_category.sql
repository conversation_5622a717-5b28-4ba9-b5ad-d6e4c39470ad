-- Add client_id column to category table
ALTER TABLE category ADD COLUMN client_id INTEGER;
ALTER TABLE category ADD COLUMN

-- Add foreign key constraint
ALTER TABLE category ADD CONSTRAINT fk_category_client
FOREIGN KEY (client_id) REFERENCES clients(id);

-- Create index for better performance
CREATE INDEX idx_category_client_id ON category(client_id);

-- Update schema_consolidated.sql to include the new column in future installations
-- Note: This doesn't actually modify the file, it's just a reminder
COMMENT ON COLUMN category.client_id IS 'Reference to the client that owns this category';

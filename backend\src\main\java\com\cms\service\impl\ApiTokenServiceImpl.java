package com.cms.service.impl;

import com.cms.entity.ApiToken;
import com.cms.entity.User;
import com.cms.exception.ResourceNotFoundException;
import com.cms.repository.ApiTokenRepository;
import com.cms.service.ApiTokenService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class ApiTokenServiceImpl implements ApiTokenService {

    private final ApiTokenRepository apiTokenRepository;
    private static final SecureRandom secureRandom = new SecureRandom();
    
    @Override
    @Transactional
    public ApiToken generateToken(User user, String name, String description, int expirationDays) {
        // Generate a secure random token
        byte[] randomBytes = new byte[32]; // 256 bits
        secureRandom.nextBytes(randomBytes);
        String tokenValue = "cms_" + Base64.getUrlEncoder().withoutPadding().encodeToString(randomBytes);
        
        // Create the token entity
        ApiToken token = new ApiToken();
        token.setUser(user);
        token.setName(name);
        token.setDescription(description);
        token.setTokenValue(tokenValue);
        token.setExpiresAt(LocalDateTime.now().plusDays(expirationDays));
        token.setIsActive(true);
        
        // Save and return the token
        ApiToken savedToken = apiTokenRepository.save(token);
        log.info("Generated new API token for user: {}, token name: {}", user.getUsername(), name);
        return savedToken;
    }

    @Override
    @Transactional(readOnly = true)
    public List<ApiToken> getTokensByUser(User user) {
        return apiTokenRepository.findByUser(user);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ApiToken> getActiveTokensByUser(User user) {
        return apiTokenRepository.findByUserAndIsActiveTrue(user);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<ApiToken> findByTokenValue(String tokenValue) {
        return apiTokenRepository.findByTokenValue(tokenValue);
    }

    @Override
    @Transactional
    public ApiToken revokeToken(Long tokenId) {
        ApiToken token = apiTokenRepository.findById(tokenId)
                .orElseThrow(() -> new ResourceNotFoundException("Token not found with id: " + tokenId));
        
        token.setIsActive(false);
        ApiToken updatedToken = apiTokenRepository.save(token);
        log.info("Revoked API token with ID: {}", tokenId);
        return updatedToken;
    }

    @Override
    @Transactional
    public ApiToken updateLastUsed(ApiToken token) {
        token.setLastUsedAt(LocalDateTime.now());
        return apiTokenRepository.save(token);
    }

    @Override
    @Transactional
    public int cleanupExpiredTokens() {
        List<ApiToken> expiredTokens = apiTokenRepository.findByExpiresAtBefore(LocalDateTime.now());
        if (!expiredTokens.isEmpty()) {
            apiTokenRepository.deleteAll(expiredTokens);
            log.info("Cleaned up {} expired API tokens", expiredTokens.size());
        }
        return expiredTokens.size();
    }
}

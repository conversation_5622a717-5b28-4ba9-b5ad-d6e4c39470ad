
import React from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import {
  Boxes,
  Database,
  Layers,
  LayoutDashboard,
  Settings
} from 'lucide-react';
import { useAuthStore } from '@/lib/store';
import { toast } from 'sonner';
import { authApi } from '@/lib/api';
import { Header } from './Header';

import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarFooter,
  SidebarTrigger,
  SidebarProvider
} from '@/components/ui/sidebar';

interface CMSLayoutProps {
  children: React.ReactNode;
}

export function CMSLayout({ children }: CMSLayoutProps) {
  const { isAuthenticated, user, token, logout } = useAuthStore();
  const navigate = useNavigate();
  const location = useLocation();
  const [isLoading, setIsLoading] = React.useState(true);
  const [isAuthed, setIsAuthed] = React.useState(false);

  // Handle authentication check
  React.useEffect(() => {
    const checkAuth = async () => {
      console.log('Checking auth status...');

      // Skip auth check for login/register pages
      if (location.pathname.includes('/login') || location.pathname.includes('/register')) {
        setIsLoading(false);
        return;
      }

      // Debug: Check localStorage directly
      const localStorageToken = localStorage.getItem('cms_token');
      console.log('Token in localStorage:', localStorageToken ? 'exists' : 'not found');
      console.log('Token in store:', token ? 'exists' : 'not found');
      console.log('isAuthenticated flag:', isAuthenticated);

      // Check if we have a token in store
      if (!token) {
        console.log('No token in store, redirecting to login');
        navigate('/login');
        setIsLoading(false);
        return;
      }

      // We have a token, validate format
      try {
        // Simple validation - check if token looks like JWT (has two dots)
        if (typeof token !== 'string' || !token.includes('.') || token.split('.').length !== 3) {
          console.log('Invalid token format, redirecting to login');
          logout();
          localStorage.clear();
          navigate('/login');
          return;
        }

        console.log('Token is valid format, user is authenticated');
        setIsAuthed(true);
      } catch (error) {
        console.error('Auth validation error:', error);
        logout();
        localStorage.clear();
        navigate('/login');
      } finally {
        setIsLoading(false);
      }
    };

    // Add a small delay to prevent race conditions during login
    const timeoutId = setTimeout(checkAuth, 100);

    return () => clearTimeout(timeoutId);
  }, [token, logout, navigate, location.pathname, isAuthenticated]);

  // Authentication check is now handled in the component

  // Navigation items
  const navItems = [
    {
      title: 'Dashboard',
      icon: LayoutDashboard,
      path: '/dashboard',
    },
    // {
    //   title: 'Collections',
    //   icon: Database,
    //   path: '/content-types',
    // },
      {
      title: 'Clients',
      icon: Database,
      path: '/clients',
    },
    {
      title: 'Components',
      icon: Layers,
      path: '/components',
    },
    {
      title: 'Media Library',
      icon: Boxes,
      path: '/media-library',
    },
    {
      title: 'Settings',
      icon: Settings,
      path: '/settings',
    },
  ];

  // Skip layout for auth pages
  if (location.pathname.includes('/login') || location.pathname.includes('/register')) {
    return <>{children}</>;
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  // Redirect if not authenticated
  if (!isAuthed) {
    return null;
  }

  return (
    <SidebarProvider>
      <div className="flex h-screen w-full">
        <Sidebar>
          <SidebarHeader className="flex h-14 items-center  mb-8 bg-white">
            <Link to="/dashboard" className="flex items-center  gap-2 font-semibold">
              <img src="/images/cloud-logo.png" alt="CMS Logo" className="h-12 w-full mb-4" />
            </Link>
          </SidebarHeader>
          <SidebarContent className="p-2">
            <SidebarMenu>
              {navItems.map((item) => (
                <SidebarMenuItem key={item.path}>
                  <SidebarMenuButton asChild>
                    <Link
                      to={item.path}
                      className={`w-full ${
                        location.pathname.includes(item.path) ? 'bg-sidebar-accent' : ''
                      }`}
                    >
                      <item.icon className="mr-2 h-5 w-5" />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>

                  {/* Render sub-items if they exist */}
                  {item.subItems && item.subItems.length > 0 && (
                    <div className="ml-7 mt-1 space-y-1 border-l pl-2">
                      {item.subItems.map((subItem) => (
                        <Link
                          key={subItem.path}
                          to={subItem.path}
                          className={`flex items-center rounded-md px-3 py-1.5 text-sm ${location.pathname === subItem.path ? 'bg-sidebar-accent font-medium' : 'hover:bg-muted/50'}`}
                        >
                          {subItem.title}
                        </Link>
                      ))}
                    </div>
                  )}
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarContent>
          <SidebarFooter className="mt-auto p-4 border-t">
            <div className="flex items-center justify-center">
              <span className="text-sm text-white">© {new Date().getFullYear()} R-CMS</span>
            </div>
          </SidebarFooter>
        </Sidebar>
        <div className="flex-1 overflow-auto">
          <Header />
          <main className="p-6">{children}</main>
        </div>
      </div>
    </SidebarProvider>
  );
}

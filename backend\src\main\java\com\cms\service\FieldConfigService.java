package com.cms.service;

import com.cms.entity.FieldConfig;
import com.cms.entity.FieldType;

import java.util.List;
import java.util.Optional;

public interface FieldConfigService {
    List<FieldConfig> getAllFieldConfigs();
    List<FieldConfig> getActiveFieldConfigs();
    List<FieldConfig> getFieldConfigsByFieldType(FieldType fieldType);
    List<FieldConfig> getFieldConfigsByFieldTypeId(Integer fieldTypeId);
    Optional<FieldConfig> getFieldConfigById(Integer id);
    FieldConfig createFieldConfig(FieldConfig fieldConfig);
    FieldConfig updateFieldConfig(Integer id, FieldConfig fieldConfig);
    void deleteFieldConfig(Integer id);
    boolean existsByConfigName(String configName);
}

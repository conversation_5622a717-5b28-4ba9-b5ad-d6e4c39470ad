package com.cms.service.impl;

import com.cms.entity.FieldConfig;
import com.cms.entity.FieldType;
import com.cms.exception.ForeignKeyViolationException;
import com.cms.exception.NullConstraintViolationException;
import com.cms.exception.ResourceNotFoundException;
import com.cms.exception.UniqueConstraintViolationException;
import com.cms.repository.FieldConfigRepository;
import com.cms.repository.FieldTypeRepository;
import com.cms.repository.ConfigTypeRepository;
import com.cms.service.FieldConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class FieldConfigServiceImpl implements FieldConfigService {

    private final FieldConfigRepository fieldConfigRepository;
    private final FieldTypeRepository fieldTypeRepository;
    private final ConfigTypeRepository configTypeRepository;

    @Override
    public List<FieldConfig> getAllFieldConfigs() {
        List<FieldConfig> fieldConfigs = fieldConfigRepository.findAll();
        log.debug("Retrieved {} field configs", fieldConfigs.size());
        return fieldConfigs;
    }

    @Override
    public List<FieldConfig> getActiveFieldConfigs() {
        List<FieldConfig> activeFieldConfigs = fieldConfigRepository.findByIsActiveTrue();
        log.debug("Retrieved {} active field configs", activeFieldConfigs.size());
        return activeFieldConfigs;
    }

    @Override
    public List<FieldConfig> getFieldConfigsByFieldType(FieldType fieldType) {
        if (fieldType == null) {
            throw new NullConstraintViolationException("fieldType");
        }

        List<FieldConfig> fieldConfigs = fieldConfigRepository.findByFieldTypeAndIsActiveTrue(fieldType);
        log.debug("Retrieved {} field configs for field type: {}", fieldConfigs.size(), fieldType.getFieldTypeName());
        return fieldConfigs;
    }

    @Override
    public List<FieldConfig> getFieldConfigsByFieldTypeId(Integer fieldTypeId) {
        if (fieldTypeId == null) {
            throw new NullConstraintViolationException("fieldTypeId");
        }

        // Verify field type exists
        if (!fieldTypeRepository.existsById(fieldTypeId)) {
            log.debug("Field type not found with id: {}", fieldTypeId);
            return new ArrayList<>(); // Return empty list instead of throwing exception
        }

        List<FieldConfig> fieldConfigs = fieldConfigRepository.findByFieldTypeIdAndIsActiveTrue(fieldTypeId);
        log.debug("Retrieved {} field configs for field type ID: {}", fieldConfigs.size(), fieldTypeId);
        return fieldConfigs;
    }

    @Override
    public Optional<FieldConfig> getFieldConfigById(Integer id) {
        return fieldConfigRepository.findById(id);
    }

    @Override
    @Transactional
    public FieldConfig createFieldConfig(FieldConfig fieldConfig) {
        // Validate required fields
        if (fieldConfig.getConfigName() == null || fieldConfig.getConfigName().trim().isEmpty()) {
            throw new NullConstraintViolationException("configName");
        }

        if (fieldConfig.getValueType() == null || fieldConfig.getValueType().trim().isEmpty()) {
            throw new NullConstraintViolationException("valueType");
        }

        // Check for unique constraint violations
        if (existsByConfigName(fieldConfig.getConfigName())) {
            throw new UniqueConstraintViolationException("configName", fieldConfig.getConfigName());
        }

        // Validate field type if provided
        if (fieldConfig.getFieldType() != null && fieldConfig.getFieldType().getId() != null) {
            if (!fieldTypeRepository.existsById(fieldConfig.getFieldType().getId())) {
                throw new ForeignKeyViolationException("FieldType", "id", fieldConfig.getFieldType().getId());
            }
        }

        // Validate config type if provided
        if (fieldConfig.getConfigType() != null && fieldConfig.getConfigType().getId() != null) {
            if (!configTypeRepository.existsById(fieldConfig.getConfigType().getId())) {
                throw new ForeignKeyViolationException("ConfigType", "id", fieldConfig.getConfigType().getId());
            }
        }

        // Set default values if not provided
        if (fieldConfig.getIsActive() == null) {
            fieldConfig.setIsActive(true);
        }

        // Save the field config
        FieldConfig savedFieldConfig = fieldConfigRepository.save(fieldConfig);
        log.info("Created new field config with ID: {}", savedFieldConfig.getId());
        return savedFieldConfig;
    }

    @Override
    @Transactional
    public FieldConfig updateFieldConfig(Integer id, FieldConfig fieldConfig) {
        // Verify the field config exists
        FieldConfig existingFieldConfig = fieldConfigRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("FieldConfig not found with id: " + id));

        // Validate required fields
        if (fieldConfig.getConfigName() == null || fieldConfig.getConfigName().trim().isEmpty()) {
            throw new NullConstraintViolationException("configName");
        }

        if (fieldConfig.getValueType() == null || fieldConfig.getValueType().trim().isEmpty()) {
            throw new NullConstraintViolationException("valueType");
        }

        // Check for unique constraint violations (only if name has changed)
        if (!fieldConfig.getConfigName().equals(existingFieldConfig.getConfigName()) &&
                existsByConfigName(fieldConfig.getConfigName())) {
            throw new UniqueConstraintViolationException("configName", fieldConfig.getConfigName());
        }

        // Validate field type if provided
        if (fieldConfig.getFieldType() != null && fieldConfig.getFieldType().getId() != null) {
            if (!fieldTypeRepository.existsById(fieldConfig.getFieldType().getId())) {
                throw new ForeignKeyViolationException("FieldType", "id", fieldConfig.getFieldType().getId());
            }
            existingFieldConfig.setFieldType(fieldConfig.getFieldType());
        }

        // Validate config type if provided
        if (fieldConfig.getConfigType() != null && fieldConfig.getConfigType().getId() != null) {
            if (!configTypeRepository.existsById(fieldConfig.getConfigType().getId())) {
                throw new ForeignKeyViolationException("ConfigType", "id", fieldConfig.getConfigType().getId());
            }
            existingFieldConfig.setConfigType(fieldConfig.getConfigType());
        }

        // Update fields
        existingFieldConfig.setConfigName(fieldConfig.getConfigName());
        existingFieldConfig.setValueType(fieldConfig.getValueType());

        if (fieldConfig.getIsActive() != null) {
            existingFieldConfig.setIsActive(fieldConfig.getIsActive());
        }

        // Save the updated field config
        FieldConfig updatedFieldConfig = fieldConfigRepository.save(existingFieldConfig);
        log.info("Updated field config with ID: {}", updatedFieldConfig.getId());
        return updatedFieldConfig;
    }

    @Override
    @Transactional
    public void deleteFieldConfig(Integer id) {
        // Verify the field config exists
        FieldConfig fieldConfig = fieldConfigRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("FieldConfig not found with id: " + id));

        // Soft delete by setting isActive to false
        fieldConfig.setIsActive(false);
        fieldConfigRepository.save(fieldConfig);

        log.info("Soft deleted field config with ID: {}", id);
    }

    @Override
    public boolean existsByConfigName(String configName) {
        return fieldConfigRepository.existsByConfigName(configName);
    }
}

package com.cms.controller;

import com.cms.dto.ComponentFieldConfigDTO;
import com.cms.entity.ComponentFieldConfig;
import com.cms.exception.ForeignKeyViolationException;
import com.cms.exception.NoContentException;
import com.cms.exception.ResourceNotFoundException;
import com.cms.mapper.ComponentFieldConfigMapper;
import com.cms.service.ComponentFieldConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/component-field-configs")
@RequiredArgsConstructor
@Tag(name = "Component Field Config Management", description = "Component Field Config Management API")
public class ComponentFieldConfigController {

    private final ComponentFieldConfigService componentFieldConfigService;
    private final ComponentFieldConfigMapper componentFieldConfigMapper;

    @GetMapping("/getAll")
    @Operation(
        summary = "Get all component field configs",
        description = "Returns a list of all component field configs",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "List of component field configs retrieved successfully",
                content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ComponentFieldConfig.class),
                    examples = {
                        @ExampleObject(
                            value = "[{\"id\":100,\"componentField\":{\"id\":100},\"fieldConfig\":{\"id\":100,\"configName\":\"Required\"},\"fieldConfigValue\":\"true\"}]"
                        )
                    }
                )
            ),
            @ApiResponse(
                responseCode = "204",
                description = "No component field configs found",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<List<ComponentFieldConfigDTO>> getAllComponentFieldConfigs() {
        List<ComponentFieldConfig> configs = componentFieldConfigService.getAllComponentFieldConfigs();
        if (configs.isEmpty()) {
            throw new NoContentException("No component field configs found");
        }
        return ResponseEntity.ok(componentFieldConfigMapper.toDTOList(configs));
    }

    @GetMapping("/getByFieldId/{componentFieldId}")
    @Operation(summary = "Get component field configs by component field ID", description = "Returns a list of component field configs for a specific component field")
    public ResponseEntity<List<ComponentFieldConfigDTO>> getComponentFieldConfigsByComponentFieldId(
            @PathVariable Integer componentFieldId) {
        List<ComponentFieldConfig> configs = componentFieldConfigService.getComponentFieldConfigsByComponentFieldId(componentFieldId);
        if (configs.isEmpty()) {
            throw new NoContentException("No configs found for component field with ID: " + componentFieldId);
        }
        return ResponseEntity.ok(componentFieldConfigMapper.toDTOList(configs));
    }

    @GetMapping("/getById/{id}")
    @Operation(summary = "Get component field config by ID", description = "Returns a component field config by its ID")
    public ResponseEntity<ComponentFieldConfigDTO> getComponentFieldConfigById(@PathVariable Integer id) {
        ComponentFieldConfig config = componentFieldConfigService.getComponentFieldConfigById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Component field config not found with id: " + id));
        return ResponseEntity.ok(componentFieldConfigMapper.toDTO(config));
    }

    @PostMapping("/create")
    @Operation(
        summary = "Create a new component field config",
        description = "Creates a new component field config",
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "Component field config object to be created",
            content = @Content(
                mediaType = "application/json",
                examples = {
                    @ExampleObject(
                        name = "Standard Component Field Config",
                        summary = "Standard component field config creation example",
                        value = "{\"componentField\":{\"id\":100},\"fieldConfig\":{\"id\":100},\"fieldConfigValue\":\"true\"}"
                    )
                },
                schema = @Schema(implementation = ComponentFieldConfig.class)
            )
        ),
        responses = {
            @ApiResponse(
                responseCode = "201",
                description = "Component field config created successfully",
                content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ComponentFieldConfigDTO.class)
                )
            ),
            @ApiResponse(
                responseCode = "400",
                description = "Invalid input or foreign key constraint violation (component field or field config does not exist)",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<ComponentFieldConfigDTO> createComponentFieldConfig(
            @Valid @RequestBody ComponentFieldConfig componentFieldConfig) {
        try {
            ComponentFieldConfig createdConfig = componentFieldConfigService.createComponentFieldConfig(componentFieldConfig);

            // Convert to DTO to include all related entities
            ComponentFieldConfigDTO dto = componentFieldConfigMapper.toDTO(createdConfig);

            return new ResponseEntity<>(dto, HttpStatus.CREATED);
        } catch (ForeignKeyViolationException ex) {
            // Return 400 Bad Request for foreign key violations
            throw ex; // Let the global exception handler handle it with 400 status
        }
    }

    @PostMapping("/createBulk")
    @Operation(
        summary = "Create multiple component field configs",
        description = "Creates multiple component field configs in a single request",
        responses = {
            @ApiResponse(
                responseCode = "201",
                description = "Component field configs created successfully",
                content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ComponentFieldConfigDTO.class, type = "array")
                )
            ),
            @ApiResponse(
                responseCode = "400",
                description = "Invalid input or foreign key constraint violation (component field or field config does not exist)",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<List<ComponentFieldConfigDTO>> createComponentFieldConfigs(
            @Valid @RequestBody List<ComponentFieldConfig> componentFieldConfigs) {
        try {
            List<ComponentFieldConfig> createdConfigs = componentFieldConfigService.createComponentFieldConfigs(componentFieldConfigs);

            // Convert to DTOs to include all related entities
            List<ComponentFieldConfigDTO> dtos = componentFieldConfigMapper.toDTOList(createdConfigs);

            return ResponseEntity.status(HttpStatus.CREATED).body(dtos);
        } catch (ForeignKeyViolationException ex) {
            // Return 400 Bad Request for foreign key violations
            throw ex; // Let the global exception handler handle it with 400 status
        }
    }

    @PutMapping("/update/{id}")
    @Operation(summary = "Update a component field config", description = "Updates an existing component field config")
    public ResponseEntity<ComponentFieldConfigDTO> updateComponentFieldConfig(@PathVariable Integer id,
            @Valid @RequestBody ComponentFieldConfig componentFieldConfig) {
        ComponentFieldConfig updatedConfig = componentFieldConfigService.updateComponentFieldConfig(id, componentFieldConfig);
        return ResponseEntity.ok(componentFieldConfigMapper.toDTO(updatedConfig));
    }

    @DeleteMapping("/deleteById/{id}")
    @Operation(summary = "Delete a component field config", description = "Deletes a component field config by its ID")
    public ResponseEntity<Void> deleteComponentFieldConfig(@PathVariable Integer id) {
        componentFieldConfigService.deleteComponentFieldConfig(id);
        return ResponseEntity.noContent().build();
    }
}

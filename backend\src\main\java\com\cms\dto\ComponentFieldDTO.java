package com.cms.dto;

import com.cms.entity.ComponentFieldConfig;
import com.cms.entity.FieldType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ComponentFieldDTO {
    private Integer id;
    private Integer componentId;
    private String componentName;
    private String componentApiId;
    private FieldType fieldType;
    private Integer displayPreference;
    private String additionalInformation;
    private List<ComponentFieldConfig> configs;
    private String createdBy;
    private LocalDateTime createdAt;
    private String modifiedBy;
    private LocalDateTime modifiedAt;
}

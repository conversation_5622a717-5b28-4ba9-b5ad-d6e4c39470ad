package com.cms.service;

import com.cms.entity.Category;
import com.cms.entity.Client;
import com.cms.entity.CollectionListing;
import com.cms.repository.CategoryRepository;
import com.cms.repository.ClientRepository;
import com.cms.repository.CollectionListingRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class CollectionsByClientCategoryNameDebugTest {

    @Autowired
    private CollectionListingService collectionListingService;

    @Autowired
    private CollectionListingRepository collectionListingRepository;

    @Autowired
    private CategoryRepository categoryRepository;

    @Autowired
    private ClientRepository clientRepository;

    private Client testClient;
    private Category testCategory;
    private CollectionListing testCollection;

    @BeforeEach
    void setUp() {
        // Create test client
        testClient = new Client();
        testClient.setName("Test Client Debug");
        testClient = clientRepository.save(testClient);
        System.out.println("Created client: " + testClient.getName() + " with ID: " + testClient.getId());

        // Create test category
        testCategory = new Category();
        testCategory.setCategoryName("Test Category Debug");
        testCategory.setClient(testClient);
        testCategory = categoryRepository.save(testCategory);
        System.out.println("Created category: " + testCategory.getCategoryName() + " with ID: " + testCategory.getId());

        // Create test collection
        testCollection = new CollectionListing();
        testCollection.setCollectionName("Test Collection Debug");
        testCollection.setCollectionApiId("test_collection_debug");
        testCollection.setCategory(testCategory);
        testCollection = collectionListingRepository.save(testCollection);
        System.out.println("Created collection: " + testCollection.getCollectionName() + " with ID: " + testCollection.getId());
    }

    @Test
    void testDebugCollectionRetrieval() {
        System.out.println("\n=== DEBUG TEST START ===");
        
        // Test 1: Check if data was created correctly
        List<CollectionListing> allCollections = collectionListingRepository.findAll();
        System.out.println("Total collections in database: " + allCollections.size());
        
        for (CollectionListing col : allCollections) {
            System.out.println("Collection: " + col.getCollectionName() + 
                ", Client: " + (col.getCategory() != null && col.getCategory().getClient() != null ? col.getCategory().getClient().getName() : "NULL") +
                ", Category: " + (col.getCategory() != null ? col.getCategory().getCategoryName() : "NULL"));
        }

        // Test 2: Try to find by client name and category name
        System.out.println("\n--- Testing getCollectionsByClientNameAndCategoryNameWithDetails ---");
        List<CollectionListing> result = collectionListingService.getCollectionsByClientNameAndCategoryNameWithDetails(
            "Test Client Debug", "Test Category Debug");
        
        System.out.println("Result size: " + result.size());
        for (CollectionListing col : result) {
            System.out.println("Found collection: " + col.getCollectionName());
        }

        // Assertions
        assertEquals(1, result.size(), "Should find exactly 1 collection");
        assertEquals("Test Collection Debug", result.get(0).getCollectionName());
        
        System.out.println("=== DEBUG TEST END ===\n");
    }

    @Test
    void testDebugWithExactClientAndCategoryNames() {
        System.out.println("\n=== EXACT NAMES TEST START ===");
        
        // Use the exact names from your application
        String clientName = testClient.getName();
        String categoryName = testCategory.getCategoryName();
        
        System.out.println("Searching for client: '" + clientName + "' and category: '" + categoryName + "'");
        
        List<CollectionListing> result = collectionListingService.getCollectionsByClientNameAndCategoryNameWithDetails(
            clientName, categoryName);
        
        System.out.println("Result size: " + result.size());
        assertTrue(result.size() > 0, "Should find at least 1 collection");
        
        System.out.println("=== EXACT NAMES TEST END ===\n");
    }

    @Test
    void testDebugRepositoryDirectly() {
        System.out.println("\n=== REPOSITORY DIRECT TEST START ===");
        
        // Test repository methods directly
        List<CollectionListing> byClientName = collectionListingRepository.findByClientNameDebug("Test Client Debug");
        System.out.println("Collections by client name: " + byClientName.size());
        
        List<CollectionListing> byCategoryName = collectionListingRepository.findByCategoryNameDebug("Test Category Debug");
        System.out.println("Collections by category name: " + byCategoryName.size());
        
        List<CollectionListing> byBoth = collectionListingRepository.findByClientNameAndCategoryNameWithDetails(
            "Test Client Debug", "Test Category Debug");
        System.out.println("Collections by both: " + byBoth.size());
        
        // All should return 1
        assertEquals(1, byClientName.size());
        assertEquals(1, byCategoryName.size());
        assertEquals(1, byBoth.size());
        
        System.out.println("=== REPOSITORY DIRECT TEST END ===\n");
    }
}

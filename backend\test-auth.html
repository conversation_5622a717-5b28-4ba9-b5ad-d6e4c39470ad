<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .card {
            flex: 1;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        input {
            width: 100%;
            padding: 8px;
            margin: 5px 0 15px;
            box-sizing: border-box;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Authentication Test</h1>

    <div class="container">
        <div class="card">
            <h2>Register</h2>
            <div>
                <label for="reg-username">Username:</label>
                <input type="text" id="reg-username" value="newuser">
            </div>
            <div>
                <label for="reg-email">Email:</label>
                <input type="email" id="reg-email" value="<EMAIL>">
            </div>
            <div>
                <label for="reg-password">Password:</label>
                <input type="password" id="reg-password" value="password123">
            </div>
            <button onclick="register()">Register</button>
            <div>
                <h3>Response:</h3>
                <pre id="register-response"></pre>
            </div>
        </div>

        <div class="card">
            <h2>Login</h2>
            <div>
                <label for="login-username">Username:</label>
                <input type="text" id="login-username" value="testuser">
            </div>
            <div>
                <label for="login-password">Password:</label>
                <input type="password" id="login-password" value="password123">
            </div>
            <button onclick="login()">Login</button>
            <div>
                <h3>Response:</h3>
                <pre id="login-response"></pre>
            </div>
        </div>
    </div>

    <div class="card">
        <h2>Test Protected Endpoint</h2>
        <button onclick="testProtected()">Test Protected Endpoint</button>
        <div>
            <h3>Response:</h3>
            <pre id="protected-response"></pre>
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:8071/api';
        let token = '';

        async function register() {
            const username = document.getElementById('reg-username').value;
            const email = document.getElementById('reg-email').value;
            const password = document.getElementById('reg-password').value;

            try {
                const response = await fetch(`${API_URL}/auth/signup`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, email, password })
                });

                const data = await response.text();
                document.getElementById('register-response').textContent = data;
                console.log('Register response:', data);
            } catch (error) {
                document.getElementById('register-response').textContent = `Error: ${error.message}`;
                console.error('Register error:', error);
            }
        }

        async function login() {
            const username = document.getElementById('login-username').value;
            const password = document.getElementById('login-password').value;

            try {
                const response = await fetch(`${API_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                document.getElementById('login-response').textContent = JSON.stringify(data, null, 2);
                console.log('Login response:', data);

                if (data.accessToken) {
                    token = data.accessToken;
                    console.log('Token saved:', token);
                }
            } catch (error) {
                document.getElementById('login-response').textContent = `Error: ${error.message}`;
                console.error('Login error:', error);
            }
        }

        async function testProtected() {
            try {
                const response = await fetch(`${API_URL}/test/private`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.text();
                document.getElementById('protected-response').textContent = data;
                console.log('Protected endpoint response:', data);
            } catch (error) {
                document.getElementById('protected-response').textContent = `Error: ${error.message}`;
                console.error('Protected endpoint error:', error);
            }
        }
    </script>
</body>
</html>

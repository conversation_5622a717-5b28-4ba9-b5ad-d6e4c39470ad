package com.cms.mapper;

import com.cms.dto.simplified.*;
import com.cms.entity.*;
import com.cms.entity.ComponentComponent;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class SimplifiedCollectionMapper {

    public SimplifiedCollectionDTO toDTO(CollectionListing entity) {
        if (entity == null) {
            return null;
        }

        SimplifiedCollectionDTO dto = new SimplifiedCollectionDTO();
        dto.setId(entity.getId());
        dto.setCollectionName(entity.getCollectionName());
        dto.setCollectionDesc(entity.getCollectionDesc());
        dto.setCollectionApiId(entity.getCollectionApiId());

        if (entity.getComponents() != null) {
            dto.setComponents(entity.getComponents().stream()
                    .map(this::toComponentDTO)
                    .collect(Collectors.toList()));
        }

        if (entity.getFields() != null) {
            dto.setFields(entity.getFields().stream()
                    .map(this::toCollectionFieldDTO)
                    .collect(Collectors.toList()));
        }

        return dto;
    }

    public List<SimplifiedCollectionDTO> toDTOList(List<CollectionListing> entities) {
        if (entities == null) {
            return null;
        }

        return entities.stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }

    private SimplifiedComponentDTO toComponentDTO(CollectionComponent entity) {
        if (entity == null) {
            return null;
        }

        SimplifiedComponentDTO dto = new SimplifiedComponentDTO();
        dto.setId(entity.getId());
        dto.setDisplayPreference(entity.getDisplayPreference());
        dto.setIsRepeatable(entity.getIsRepeatable());
        dto.setMinRepeatOccurrences(entity.getMinRepeatOccurrences());
        dto.setMaxRepeatOccurrences(entity.getMaxRepeatOccurrences());
        dto.setIsActive(entity.getIsActive());

        if (entity.getComponent() != null) {
            dto.setComponent(toComponentDetailsDTO(entity.getComponent()));
        }

        return dto;
    }

    private SimplifiedComponentDetailsDTO toComponentDetailsDTO(ComponentListing entity) {
        if (entity == null) {
            return null;
        }

        SimplifiedComponentDetailsDTO dto = new SimplifiedComponentDetailsDTO();
        dto.setId(entity.getId());
        dto.setComponentName(entity.getComponentName());
        dto.setComponentDisplayName(entity.getComponentDisplayName());
        dto.setComponentApiId(entity.getComponentApiId());
        dto.setIsActive(entity.getIsActive());
        dto.setGetUrl(entity.getGetUrl());
        dto.setPostUrl(entity.getPostUrl());
        dto.setUpdateUrl(entity.getUpdateUrl());

        // Map fields
        if (entity.getFields() != null) {
            dto.setFields(entity.getFields().stream()
                    .map(this::toFieldDTO)
                    .collect(Collectors.toList()));
        }

        // Map child components
        if (entity.getChildComponents() != null) {
            dto.setChildComponents(entity.getChildComponents().stream()
                    .map(this::toChildComponentDTO)
                    .collect(Collectors.toList()));
        }

        return dto;
    }

    private SimplifiedFieldDTO toFieldDTO(ComponentField entity) {
        if (entity == null) {
            return null;
        }

        SimplifiedFieldDTO dto = new SimplifiedFieldDTO();
        dto.setId(entity.getId());
        dto.setDisplayPreference(entity.getDisplayPreference());

        if (entity.getFieldType() != null) {
            SimplifiedFieldTypeDTO fieldTypeDTO = new SimplifiedFieldTypeDTO();
            fieldTypeDTO.setId(entity.getFieldType().getId());
            fieldTypeDTO.setFieldTypeName(entity.getFieldType().getFieldTypeName());
            fieldTypeDTO.setFieldTypeDesc(entity.getFieldType().getFieldTypeDesc());
            fieldTypeDTO.setDisplayName(entity.getFieldType().getDisplayName());
            fieldTypeDTO.setHelpText(entity.getFieldType().getHelpText());
            fieldTypeDTO.setIsActive(entity.getFieldType().getIsActive());
            dto.setFieldType(fieldTypeDTO);
        }

        // Group configs by config type
        Map<String, Map<String, Object>> groupedConfigs = new HashMap<>();
        Map<String, Object> properties = new HashMap<>();
        Map<String, Object> attributes = new HashMap<>();
        Map<String, Object> validations = new HashMap<>();

        groupedConfigs.put("Properties", properties);
        groupedConfigs.put("Attributes", attributes);
        groupedConfigs.put("Validations", validations);

        if (entity.getConfigs() != null && !entity.getConfigs().isEmpty()) {
            for (ComponentFieldConfig config : entity.getConfigs()) {
                if (config.getFieldConfig() != null && config.getFieldConfig().getConfigType() != null) {
                    String configTypeName = config.getFieldConfig().getConfigType().getConfigTypeName();
                    String configName = config.getFieldConfig().getConfigName();
                    String configValue = config.getFieldConfigValue();

                    // Convert value to appropriate type based on valueType
                    Object typedValue = convertToType(configValue, config.getFieldConfig().getValueType());

                    // Map the config type names from the database to the expected output format
                    if ("Properties".equalsIgnoreCase(configTypeName) || "Property".equalsIgnoreCase(configTypeName)) {
                        properties.put(configName, typedValue);
                    } else if ("Attributes".equalsIgnoreCase(configTypeName) || "Attribute".equalsIgnoreCase(configTypeName)) {
                        attributes.put(configName, typedValue);
                    } else if ("Validations".equalsIgnoreCase(configTypeName) || "Validation".equalsIgnoreCase(configTypeName)) {
                        validations.put(configName, typedValue);
                    }
                }
            }
        }

        dto.setConfigs(groupedConfigs);
        return dto;
    }

    private SimplifiedChildComponentDTO toChildComponentDTO(ComponentComponent entity) {
        if (entity == null) {
            return null;
        }

        SimplifiedChildComponentDTO dto = new SimplifiedChildComponentDTO();
        dto.setId(entity.getId());
        dto.setDisplayPreference(entity.getDisplayPreference());
        dto.setIsRepeatable(entity.getIsRepeatable());
        dto.setIsActive(entity.getIsActive());
        dto.setAdditionalInformation(entity.getAdditionalInformation());

        if (entity.getChildComponent() != null) {
            dto.setChildComponent(toComponentDetailsDTO(entity.getChildComponent()));
        }

        return dto;
    }

    private SimplifiedFieldDTO toCollectionFieldDTO(CollectionField entity) {
        if (entity == null) {
            return null;
        }

        SimplifiedFieldDTO dto = new SimplifiedFieldDTO();
        dto.setId(entity.getId());
        dto.setDisplayPreference(entity.getDisplayPreference());

        if (entity.getFieldType() != null) {
            SimplifiedFieldTypeDTO fieldTypeDTO = new SimplifiedFieldTypeDTO();
            fieldTypeDTO.setId(entity.getFieldType().getId());
            fieldTypeDTO.setFieldTypeName(entity.getFieldType().getFieldTypeName());
            fieldTypeDTO.setFieldTypeDesc(entity.getFieldType().getFieldTypeDesc());
            fieldTypeDTO.setDisplayName(entity.getFieldType().getDisplayName());
            fieldTypeDTO.setHelpText(entity.getFieldType().getHelpText());
            fieldTypeDTO.setIsActive(entity.getFieldType().getIsActive());
            dto.setFieldType(fieldTypeDTO);
        }

        // Group configs by config type
        Map<String, Map<String, Object>> groupedConfigs = new HashMap<>();
        Map<String, Object> properties = new HashMap<>();
        Map<String, Object> attributes = new HashMap<>();
        Map<String, Object> validations = new HashMap<>();

        groupedConfigs.put("Properties", properties);
        groupedConfigs.put("Attributes", attributes);
        groupedConfigs.put("Validations", validations);

        if (entity.getConfigs() != null && !entity.getConfigs().isEmpty()) {
            for (CollectionFieldConfig config : entity.getConfigs()) {
                if (config.getFieldConfig() != null && config.getFieldConfig().getConfigType() != null) {
                    String configTypeName = config.getFieldConfig().getConfigType().getConfigTypeName();
                    String configName = config.getFieldConfig().getConfigName();
                    String configValue = config.getFieldConfigValue();

                    // Convert value to appropriate type based on valueType
                    Object typedValue = convertToType(configValue, config.getFieldConfig().getValueType());

                    // Map the config type names from the database to the expected output format
                    if ("Properties".equalsIgnoreCase(configTypeName) || "Property".equalsIgnoreCase(configTypeName)) {
                        properties.put(configName, typedValue);
                    } else if ("Attributes".equalsIgnoreCase(configTypeName) || "Attribute".equalsIgnoreCase(configTypeName)) {
                        attributes.put(configName, typedValue);
                    } else if ("Validations".equalsIgnoreCase(configTypeName) || "Validation".equalsIgnoreCase(configTypeName)) {
                        validations.put(configName, typedValue);
                    }
                }
            }
        }

        dto.setConfigs(groupedConfigs);
        return dto;
    }

    private Object convertToType(String value, String valueType) {
        if (value == null) {
            return null;
        }

        try {
            if (valueType == null) {
                return value;
            }

            switch (valueType.toLowerCase()) {
                case "boolean":
                    return Boolean.parseBoolean(value);
                case "integer":
                    return Integer.parseInt(value);
                case "double":
                    return Double.parseDouble(value);
                default:
                    return value;
            }
        } catch (Exception e) {
            // If conversion fails, return the original string value
            return value;
        }
    }
}

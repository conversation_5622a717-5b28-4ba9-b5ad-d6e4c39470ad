package com.cms.repository;

import com.cms.entity.CollectionListing;
import com.cms.entity.ContentEntry;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ContentEntryRepository extends JpaRepository<ContentEntry, Integer> {
    List<ContentEntry> findByCollection(CollectionListing collection);

    /**
     * Find content entries by collection and search text in JSON data
     * Uses the GIN index on data_json for efficient text search
     */
    @Query(value = "SELECT * FROM content_entries WHERE collection_id = :collectionId AND data_json::text ILIKE '%' || :searchText || '%'", nativeQuery = true)
    List<ContentEntry> findByCollectionIdAndDataJsonContaining(@Param("collectionId") Integer collectionId, @Param("searchText") String searchText);

    /**
     * Find content entries by JSON path expression
     * Allows for querying specific fields within the JSON structure
     */
    @Query(value = "SELECT * FROM content_entries WHERE jsonb_path_exists(data_json, :jsonPath)", nativeQuery = true)
    List<ContentEntry> findByJsonPath(@Param("jsonPath") String jsonPath);

    /**
     * Find content entries by JSON path expression with a specific value
     */
    @Query(value = "SELECT * FROM content_entries WHERE jsonb_path_exists(data_json, :jsonPath, :jsonValue::jsonb)", nativeQuery = true)
    List<ContentEntry> findByJsonPathAndValue(@Param("jsonPath") String jsonPath, @Param("jsonValue") String jsonValue);
}

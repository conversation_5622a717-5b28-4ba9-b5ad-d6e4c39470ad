package com.cms.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.ContentNegotiationConfigurer;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CustomJsonHttpMessageConverter customJsonHttpMessageConverter;

    @Autowired
    private TenantContextInterceptor tenantContextInterceptor;

    @Override
    public void configureContentNegotiation(ContentNegotiationConfigurer configurer) {
        configurer
            .favorParameter(false)
            .ignoreAcceptHeader(false)
            .defaultContentType(MediaType.APPLICATION_JSON)
            .mediaType("json", MediaType.APPLICATION_JSON)
            .mediaType("*", MediaType.ALL);
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        // Clear existing converters
        converters.clear();

        // Add StringHttpMessageConverter with UTF-8 encoding
        StringHttpMessageConverter stringConverter = new StringHttpMessageConverter(StandardCharsets.UTF_8);
        stringConverter.setWriteAcceptCharset(false);
        converters.add(stringConverter);

        // Add our custom JSON converter
        converters.add(customJsonHttpMessageConverter);

        // Add standard Jackson converter as fallback
        converters.add(customJacksonConverter());
    }

    @Bean
    @Primary
    public MappingJackson2HttpMessageConverter customJacksonConverter() {
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter(objectMapper);

        // Define all the media types we want to support
        List<MediaType> supportedMediaTypes = new ArrayList<>(Arrays.asList(
            MediaType.APPLICATION_JSON,
            MediaType.valueOf("application/json;charset=UTF-8"),
            MediaType.valueOf("application/json; charset=UTF-8"),
            MediaType.valueOf("application/json;charset=utf-8"),
            MediaType.valueOf("application/json; charset=utf-8"),
            MediaType.valueOf("*/*")
        ));

        converter.setSupportedMediaTypes(supportedMediaTypes);
        return converter;
    }

    /**
     * Register interceptors for the application
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // Add tenant context interceptor
        registry.addInterceptor(tenantContextInterceptor)
                .addPathPatterns("/**") // Apply to all paths
                .excludePathPatterns("/swagger-ui/**", "/v3/api-docs/**"); // Exclude Swagger paths
    }
}

import React from 'react';
interface FieldConfig {
  configName: string;
  valueType: string;
  isActive?: boolean;
  configType?: string;
}
import { getConfigComponent } from './FieldConfigComponents';

interface ConfigurationGroupProps {
  title: string;
  configs: FieldConfig[];
  fieldData: Record<string, any>;
  onChange: (configName: string, value: any) => void;
}

const ConfigurationGroup: React.FC<ConfigurationGroupProps> = ({
  title,
  configs,
  fieldData,
  onChange
}) => {
  if (configs.length === 0) return null;

  return (
    <div className="field-config-group mb-6">
      <h4 className="text-base font-medium mb-4">{title}</h4>
      {configs.map((config, index) => {
        const ConfigComponent = getConfigComponent(config.valueType);
        return (
          <div key={index} className="mb-4">
            <ConfigComponent
              configName={config.configName}
              value={fieldData?.[config.configName]}
              onChange={(value) => onChange(config.configName, value)}
            />
          </div>
        );
      })}
    </div>
  );
};

export default ConfigurationGroup;

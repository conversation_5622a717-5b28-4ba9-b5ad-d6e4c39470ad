package com.cms.controller;

import com.cms.dto.ClientCreateDTO;
import com.cms.dto.ClientDTO;
import com.cms.entity.Client;
import com.cms.exception.NoContentException;
import com.cms.mapper.ClientMapper;
import com.cms.service.ClientService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing clients
 */
@RestController
@RequestMapping("/clients")
@RequiredArgsConstructor
@Tag(name = "Client Management", description = "Client Management API")
public class ClientController {

    private final ClientService clientService;
    private final ClientMapper clientMapper;

    @GetMapping("/getAll")
    @Operation(
        summary = "Get all clients", 
        description = "Returns a list of all clients",
        security = @SecurityRequirement(name = "Bearer Authentication")
    )
    public ResponseEntity<List<ClientDTO>> getAllClients() {
        List<Client> clients = clientService.getAllClients();
        if (clients.isEmpty()) {
            throw new NoContentException("No clients found");
        }
        return ResponseEntity.ok(clientMapper.toDTOList(clients));
    }

    @GetMapping("/getById/{id}")
    @Operation(
        summary = "Get client by ID", 
        description = "Returns a client by its ID",
        security = @SecurityRequirement(name = "Bearer Authentication")
    )
    public ResponseEntity<ClientDTO> getClientById(@PathVariable Integer id) {
        Optional<Client> clientOpt = clientService.getClientById(id);
        return clientOpt.map(client -> ResponseEntity.ok(clientMapper.toDTO(client)))
                .orElseThrow(() -> new NoContentException("Client not found with id: " + id));
    }

    @GetMapping("/getByName/{name}")
    @Operation(
        summary = "Get client by name", 
        description = "Returns a client by its name",
        security = @SecurityRequirement(name = "Bearer Authentication")
    )
    public ResponseEntity<ClientDTO> getClientByName(@PathVariable String name) {
        Optional<Client> clientOpt = clientService.getClientByName(name);
        return clientOpt.map(client -> ResponseEntity.ok(clientMapper.toDTO(client)))
                .orElseThrow(() -> new NoContentException("Client not found with name: " + name));
    }

    @PostMapping("/create")
    @Operation(
        summary = "Create a new client", 
        description = "Creates a new client",
        security = @SecurityRequirement(name = "Bearer Authentication"),
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "Client object to be created",
            content = @Content(
                mediaType = "application/json",
                examples = {
                    @ExampleObject(
                        name = "Standard Client",
                        summary = "Standard client creation example",
                        value = "{\"name\":\"Acme Corporation\"}"
                    )
                },
                schema = @Schema(implementation = ClientCreateDTO.class)
            )
        )
    )
    public ResponseEntity<ClientDTO> createClient(@Valid @RequestBody ClientCreateDTO clientCreateDTO) {
        Client client = clientCreateDTO.toEntity();
        Client createdClient = clientService.createClient(client);
        return new ResponseEntity<>(clientMapper.toDTO(createdClient), HttpStatus.CREATED);
    }

    @PutMapping("/update/{id}")
    @Operation(
        summary = "Update an existing client", 
        description = "Updates an existing client by its ID",
        security = @SecurityRequirement(name = "Bearer Authentication"),
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "Updated client object",
            content = @Content(
                mediaType = "application/json",
                examples = {
                    @ExampleObject(
                        name = "Update Client",
                        summary = "Client update example",
                        value = "{\"name\":\"Updated Corporation\"}"
                    )
                },
                schema = @Schema(implementation = ClientCreateDTO.class)
            )
        )
    )
    public ResponseEntity<ClientDTO> updateClient(
            @PathVariable Integer id,
            @Valid @RequestBody ClientCreateDTO clientCreateDTO) {
        Client client = clientCreateDTO.toEntity();
        Client updatedClient = clientService.updateClient(id, client);
        return ResponseEntity.ok(clientMapper.toDTO(updatedClient));
    }

    @DeleteMapping("/deleteById/{id}")
    @Operation(
        summary = "Delete a client", 
        description = "Deletes a client by its ID",
        security = @SecurityRequirement(name = "Bearer Authentication")
    )
    public ResponseEntity<Void> deleteClient(@PathVariable Integer id) {
        clientService.deleteClient(id);
        return ResponseEntity.noContent().build();
    }
}

package com.cms.service;

import com.cms.dto.MediaDTO;
import com.cms.entity.Media;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface MediaService {

    /**
     * Store a media file
     *
     * @param file The file to store
     * @param folderId The folder ID to store the file in (optional)
     * @param description The description of the file (optional)
     * @param altText The alt text for the file (optional)
     * @param isPublic Whether the file is publicly accessible
     * @return The stored media
     */
    MediaDTO storeFile(MultipartFile file, Integer folderId, String description, String altText, Boolean isPublic);

    /**
     * Get a media file by ID
     *
     * @param id The ID of the media file
     * @return The media file
     */
    MediaDTO getMediaById(Integer id);

    /**
     * Get all media files
     *
     * @param pageable Pagination information
     * @return A page of media files
     */
    Page<MediaDTO> getAllMedia(Pageable pageable);

    /**
     * Get media files by folder ID
     *
     * @param folderId The folder ID
     * @return A list of media files
     */
    List<MediaDTO> getMediaByFolder(Integer folderId);

    /**
     * Search media files by name
     *
     * @param query The search query
     * @param pageable Pagination information
     * @return A page of media files
     */
    Page<MediaDTO> searchMedia(String query, Pageable pageable);

    /**
     * Update media file metadata
     *
     * @param id The ID of the media file
     * @param fileName The new file name (optional)
     * @param description The new description (optional)
     * @param altText The new alt text (optional)
     * @param isPublic Whether the file is publicly accessible
     * @param folderId The new folder ID (optional)
     * @return The updated media file
     */
    MediaDTO updateMedia(Integer id, String fileName, String description, String altText, Boolean isPublic, Integer folderId);

    /**
     * Delete a media file
     *
     * @param id The ID of the media file
     */
    void deleteMedia(Integer id);

    /**
     * Generate a share token for a media file
     *
     * @param id The ID of the media file
     * @return The share URL
     */
    String generateShareToken(Integer id);

    /**
     * Get a media file by share token
     *
     * @param token The share token
     * @return The media file
     */
    Media getMediaByShareToken(String token);

    /**
     * Replace a media file while keeping the same URL
     *
     * @param id The ID of the media file to replace
     * @param file The new file
     * @return The updated media file
     */
    MediaDTO replaceFile(Integer id, MultipartFile file);
}

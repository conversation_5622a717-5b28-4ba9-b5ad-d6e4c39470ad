# Multi-Tenant System Testing Instructions

This guide provides instructions on how to test the multi-tenant system after implementing the fix for the username uniqueness issue.

## Prerequisites

1. The application is running
2. PostgreSQL database is running
3. The SQL migration script has been executed to remove unique constraints

## Testing Steps

### Step 1: Execute the SQL Migration Script

1. Navigate to the `backend/src/main/resources/db` directory
2. Run the `execute_migration.bat` (Windows) or `execute_migration.sh` (Linux/Mac) script
3. Alternatively, use the API endpoint to execute the migration:

```
POST /admin/database/execute-migration
```

### Step 2: Test Username Uniqueness

#### Option 1: Using the Test Controller

1. Use the test controller endpoint to create users with the same username in different tenants:

```
POST /test/multi-tenancy/test-username-uniqueness?username=testuser&tenant1=testtenant1&tenant2=testtenant2
```

2. Check the response to verify that users were created successfully in both tenants.

#### Option 2: Manual Testing

1. Register a user in one tenant:

```
POST /auth/register
{
  "username": "testuser@testtenant1",
  "email": "<EMAIL>",
  "password": "password123"
}
```

2. Register a user with the same username in another tenant:

```
POST /auth/register
{
  "username": "testuser@testtenant2",
  "email": "<EMAIL>",
  "password": "password123"
}
```

3. Verify that both users were created successfully.

### Step 3: Test Login

1. Login with the first user:

```
POST /auth/login
{
  "username": "testuser@testtenant1",
  "password": "password123"
}
```

2. Login with the second user:

```
POST /auth/login
{
  "username": "testuser@testtenant2",
  "password": "password123"
}
```

3. Verify that both users can login successfully.

### Step 4: Test Data Isolation

1. Create some data (e.g., collections, components) while logged in as the first user.
2. Create different data while logged in as the second user.
3. Verify that each user can only see their own data.

## Troubleshooting

### Issue: Users Still Can't Have the Same Username in Different Tenants

1. Verify that the SQL migration script was executed successfully.
2. Check the database to ensure that the unique constraints were removed from all tenant schemas.
3. Restart the application to ensure that the changes take effect.
4. Check the logs for any errors related to tenant context or database operations.

### Issue: User Data Not Being Saved in the Correct Schema

1. Check the logs to see which tenant context is being used during user creation.
2. Verify that the tenant context is being properly set before saving the user.
3. Check the database to see if the user was saved in the correct schema.

### Issue: Login Not Working

1. Check the logs to see which tenant context is being used during login.
2. Verify that the tenant context is being properly set before looking up the user.
3. Check the database to see if the user exists in the expected schema.

## Additional Tests

### Test 1: Create a User in a Non-Existent Tenant

1. Try to register a user in a tenant that doesn't exist:

```
POST /auth/register
{
  "username": "testuser@nonexistenttenant",
  "email": "<EMAIL>",
  "password": "password123"
}
```

2. Verify that the tenant is created automatically and the user is registered successfully.

### Test 2: Create a User Without a Tenant

1. Try to register a user without specifying a tenant:

```
POST /auth/register
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123"
}
```

2. Verify that the user is registered in the default "public" tenant.

### Test 3: Login with a User in a Non-Existent Tenant

1. Try to login with a user in a tenant that doesn't exist:

```
POST /auth/login
{
  "username": "testuser@nonexistenttenant",
  "password": "password123"
}
```

2. Verify that the login fails with an appropriate error message.

### Test 4: Login with a User Without a Tenant

1. Try to login with a user without specifying a tenant:

```
POST /auth/login
{
  "username": "testuser",
  "password": "password123"
}
```

2. Verify that the login works if the user exists in the default "public" tenant.

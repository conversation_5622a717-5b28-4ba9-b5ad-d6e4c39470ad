package com.cms.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.examples.Example;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.Map;

@Configuration
public class OpenApiConfig {

    @Bean
    public OpenAPI openAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Content Management System API")
                        .description("API for managing content components and collections.\n\n" +
                                "**Authentication Instructions:**\n" +
                                "1. Use the `/auth/login` endpoint to get a token\n" +
                                "2. Click the Authorize button at the top of this page\n" +
                                "3. In the value field, enter `Bearer ` followed by your token (include the word 'Bearer' and a space)\n" +
                                "4. Example: `Bearer eyJhbGciOiJIUzUxMiJ9...`\n" +
                                "5. Click Authorize\n\n" +
                                "**Common Issues:**\n" +
                                "- Make sure to include the word 'Bearer' followed by a space before your token\n" +
                                "- Do not include any extra spaces or quotes around the token\n" +
                                "- If you're getting authentication errors, try logging in again to get a fresh token")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("CMS Team")
                                .email("<EMAIL>"))
                        .license(new License()
                                .name("Apache 2.0")
                                .url("http://www.apache.org/licenses/LICENSE-2.0.html")))
                .addSecurityItem(new SecurityRequirement().addList("Bearer Authentication"))
                .components(new Components()
                        .addSecuritySchemes("Bearer Authentication", createAPIKeyScheme())
                        .addExamples("componentExample", createComponentExample())
                        .addExamples("componentCreateExample", createComponentCreateExample())
                        .addExamples("collectionExample", createCollectionExample())
                        .addExamples("collectionCreateExample", createCollectionCreateExample())
                        .addExamples("contentEntryExample", createContentEntryExample())
                        .addExamples("collectionComponentExample", createCollectionComponentExample()))
                .addServersItem(new Server().url("/api").description("Default Server URL"));
    }

    private SecurityScheme createAPIKeyScheme() {
        return new SecurityScheme()
                .type(SecurityScheme.Type.HTTP)
                .bearerFormat("JWT")
                .scheme("bearer")
                .description("Enter JWT token with Bearer prefix. Make sure to include the word 'Bearer' followed by a space, then your token. Example: 'Bearer eyJhbGciOiJIUzUxMiJ9...' (without quotes). If you're getting authentication errors, make sure you've included the 'Bearer ' prefix exactly as shown.")
                .in(SecurityScheme.In.HEADER)
                .name("Authorization");
    }

    private Example createComponentExample() {
        return new Example()
                .summary("Example Component")
                .description("Example of a component object")
                .value(Map.of(
                        "id", 100,
                        "componentName", "Example Component",
                        "componentDisplayName", "Example Component Display",
                        "componentApiId", "example_component",
                        "isActive", true,
                        "getUrl", "/api/example",
                        "postUrl", "/api/example/create",
                        "updateUrl", "/api/example/update"
                ));
    }

    private Example createComponentCreateExample() {
        return new Example()
                .summary("Component Creation Example")
                .description("Example payload for creating a component")
                .value(Map.of(
                        "componentName", "Example Component",
                        "componentDisplayName", "Example Component Display",
                        "componentApiId", "example_component",
                        "isActive", true,
                        "getUrl", "/api/example",
                        "postUrl", "/api/example/create",
                        "updateUrl", "/api/example/update"
                ));
    }

    private Example createCollectionExample() {
        return new Example()
                .summary("Example Collection")
                .description("Example of a collection object")
                .value(Map.of(
                        "id", 100,
                        "collectionName", "Example Collection",
                        "collectionDesc", "This is an example collection",
                        "collectionApiId", "example_collection",
                        "additionalInformation", "Additional information about the collection",
                        "disclaimerText", "Disclaimer text for the collection"
                ));
    }

    private Example createCollectionCreateExample() {
        return new Example()
                .summary("Collection Creation Example")
                .description("Example payload for creating a collection")
                .value(Map.of(
                        "collectionName", "Example Collection",
                        "collectionDesc", "This is an example collection",
                        "collectionApiId", "example_collection",
                        "additionalInformation", "Additional information about the collection",
                        "disclaimerText", "Disclaimer text for the collection"
                ));
    }

    private Example createContentEntryExample() {
        return new Example()
                .summary("Example Content Entry")
                .description("Example of a content entry object")
                .value(Map.of(
                        "collection", Map.of("id", 100),
                        "dataJson", "{\"title\":\"Example Title\",\"content\":\"Example content text\",\"author\":\"John Doe\"}"
                ));
    }

    private Example createCollectionComponentExample() {
        return new Example()
                .summary("Example Collection Component")
                .description("Example of a collection component object")
                .value(Map.of(
                        "collection", Map.of("id", 100),
                        "component", Map.of("id", 101),
                        "isRepeatable", false,
                        "minRepeatOccurrences", 0,
                        "maxRepeatOccurrences", 0
                ));
    }
}

package com.cms.controller;

import com.cms.entity.ApiToken;
import com.cms.entity.User;
import com.cms.payload.ApiTokenRequest;
import com.cms.payload.ApiTokenResponse;
import com.cms.repository.UserRepository;
import com.cms.service.ApiTokenService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api-tokens")
@RequiredArgsConstructor
@Tag(name = "API Tokens", description = "API Token Management")
public class ApiTokenController {

    private final ApiTokenService apiTokenService;
    private final UserRepository userRepository;

    @PostMapping
    @Operation(
            summary = "Generate a new API token",
            description = "Creates a new API token for the authenticated user",
            security = @SecurityRequirement(name = "Bearer Authentication")
    )
    public ResponseEntity<ApiTokenResponse> generateToken(@Valid @RequestBody ApiTokenRequest request) {
        User user = getCurrentUser();
        
        ApiToken token = apiTokenService.generateToken(
                user,
                request.getName(),
                request.getDescription(),
                request.getExpirationDays()
        );
        
        return ResponseEntity.status(HttpStatus.CREATED).body(mapToResponse(token));
    }

    @GetMapping
    @Operation(
            summary = "Get all API tokens",
            description = "Returns all API tokens for the authenticated user",
            security = @SecurityRequirement(name = "Bearer Authentication")
    )
    public ResponseEntity<List<ApiTokenResponse>> getTokens() {
        User user = getCurrentUser();
        
        List<ApiToken> tokens = apiTokenService.getTokensByUser(user);
        List<ApiTokenResponse> responses = tokens.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(responses);
    }

    @DeleteMapping("/{id}")
    @Operation(
            summary = "Revoke an API token",
            description = "Revokes (deactivates) an API token",
            security = @SecurityRequirement(name = "Bearer Authentication")
    )
    public ResponseEntity<ApiTokenResponse> revokeToken(@PathVariable Long id) {
        User user = getCurrentUser();
        
        // First check if the token belongs to the current user
        ApiToken token = apiTokenService.getTokensByUser(user).stream()
                .filter(t -> t.getId().equals(id))
                .findFirst()
                .orElseThrow(() -> new UsernameNotFoundException("Token not found or does not belong to the current user"));
        
        ApiToken revokedToken = apiTokenService.revokeToken(id);
        return ResponseEntity.ok(mapToResponse(revokedToken));
    }

    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with username: " + username));
    }
    
    private ApiTokenResponse mapToResponse(ApiToken token) {
        ApiTokenResponse response = new ApiTokenResponse();
        response.setId(token.getId());
        response.setName(token.getName());
        response.setDescription(token.getDescription());
        // Only include the full token value for newly created tokens
        if (token.getLastUsedAt() == null) {
            response.setTokenValue(token.getTokenValue());
        } else {
            // For existing tokens, mask the value
            String maskedValue = token.getTokenValue().substring(0, 8) + "..." + 
                    token.getTokenValue().substring(token.getTokenValue().length() - 4);
            response.setTokenValue(maskedValue);
        }
        response.setExpiresAt(token.getExpiresAt());
        response.setLastUsedAt(token.getLastUsedAt());
        response.setIsActive(token.getIsActive());
        response.setCreatedAt(token.getCreatedAt());
        return response;
    }
}

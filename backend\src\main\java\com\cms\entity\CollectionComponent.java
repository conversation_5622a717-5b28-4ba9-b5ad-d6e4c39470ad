package com.cms.entity;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIdentityReference;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "collection_components")
@Getter
@Setter
@ToString(exclude = "collection")
@NoArgsConstructor
@AllArgsConstructor
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
public class CollectionComponent extends Auditable {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "cms_collection_component_seq")
    @SequenceGenerator(name = "cms_collection_component_seq", sequenceName = "cms_collection_component_seq", initialValue = 100, allocationSize = 1)
    private Integer id;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "collection_id")
    @JsonIdentityReference(alwaysAsId = true)
    private CollectionListing collection;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "component_id")
    private ComponentListing component;

    @Column(name = "display_preference")
    private Integer displayPreference;

    @Column(name = "is_repeatable")
    private Boolean isRepeatable = false;

    @Column(name = "min_repeat_occurrences")
    private Integer minRepeatOccurrences;

    @Column(name = "max_repeat_occurrences")
    private Integer maxRepeatOccurrences;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @Column(name = "additional_information", columnDefinition = "TEXT")
    private String additionalInformation;

}

package com.cms.mapper;

import com.cms.dto.ComponentFieldDTO;
import com.cms.entity.ComponentField;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class ComponentFieldMapper {

    public ComponentFieldDTO toDTO(ComponentField entity) {
        if (entity == null) {
            return null;
        }

        ComponentFieldDTO dto = new ComponentFieldDTO();
        dto.setId(entity.getId());
        
        // Set component information if available
        if (entity.getComponent() != null) {
            dto.setComponentId(entity.getComponent().getId());
            dto.setComponentName(entity.getComponent().getComponentName());
            dto.setComponentApiId(entity.getComponent().getComponentApiId());
        }
        
        dto.setFieldType(entity.getFieldType());
        dto.setDisplayPreference(entity.getDisplayPreference());
        dto.setAdditionalInformation(entity.getAdditionalInformation());
        dto.setConfigs(entity.getConfigs());
        dto.setCreatedBy(entity.getCreatedBy());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setModifiedBy(entity.getModifiedBy());
        dto.setModifiedAt(entity.getModifiedAt());
        
        return dto;
    }

    public List<ComponentFieldDTO> toDTOList(List<ComponentField> entities) {
        if (entities == null) {
            return null;
        }
        
        return entities.stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }
}

package com.cms.service.impl;

import com.cms.entity.ConfigType;
import com.cms.exception.NoContentException;
import com.cms.exception.NullConstraintViolationException;
import com.cms.exception.ResourceNotFoundException;
import com.cms.exception.UniqueConstraintViolationException;
import com.cms.repository.ConfigTypeRepository;
import com.cms.service.ConfigTypeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class ConfigTypeServiceImpl implements ConfigTypeService {

    private final ConfigTypeRepository configTypeRepository;

    @Override
    public List<ConfigType> getAllConfigTypes() {
        List<ConfigType> configTypes = configTypeRepository.findAll();
        log.debug("Retrieved {} config types", configTypes.size());
        return configTypes;
    }

    @Override
    public List<ConfigType> getActiveConfigTypes() {
        List<ConfigType> activeConfigTypes = configTypeRepository.findByIsActiveTrue();
        log.debug("Retrieved {} active config types", activeConfigTypes.size());
        return activeConfigTypes;
    }

    @Override
    public Optional<ConfigType> getConfigTypeById(Integer id) {
        return configTypeRepository.findById(id);
    }

    @Override
    @Transactional
    public ConfigType createConfigType(ConfigType configType) {
        // Validate required fields
        if (configType.getConfigTypeName() == null || configType.getConfigTypeName().trim().isEmpty()) {
            throw new NullConstraintViolationException("configTypeName");
        }

        // Check for unique constraint violations
        if (existsByConfigTypeName(configType.getConfigTypeName())) {
            throw new UniqueConstraintViolationException("configTypeName", configType.getConfigTypeName());
        }

        // Set default values if not provided
        if (configType.getIsActive() == null) {
            configType.setIsActive(true);
        }

        // Save the config type
        ConfigType savedConfigType = configTypeRepository.save(configType);
        log.info("Created new config type with ID: {}", savedConfigType.getId());
        return savedConfigType;
    }

    @Override
    @Transactional
    public ConfigType updateConfigType(Integer id, ConfigType configType) {
        // Verify the config type exists
        ConfigType existingConfigType = configTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("ConfigType not found with id: " + id));

        // Validate required fields
        if (configType.getConfigTypeName() == null || configType.getConfigTypeName().trim().isEmpty()) {
            throw new NullConstraintViolationException("configTypeName");
        }

        // Check for unique constraint violations (only if name has changed)
        if (!configType.getConfigTypeName().equals(existingConfigType.getConfigTypeName()) &&
                existsByConfigTypeName(configType.getConfigTypeName())) {
            throw new UniqueConstraintViolationException("configTypeName", configType.getConfigTypeName());
        }

        // Update fields
        existingConfigType.setConfigTypeName(configType.getConfigTypeName());

        if (configType.getConfigTypeDesc() != null) {
            existingConfigType.setConfigTypeDesc(configType.getConfigTypeDesc());
        }

        if (configType.getDisplayName() != null) {
            existingConfigType.setDisplayName(configType.getDisplayName());
        }

        if (configType.getAdditionalInfo() != null) {
            existingConfigType.setAdditionalInfo(configType.getAdditionalInfo());
        }

        if (configType.getDisclaimerText() != null) {
            existingConfigType.setDisclaimerText(configType.getDisclaimerText());
        }

        if (configType.getPlaceholderText() != null) {
            existingConfigType.setPlaceholderText(configType.getPlaceholderText());
        }

        if (configType.getIsActive() != null) {
            existingConfigType.setIsActive(configType.getIsActive());
        }

        // Save the updated config type
        ConfigType updatedConfigType = configTypeRepository.save(existingConfigType);
        log.info("Updated config type with ID: {}", updatedConfigType.getId());
        return updatedConfigType;
    }

    @Override
    @Transactional
    public void deleteConfigType(Integer id) {
        // Verify the config type exists
        ConfigType configType = configTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("ConfigType not found with id: " + id));

        // Soft delete by setting isActive to false
        configType.setIsActive(false);
        configTypeRepository.save(configType);

        log.info("Soft deleted config type with ID: {}", id);
    }

    @Override
    public boolean existsByConfigTypeName(String configTypeName) {
        return configTypeRepository.existsByConfigTypeName(configTypeName);
    }
}

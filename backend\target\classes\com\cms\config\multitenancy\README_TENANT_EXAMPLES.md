# Multi-Tenant System Examples

This document provides practical examples of how to use the multi-tenant system in the CMS application.

## Registration Examples

### 1. Register a user in a specific tenant

```bash
curl -X POST http://localhost:8080/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "john@tenant1",
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

This will:
- Create the tenant "tenant1" if it doesn't exist
- Create a user "john" in the tenant1 schema

### 2. Register a user in the default tenant

```bash
curl -X POST http://localhost:8080/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "jane",
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

This will create a user "jane" in the default "public" schema.

## Login Examples

### 1. Login with tenant identifier

```bash
curl -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "john@tenant1",
    "password": "password123"
  }'
```

This will authenticate the user "john" in the tenant1 schema and return a JWT token.

### 2. Login without tenant identifier (uses default tenant)

```bash
curl -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "jane",
    "password": "password123"
  }'
```

This will authenticate the user "jane" in the default "public" schema and return a JWT token.

## API Access Examples

### 1. Using the JWT token from login

After login, you'll receive a JWT token. Use this token for subsequent API calls:

```bash
curl -X GET http://localhost:8080/api/collections \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

The tenant context is stored in the JWT token, so the system will automatically use the correct tenant schema.

### 2. Using the X-TenantID header

You can override the tenant in the JWT token by providing the X-TenantID header:

```bash
curl -X GET http://localhost:8080/api/collections \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-TenantID: tenant1"
```

This will retrieve data from the tenant1 schema, regardless of the tenant in the JWT token.

## Testing Multi-Tenant Functionality

To test that the multi-tenant system is working correctly:

1. Register users in different tenants
2. Login with each user
3. Create data using each user's JWT token
4. Verify that each user can only see their own tenant's data

Example test flow:

```bash
# Register user1 in tenant1
curl -X POST http://localhost:8080/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "user1@tenant1",
    "email": "<EMAIL>",
    "password": "password123"
  }'

# Register user2 in tenant2
curl -X POST http://localhost:8080/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "user2@tenant2",
    "email": "<EMAIL>",
    "password": "password123"
  }'

# Login as user1
curl -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "user1@tenant1",
    "password": "password123"
  }'
# Save the token as USER1_TOKEN

# Login as user2
curl -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "user2@tenant2",
    "password": "password123"
  }'
# Save the token as USER2_TOKEN

# Create a collection as user1
curl -X POST http://localhost:8080/collections \
  -H "Authorization: Bearer $USER1_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Tenant1Collection",
    "apiId": "tenant1Collection",
    "apiIdPlural": "tenant1Collections"
  }'

# Create a collection as user2
curl -X POST http://localhost:8080/collections \
  -H "Authorization: Bearer $USER2_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Tenant2Collection",
    "apiId": "tenant2Collection",
    "apiIdPlural": "tenant2Collections"
  }'

# Get collections as user1 - should only see Tenant1Collection
curl -X GET http://localhost:8080/collections \
  -H "Authorization: Bearer $USER1_TOKEN"

# Get collections as user2 - should only see Tenant2Collection
curl -X GET http://localhost:8080/collections \
  -H "Authorization: Bearer $USER2_TOKEN"
```

This test flow demonstrates that each tenant's data is isolated from other tenants.

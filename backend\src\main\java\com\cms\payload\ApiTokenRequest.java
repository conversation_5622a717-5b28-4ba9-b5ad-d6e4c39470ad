package com.cms.payload;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiTokenRequest {
    
    @NotBlank(message = "Token name is required")
    @Size(min = 3, max = 50, message = "Token name must be between 3 and 50 characters")
    private String name;
    
    @Size(max = 200, message = "Description cannot exceed 200 characters")
    private String description;
    
    @Min(value = 1, message = "Expiration days must be at least 1")
    @Max(value = 365, message = "Expiration days cannot exceed 365")
    private int expirationDays = 30; // Default to 30 days
}

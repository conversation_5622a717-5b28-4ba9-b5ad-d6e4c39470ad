import React, { useState, useEffect } from 'react';
import { Modal, Form, Select, InputNumber, Switch, Button, message } from 'antd';
import axios from 'axios';

const AddChildComponentModal = ({ visible, parentComponent, onCancel, onSuccess }) => {
  const [form] = Form.useForm();
  const [components, setComponents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    if (visible) {
      fetchAvailableComponents();
    }
  }, [visible]);

  const fetchAvailableComponents = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/components/getAllActive');
      // Filter out the parent component itself to prevent direct circular references
      const availableComponents = response.data.filter(
        component => component.id !== parentComponent.id
      );
      setComponents(availableComponents);
    } catch (error) {
      console.error('Error fetching components:', error);
      message.error('Failed to load available components');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setSubmitting(true);
      
      const payload = {
        parentComponent: {
          id: parentComponent.id
        },
        childComponent: {
          id: values.childComponentId
        },
        isRepeatable: values.isRepeatable,
        displayPreference: values.displayPreference,
        isActive: true
      };
      
      if (values.isRepeatable) {
        payload.minRepeatOccurrences = values.minRepeatOccurrences;
        payload.maxRepeatOccurrences = values.maxRepeatOccurrences;
      }
      
      await axios.post('/api/component-components/create', payload);
      
      message.success('Child component added successfully');
      form.resetFields();
      onSuccess();
    } catch (error) {
      console.error('Error adding child component:', error);
      if (error.response && error.response.data && error.response.data.message) {
        message.error(error.response.data.message);
      } else {
        message.error('Failed to add child component');
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={`Add Child Component to ${parentComponent.componentDisplayName}`}
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          Cancel
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={submitting}
          onClick={handleSubmit}
        >
          Add Child Component
        </Button>
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          isRepeatable: false,
          displayPreference: 1,
          minRepeatOccurrences: 1,
          maxRepeatOccurrences: 10
        }}
      >
        <Form.Item
          name="childComponentId"
          label="Child Component"
          rules={[{ required: true, message: 'Please select a child component' }]}
        >
          <Select
            placeholder="Select a component"
            loading={loading}
            showSearch
            optionFilterProp="children"
          >
            {components.map(component => (
              <Select.Option key={component.id} value={component.id}>
                {component.componentDisplayName} ({component.componentName})
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        
        <Form.Item
          name="displayPreference"
          label="Display Order"
          rules={[{ required: true, message: 'Please enter display order' }]}
        >
          <InputNumber min={1} style={{ width: '100%' }} />
        </Form.Item>
        
        <Form.Item
          name="isRepeatable"
          label="Repeatable"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>
        
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) => 
            prevValues.isRepeatable !== currentValues.isRepeatable
          }
        >
          {({ getFieldValue }) => 
            getFieldValue('isRepeatable') ? (
              <>
                <Form.Item
                  name="minRepeatOccurrences"
                  label="Minimum Occurrences"
                  rules={[{ required: true, message: 'Please enter minimum occurrences' }]}
                >
                  <InputNumber min={0} style={{ width: '100%' }} />
                </Form.Item>
                
                <Form.Item
                  name="maxRepeatOccurrences"
                  label="Maximum Occurrences"
                  rules={[{ required: true, message: 'Please enter maximum occurrences' }]}
                >
                  <InputNumber min={1} style={{ width: '100%' }} />
                </Form.Item>
              </>
            ) : null
          }
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddChildComponentModal;

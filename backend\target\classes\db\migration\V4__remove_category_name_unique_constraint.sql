-- Remove unique constraint from category_name in category table
-- This allows multiple categories to have the same name across different clients

-- Drop the unique constraint on category_name
-- Note: The exact constraint name may vary depending on the database system
-- For PostgreSQL, the constraint name is typically generated automatically

-- Try different possible constraint names that might exist
DO $$
BEGIN
    -- Try to drop constraint with auto-generated name (PostgreSQL style)
    BEGIN
        ALTER TABLE category DROP CONSTRAINT IF EXISTS category_category_name_key;
    EXCEPTION
        WHEN undefined_object THEN
            -- Constraint doesn't exist, continue
            NULL;
    END;
    
    -- Try to drop constraint with explicit name
    BEGIN
        ALTER TABLE category DROP CONSTRAINT IF EXISTS uk_category_name;
    EXCEPTION
        WHEN undefined_object THEN
            -- Constraint doesn't exist, continue
            NULL;
    END;
    
    -- Try to drop constraint with another possible name
    BEGIN
        ALTER TABLE category DROP CONSTRAINT IF EXISTS category_name_unique;
    EXCEPTION
        WHEN undefined_object THEN
            -- Constraint doesn't exist, continue
            NULL;
    END;
END $$;

-- For H2 database (used in testing), use a different approach
-- H2 uses different syntax for dropping constraints
-- This will be ignored in PostgreSQL due to the IF EXISTS clause

-- Alternative approach for databases that don't support IF EXISTS
-- Check if we're running on H2 and handle accordingly
-- Note: This is a best-effort approach to handle different database systems

-- Add a comment to document the change
COMMENT ON COLUMN category.category_name IS 'Category name - no longer unique to allow same names across different clients';

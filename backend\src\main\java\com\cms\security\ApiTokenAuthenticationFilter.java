package com.cms.security;

import com.cms.entity.ApiToken;
import com.cms.entity.User;
import com.cms.service.ApiTokenService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Optional;

@Component
@RequiredArgsConstructor
@Slf4j
public class ApiTokenAuthenticationFilter extends OncePerRequestFilter {

    private final ApiTokenService apiTokenService;
    private final UserDetailsService userDetailsService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        try {
            // Check if already authenticated by JWT filter
            if (SecurityContextHolder.getContext().getAuthentication() != null) {
                filterChain.doFilter(request, response);
                return;
            }
            
            String apiToken = getApiTokenFromRequest(request);
            
            if (StringUtils.hasText(apiToken)) {
                Optional<ApiToken> tokenOpt = apiTokenService.findByTokenValue(apiToken);
                
                if (tokenOpt.isPresent()) {
                    ApiToken token = tokenOpt.get();
                    
                    // Check if token is active and not expired
                    if (token.getIsActive() && token.getExpiresAt().isAfter(LocalDateTime.now())) {
                        User user = token.getUser();
                        
                        // Create authentication
                        UserDetails userDetails = userDetailsService.loadUserByUsername(user.getUsername());
                        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                                userDetails, null, userDetails.getAuthorities());
                        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                        
                        SecurityContextHolder.getContext().setAuthentication(authentication);
                        
                        // Update last used timestamp
                        apiTokenService.updateLastUsed(token);
                        
                        log.debug("API token authentication successful for user: {}", user.getUsername());
                    } else {
                        log.debug("API token is expired or inactive");
                    }
                } else {
                    log.debug("API token not found: {}", apiToken);
                }
            }
        } catch (Exception ex) {
            log.error("Could not set user authentication with API token", ex);
        }

        filterChain.doFilter(request, response);
    }

    private String getApiTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("X-API-Key");
        if (StringUtils.hasText(bearerToken)) {
            return bearerToken;
        }
        return null;
    }
}

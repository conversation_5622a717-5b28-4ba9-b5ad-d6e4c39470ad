// Helper script for Swagger UI to properly format JWT tokens
window.onload = function() {
  // Wait for Swagger UI to fully load
  const checkInterval = setInterval(function() {
    const authorizeButton = document.querySelector('.swagger-ui .auth-wrapper .authorize');
    if (authorizeButton) {
      clearInterval(checkInterval);
      setupTokenHelper();
    }
  }, 300);

  function setupTokenHelper() {
    // Create a container for our helper
    const helperContainer = document.createElement('div');
    helperContainer.className = 'swagger-token-helper';
    helperContainer.style.margin = '10px 0';
    helperContainer.style.padding = '10px';
    helperContainer.style.backgroundColor = '#f0f0f0';
    helperContainer.style.borderRadius = '4px';
    
    // Create helper text
    const helperText = document.createElement('p');
    helperText.innerHTML = '<strong>JWT Token Helper</strong><br>' +
      'To authenticate: <br>' +
      '1. Use the <code>/auth/login</code> endpoint to get a token<br>' +
      '2. Click the Authorize button above<br>' +
      '3. Enter your token with the format: <code>Bearer YOUR_TOKEN</code> (with the word "Bearer" and a space)<br>' +
      '4. Click Authorize';
    helperContainer.appendChild(helperText);
    
    // Add a token formatter
    const tokenInput = document.createElement('input');
    tokenInput.type = 'text';
    tokenInput.placeholder = 'Paste your token here (without Bearer prefix)';
    tokenInput.style.width = '100%';
    tokenInput.style.padding = '8px';
    tokenInput.style.marginBottom = '8px';
    tokenInput.style.boxSizing = 'border-box';
    helperContainer.appendChild(tokenInput);
    
    const formatButton = document.createElement('button');
    formatButton.textContent = 'Format Token';
    formatButton.style.padding = '8px 16px';
    formatButton.style.backgroundColor = '#4990e2';
    formatButton.style.color = 'white';
    formatButton.style.border = 'none';
    formatButton.style.borderRadius = '4px';
    formatButton.style.cursor = 'pointer';
    formatButton.onclick = function() {
      const token = tokenInput.value.trim();
      if (token) {
        // Remove 'Bearer ' prefix if present
        const cleanToken = token.startsWith('Bearer ') ? token.substring(7) : token;
        const formattedToken = 'Bearer ' + cleanToken;
        
        // Copy to clipboard
        navigator.clipboard.writeText(formattedToken).then(function() {
          formatButton.textContent = 'Copied to Clipboard!';
          setTimeout(function() {
            formatButton.textContent = 'Format Token';
          }, 2000);
        });
      }
    };
    helperContainer.appendChild(formatButton);
    
    // Insert the helper after the info container
    const infoContainer = document.querySelector('.swagger-ui .information-container');
    if (infoContainer && infoContainer.parentNode) {
      infoContainer.parentNode.insertBefore(helperContainer, infoContainer.nextSibling);
    }
  }
};

package com.cms.config;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@Order(1)
public class TenantFilter implements Filter {

    private static final Logger log = LoggerFactory.getLogger(TenantFilter.class);

    private static final String TENANT_HEADER = "X-TenantID";

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;
        String tenantId = req.getHeader(TENANT_HEADER);
        String requestURI = req.getRequestURI();
        String authHeader = req.getHeader("Authorization");
        boolean hasAuthHeader = authHeader != null && !authHeader.isEmpty();

        // Skip auth endpoints - they are handled by AuthTenantFilter
        if (requestURI.contains("/auth/")) {
            log.debug("TenantFilter skipping auth endpoint: {}", requestURI);
            chain.doFilter(request, response);
            return;
        }

        log.debug("TenantFilter processing request: {}, Auth header present: {}", requestURI, hasAuthHeader);
        log.debug("X-TenantID header value: {}", tenantId);

        // Get current tenant context before potentially modifying it
        String currentTenant = TenantContextHolder.getTenantId();
        log.debug("Current tenant context before processing: {}", currentTenant);

        try {
            // Only set tenant from header if:
            // 1. We have a header value AND
            // 2. Either there's no current tenant OR we're explicitly overriding with header
            if (tenantId != null && !tenantId.isEmpty() &&
                (currentTenant == null || currentTenant.equals("public") || !hasAuthHeader)) {
                log.info("Setting tenant context from header: {}", tenantId);
                TenantContextHolder.setTenantId(tenantId);
            } else if (!hasAuthHeader && (currentTenant == null || currentTenant.isEmpty())) {
                // Only set default tenant if no auth header and no current tenant
                log.debug("No tenant header or auth token found, using default tenant");
                TenantContextHolder.setTenantId("public");
            } else {
                log.debug("Keeping existing tenant context: {}", TenantContextHolder.getTenantId());
            }

            chain.doFilter(request, response);
        } finally {
            // Only clear tenant context if there's no auth header
            // This allows the tenant context to persist for authenticated requests
            if (!hasAuthHeader) {
                log.debug("Clearing tenant context after request: {}", requestURI);
                TenantContextHolder.clear();
            } else {
                log.debug("Preserving tenant context after authenticated request: {}, tenant: {}",
                        requestURI, TenantContextHolder.getTenantId());
            }
        }
    }
}

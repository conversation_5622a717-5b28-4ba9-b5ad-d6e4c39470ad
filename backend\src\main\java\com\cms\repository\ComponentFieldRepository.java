package com.cms.repository;

import com.cms.entity.ComponentField;
import com.cms.entity.ComponentListing;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ComponentFieldRepository extends JpaRepository<ComponentField, Integer> {
    List<ComponentField> findByComponent(ComponentListing component);
    List<ComponentField> findByComponentOrderByDisplayPreference(ComponentListing component);
    List<ComponentField> findByComponentId(Integer componentId);

    @Query("SELECT MAX(cf.id) FROM ComponentField cf")
    Integer findMaxId();

    @Query("SELECT MAX(cf.displayPreference) FROM ComponentField cf WHERE cf.component.id = :componentId")
    Integer findMaxDisplayPreferenceByComponentId(@Param("componentId") Integer componentId);
}

package com.cms.service;

import com.cms.entity.Category;
import com.cms.entity.Client;
import com.cms.entity.CollectionListing;
import com.cms.exception.UniqueConstraintViolationException;
import com.cms.repository.CategoryRepository;
import com.cms.repository.ClientRepository;
import com.cms.repository.CollectionListingRepository;
import com.cms.service.impl.CollectionListingServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class CollectionListingUniqueConstraintTest {

    @Autowired
    private CollectionListingService collectionListingService;

    @Autowired
    private CollectionListingRepository collectionListingRepository;

    @Autowired
    private CategoryRepository categoryRepository;

    @Autowired
    private ClientRepository clientRepository;

    private Client testClient;
    private Category testCategory1;
    private Category testCategory2;

    @BeforeEach
    void setUp() {
        // Create test client
        testClient = new Client();
        testClient.setName("Test Client for Unique Constraint");
        testClient = clientRepository.save(testClient);

        // Create test categories
        testCategory1 = new Category();
        testCategory1.setCategoryName("Category 1");
        testCategory1.setClient(testClient);
        testCategory1 = categoryRepository.save(testCategory1);

        testCategory2 = new Category();
        testCategory2.setCategoryName("Category 2");
        testCategory2.setClient(testClient);
        testCategory2 = categoryRepository.save(testCategory2);
    }

    @Test
    void testUniqueConstraintWithinSameCategory() {
        // Create first collection in category 1
        CollectionListing collection1 = new CollectionListing();
        collection1.setCollectionName("Test Collection");
        collection1.setCollectionApiId("test_collection_1");
        collection1.setCategory(testCategory1);

        CollectionListing saved1 = collectionListingService.createCollection(collection1);
        assertNotNull(saved1.getId());

        // Try to create another collection with same name in same category - should fail
        CollectionListing collection2 = new CollectionListing();
        collection2.setCollectionName("Test Collection"); // Same name
        collection2.setCollectionApiId("test_collection_2");
        collection2.setCategory(testCategory1); // Same category

        assertThrows(UniqueConstraintViolationException.class, () -> {
            collectionListingService.createCollection(collection2);
        });
    }

    @Test
    void testUniqueConstraintAcrossDifferentCategories() {
        // Create first collection in category 1
        CollectionListing collection1 = new CollectionListing();
        collection1.setCollectionName("Test Collection");
        collection1.setCollectionApiId("test_collection_1");
        collection1.setCategory(testCategory1);

        CollectionListing saved1 = collectionListingService.createCollection(collection1);
        assertNotNull(saved1.getId());

        // Create another collection with same name in different category - should succeed
        CollectionListing collection2 = new CollectionListing();
        collection2.setCollectionName("Test Collection"); // Same name
        collection2.setCollectionApiId("test_collection_2");
        collection2.setCategory(testCategory2); // Different category

        CollectionListing saved2 = collectionListingService.createCollection(collection2);
        assertNotNull(saved2.getId());
        assertNotEquals(saved1.getId(), saved2.getId());
    }

    @Test
    void testRepositoryExistsByCollectionNameAndCategoryId() {
        // Create a collection
        CollectionListing collection = new CollectionListing();
        collection.setCollectionName("Repository Test Collection");
        collection.setCollectionApiId("repo_test_collection");
        collection.setCategory(testCategory1);

        collectionListingRepository.save(collection);

        // Test repository method
        assertTrue(collectionListingService.existsByCollectionNameAndCategoryId(
                "Repository Test Collection", testCategory1.getId()));

        assertFalse(collectionListingService.existsByCollectionNameAndCategoryId(
                "Repository Test Collection", testCategory2.getId()));

        assertFalse(collectionListingService.existsByCollectionNameAndCategoryId(
                "Non-existent Collection", testCategory1.getId()));
    }

    @Test
    void testUpdateCollectionWithSameNameInSameCategory() {
        // Create a collection
        CollectionListing collection = new CollectionListing();
        collection.setCollectionName("Original Collection");
        collection.setCollectionApiId("original_collection");
        collection.setCategory(testCategory1);

        CollectionListing saved = collectionListingService.createCollection(collection);

        // Update with same name in same category - should succeed
        saved.setCollectionDesc("Updated description");
        CollectionListing updated = collectionListingService.updateCollection(saved.getId(), saved);
        assertEquals("Updated description", updated.getCollectionDesc());
    }
}

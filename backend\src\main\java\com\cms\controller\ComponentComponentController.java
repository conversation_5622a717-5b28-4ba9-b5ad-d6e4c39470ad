package com.cms.controller;

import com.cms.dto.ComponentComponentDTO;
import com.cms.entity.ComponentComponent;
import com.cms.exception.ResourceNotFoundException;
import com.cms.mapper.ComponentComponentMapper;
import com.cms.service.ComponentComponentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/component-components")
@RequiredArgsConstructor
@Tag(name = "Component Component Management", description = "Component Component Management API")
public class ComponentComponentController {

    private final ComponentComponentService componentComponentService;
    private final ComponentComponentMapper componentComponentMapper;

    @GetMapping("/getAll")
    @Operation(summary = "Get all component components", description = "Returns a list of all component components")
    public ResponseEntity<List<ComponentComponentDTO>> getAllComponentComponents() {
        List<ComponentComponent> components = componentComponentService.getAllComponentComponents();
        return ResponseEntity.ok(componentComponentMapper.toDTOList(components));
    }

    @GetMapping("/getAllWithRelationships")
    @Operation(summary = "Get all components with their relationships", description = "Returns a list of all components with their parent-child relationships")
    public ResponseEntity<Map<String, Object>> getAllComponentsWithRelationships() {
        List<ComponentComponent> relationships = componentComponentService.getAllComponentComponents();
        List<ComponentComponentDTO> relationshipDTOs = componentComponentMapper.toDTOList(relationships);

        // Group relationships by parent component
        Map<Integer, List<ComponentComponentDTO>> relationshipsByParent = relationshipDTOs.stream()
                .collect(Collectors.groupingBy(dto -> dto.getParentComponent().getId()));

        Map<String, Object> response = new HashMap<>();
        response.put("relationships", relationshipDTOs);
        response.put("relationshipsByParent", relationshipsByParent);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/getById/{id}")
    @Operation(summary = "Get component component by ID", description = "Returns a component component by its ID or 204 No Content if not found")
    public ResponseEntity<?> getComponentComponentById(@PathVariable Integer id) {
        return componentComponentService.getComponentComponentById(id)
                .map(component -> ResponseEntity.ok(componentComponentMapper.toDTO(component)))
                .orElse(ResponseEntity.noContent().build());
    }

    @GetMapping("/getByParentId/{parentComponentId}")
    @Operation(summary = "Get component components by parent ID", description = "Returns a list of component components for a specific parent component")
    public ResponseEntity<List<ComponentComponentDTO>> getComponentComponentsByParentId(@PathVariable Integer parentComponentId) {
        List<ComponentComponent> components = componentComponentService.getComponentComponentsByParentId(parentComponentId);
        return ResponseEntity.ok(componentComponentMapper.toDTOList(components));
    }

    @PostMapping("/create")
    @Operation(summary = "Create a new component component", description = "Creates a new component component")
    public ResponseEntity<ComponentComponentDTO> createComponentComponent(@Valid @RequestBody ComponentComponentDTO componentComponentDTO) {
        // Create a copy of the DTO to preserve the full component details
        ComponentComponentDTO responseCopy = new ComponentComponentDTO();
        responseCopy.setParentComponent(componentComponentDTO.getParentComponent());
        responseCopy.setChildComponent(componentComponentDTO.getChildComponent());
        responseCopy.setIsRepeatable(componentComponentDTO.getIsRepeatable());
        responseCopy.setDisplayPreference(componentComponentDTO.getDisplayPreference());
        responseCopy.setIsActive(componentComponentDTO.getIsActive());
        responseCopy.setAdditionalInformation(componentComponentDTO.getAdditionalInformation());

        // Convert DTO to entity
        ComponentComponent entity = componentComponentMapper.toEntity(componentComponentDTO);

        // Create the component component
        ComponentComponent createdEntity = componentComponentService.createComponentComponent(entity);

        // Update the response copy with the created entity's ID
        responseCopy.setId(createdEntity.getId());

        return ResponseEntity.status(HttpStatus.CREATED).body(responseCopy);
    }

    @PutMapping("/update/{id}")
    @Operation(summary = "Update a component component", description = "Updates an existing component component")
    public ResponseEntity<ComponentComponentDTO> updateComponentComponent(@PathVariable Integer id, @Valid @RequestBody ComponentComponentDTO componentComponentDTO) {
        ComponentComponent entity = componentComponentMapper.toEntity(componentComponentDTO);
        ComponentComponent updatedEntity = componentComponentService.updateComponentComponent(id, entity);
        return ResponseEntity.ok(componentComponentMapper.toDTO(updatedEntity));
    }

    @DeleteMapping("/delete/{id}")
    @Operation(summary = "Delete a component component", description = "Deletes a component component")
    public ResponseEntity<?> deleteComponentComponent(@PathVariable Integer id) {
        try {
            componentComponentService.deleteComponentComponent(id);
            // Return a success message instead of no content
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Component relationship with ID " + id + " successfully deleted");
            return ResponseEntity.ok(response);
        } catch (ResourceNotFoundException e) {
            // Return 404 if the component doesn't exist
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            // Return 500 for other errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to delete component relationship: " + e.getMessage()));
        }
    }

    @PostMapping("/reorder/{parentComponentId}")
    @Operation(summary = "Reorder component components", description = "Reorders component components for a specific parent component")
    public ResponseEntity<Void> reorderComponentComponents(@PathVariable Integer parentComponentId, @RequestBody List<Integer> componentComponentIds) {
        componentComponentService.reorderComponentComponents(parentComponentId, componentComponentIds);
        return ResponseEntity.ok().build();
    }
}

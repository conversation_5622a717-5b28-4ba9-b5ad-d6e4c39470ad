package com.cms.service.impl;

import com.cms.entity.ComponentField;
import com.cms.entity.ComponentFieldConfig;
import com.cms.entity.ComponentListing;
import com.cms.entity.FieldType;
import com.cms.exception.ForeignKeyViolationException;

import com.cms.exception.NullConstraintViolationException;
import com.cms.exception.ResourceNotFoundException;
import com.cms.repository.ComponentFieldRepository;
import com.cms.repository.ComponentRepository;
import com.cms.repository.FieldTypeRepository;
import com.cms.service.ComponentFieldConfigService;
import com.cms.service.ComponentFieldService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class ComponentFieldServiceImpl implements ComponentFieldService {

    private final ComponentFieldRepository componentFieldRepository;
    private final ComponentRepository componentRepository;
    private final ComponentFieldConfigService componentFieldConfigService;
    private final FieldTypeRepository fieldTypeRepository;

    @Override
    public ComponentField getComponentFieldById(Integer id) {
        return componentFieldRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("ComponentField not found with id: " + id));
    }

    @Override
    public List<ComponentField> getAllComponentFields() {
        List<ComponentField> fields = componentFieldRepository.findAll();
        log.debug("Retrieved {} component fields", fields.size());
        return fields;
    }

    @Override
    public List<ComponentField> getComponentFieldsByComponentId(Integer componentId) {
        // Verify component exists
        if (!componentRepository.existsById(componentId)) {
            throw new ResourceNotFoundException("Component not found with id: " + componentId);
        }

        List<ComponentField> fields = componentFieldRepository.findByComponentId(componentId);
        log.debug("Retrieved {} fields for component ID: {}", fields.size(), componentId);
        return fields;
    }

    @Override
    @Transactional
    public ComponentField createComponentField(ComponentField componentField) {
        // Validate field type
        if (componentField.getFieldType() == null || componentField.getFieldType().getId() == null) {
            throw new NullConstraintViolationException("fieldType");
        }

        // Verify field type exists
        FieldType fieldType = fieldTypeRepository.findById(componentField.getFieldType().getId())
                .orElseThrow(() -> new ForeignKeyViolationException("FieldType", "id",
                        componentField.getFieldType().getId()));
        componentField.setFieldType(fieldType);

        // Validate component
        if (componentField.getComponent() == null || componentField.getComponent().getId() == null) {
            throw new NullConstraintViolationException("component");
        }

        // Verify component exists
        ComponentListing component = componentRepository.findById(componentField.getComponent().getId())
                .orElseThrow(() -> new ForeignKeyViolationException("ComponentListing", "id",
                        componentField.getComponent().getId()));
        componentField.setComponent(component);

        // Set display_preference to be the max value for this component + 10
        Integer componentId = component.getId();
        Integer maxDisplayPreference = componentFieldRepository.findMaxDisplayPreferenceByComponentId(componentId);

        // If no fields exist yet, start with 10, otherwise increment by 10
        if (maxDisplayPreference == null) {
            componentField.setDisplayPreference(10);
        } else {
            componentField.setDisplayPreference(maxDisplayPreference + 10);
        }

        log.debug("Setting display_preference to {} for component {}",
                componentField.getDisplayPreference(), componentId);

        // Save the component field first to get an ID
        ComponentField savedField = componentFieldRepository.save(componentField);

        // Process and save field configurations if they exist
        if (componentField.getConfigs() != null && !componentField.getConfigs().isEmpty()) {
            List<ComponentFieldConfig> configsToSave = new ArrayList<>();

            for (ComponentFieldConfig config : componentField.getConfigs()) {
                // Set the reference to the saved field
                config.setComponentField(savedField);
                configsToSave.add(config);
            }

            // Save all configurations
            List<ComponentFieldConfig> savedConfigs = componentFieldConfigService.createComponentFieldConfigs(configsToSave);

            // Update the field with the saved configs
            savedField.setConfigs(savedConfigs);
        }

        return savedField;
    }

    @Override
    @Transactional
    public ComponentField updateComponentField(Integer id, ComponentField componentField) {
        // Verify the field exists
        ComponentField existingField = getComponentFieldById(id);

        // Validate field type
        if (componentField.getFieldType() == null || componentField.getFieldType().getId() == null) {
            throw new NullConstraintViolationException("fieldType");
        }

        // Verify field type exists
        FieldType fieldType = fieldTypeRepository.findById(componentField.getFieldType().getId())
                .orElseThrow(() -> new ForeignKeyViolationException("FieldType", "id",
                        componentField.getFieldType().getId()));
        existingField.setFieldType(fieldType);

        // Update display preference if provided
        if (componentField.getDisplayPreference() != null) {
            log.info("Updating display preference for field {} from {} to {}",
                    id, existingField.getDisplayPreference(), componentField.getDisplayPreference());
            existingField.setDisplayPreference(componentField.getDisplayPreference());
        } else {
            log.warn("No display preference provided for field {}", id);
        }

        // Update dependent field if provided
        existingField.setDependentOn(componentField.getDependentOn());

        // Update additional information if provided
        if (componentField.getAdditionalInformation() != null) {
            existingField.setAdditionalInformation(componentField.getAdditionalInformation());
        }

        // Update component if provided
        if (componentField.getComponent() != null && componentField.getComponent().getId() != null) {
            ComponentListing component = componentRepository.findById(componentField.getComponent().getId())
                    .orElseThrow(() -> new ForeignKeyViolationException("ComponentListing", "id",
                            componentField.getComponent().getId()));
            existingField.setComponent(component);
        }

        // Save the updated field
        ComponentField savedField = componentFieldRepository.save(existingField);
        log.info("Successfully updated component field with ID: {}, display preference: {}",
                savedField.getId(), savedField.getDisplayPreference());
        return savedField;
    }

    @Override
    @Transactional
    public void deleteComponentField(Integer id) {
        // Verify the field exists
        ComponentField field = getComponentFieldById(id);

        // Delete all associated configs first
        if (field.getConfigs() != null && !field.getConfigs().isEmpty()) {
            for (ComponentFieldConfig config : field.getConfigs()) {
                componentFieldConfigService.deleteComponentFieldConfig(config.getId());
            }
        }

        // Then delete the field
        componentFieldRepository.delete(field);

        // Verify deletion
        if (componentFieldRepository.existsById(id)) {
            log.error("Failed to delete component field with ID: {}", id);
            throw new RuntimeException("Failed to delete component field with ID: " + id);
        }

        log.info("Successfully deleted component field with ID: {}", id);
    }

    @Override
    public Integer getNextAvailableId() {
        // Get the maximum ID currently in use
        Integer maxId = componentFieldRepository.findMaxId();

        // If no records exist, start with ID 1, otherwise use max + 1
        return maxId != null ? maxId + 1 : 1;
    }

    /**
     * Helper method to extract field name from additionalInformation JSON
     * @param field The component field
     * @return The field name or a default value if not found
     */
    private String getFieldNameFromAdditionalInfo(ComponentField field) {
        if (field == null || field.getAdditionalInformation() == null || field.getAdditionalInformation().isEmpty()) {
            return "Unknown";
        }

        try {
            // Parse the JSON string
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            com.fasterxml.jackson.databind.JsonNode rootNode = mapper.readTree(field.getAdditionalInformation());

            // Extract the name field if it exists
            if (rootNode.has("name")) {
                return rootNode.get("name").asText();
            }
        } catch (Exception e) {
            log.error("Error parsing additionalInformation for field {}: {}", field.getId(), e.getMessage());
        }

        return "Field " + field.getId();
    }

    @Override
    @Transactional
    public List<ComponentField> reorderComponentFields(Integer componentId, List<Integer> fieldIds) {
        // Verify component exists
        componentRepository.findById(componentId)
                .orElseThrow(() -> new ResourceNotFoundException("Component not found with id: " + componentId));

        log.info("Reordering component fields for component {}: {} fields", componentId, fieldIds.size());
        log.info("Field IDs: {}", fieldIds);

        // Get all fields for this component
        List<ComponentField> fields = componentFieldRepository.findByComponentId(componentId);

        // Create map for quick lookup
        java.util.Map<Integer, ComponentField> fieldMap = new java.util.HashMap<>();
        fields.forEach(field -> fieldMap.put(field.getId(), field));

        // Update display preferences for fields
        List<ComponentField> updatedFields = new ArrayList<>();
        for (int i = 0; i < fieldIds.size(); i++) {
            Integer fieldId = fieldIds.get(i);
            ComponentField field = fieldMap.get(fieldId);

            if (field != null) {
                // Set display preference based on position (multiply by 10 to leave room for insertion)
                int displayPreference = (i + 1) * 10;
                log.info("Setting field {} (name from additionalInfo: {}) display preference to {}",
                        fieldId,
                        getFieldNameFromAdditionalInfo(field),
                        displayPreference);
                field.setDisplayPreference(displayPreference);
                updatedFields.add(field);
            } else {
                log.warn("Field with ID {} not found for component {}", fieldId, componentId);
            }
        }

        // Save all updates
        List<ComponentField> savedFields = componentFieldRepository.saveAll(updatedFields);

        log.info("Saved {} fields with updated display preferences", savedFields.size());

        // Log the saved display preferences with field names
        savedFields.forEach(field ->
            log.info("Field {} ({}) saved with display preference {}",
                    field.getId(),
                    getFieldNameFromAdditionalInfo(field),
                    field.getDisplayPreference()));

        return savedFields;
    }
}

-- Update display_preference values for collection_id 6 fields
-- This will set display_preference values in increments of 10

-- First, let's create a temporary table to hold the IDs and new display_preference values
CREATE TEMPORARY TABLE temp_field_order AS
SELECT 
    id,
    ROW_NUMBER() OVER (ORDER BY id) * 10 AS new_display_preference
FROM 
    public.collection_fields
WHERE 
    collection_id = 6;

-- Now update the collection_fields table
UPDATE public.collection_fields cf
SET display_preference = t.new_display_preference
FROM temp_field_order t
WHERE cf.id = t.id;

-- Drop the temporary table
DROP TABLE temp_field_order;

-- Verify the changes
SELECT id, collection_id, display_preference, additional_information
FROM public.collection_fields
WHERE collection_id = 6
ORDER BY display_preference ASC;

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { componentsApi } from '@/lib/api';
import { useComponentStore } from '@/lib/store';
import { useToast } from '@/hooks/use-toast';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';

export default function ComponentCreate() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { addComponent } = useComponentStore();
  const [loading, setLoading] = useState(false);

  // Form state
  const [name, setName] = useState('');
  const [apiId, setApiId] = useState('');
  const [description, setDescription] = useState('');
  const [isActive, setIsActive] = useState(true);

  // Form validation
  const [errors, setErrors] = useState<{
    name?: string;
    apiId?: string;
  }>({});

  // Generate API ID from name
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    setName(newName);

    // Auto-generate API ID if it hasn't been manually edited
    if (!apiId || apiId === generateApiId(name)) {
      setApiId(generateApiId(newName));
    }
  };

  // Generate API ID from name (lowercase, replace spaces with underscores)
  const generateApiId = (str: string) => {
    return str
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '_');
  };

  // Validate form
  const validateForm = () => {
    const newErrors: {
      name?: string;
      apiId?: string;
    } = {};

    if (!name.trim()) {
      newErrors.name = 'Component name is required';
    }

    if (!apiId.trim()) {
      newErrors.apiId = 'API ID is required';
    } else if (!/^[a-z0-9_]+$/.test(apiId)) {
      newErrors.apiId = 'API ID can only contain lowercase letters, numbers, and underscores';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Prepare component data
      const componentData = {
        componentName: name,
        componentApiId: apiId,
        componentDesc: description,
        isActive
      };

      // Create component
      const response = await componentsApi.create(componentData);

      // Add to store
      addComponent({
        id: response.data.id.toString(),
        componentName: name,
        componentApiId: apiId,
        fields: [],
        isActive
      });

      toast({
        title: 'Success',
        description: 'Component created successfully',
      });

      // Navigate to component edit page
      navigate(`/components/edit/${response.data.id}`);
    } catch (error) {
      console.error('Error creating component:', error);
      toast({
        title: 'Error',
        description: 'Failed to create component',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center mb-6">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => navigate('/components')}
          className="mr-2"
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Create Component</h1>
          <p className="text-muted-foreground">
            Create a new reusable component for your content types
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Component Information</CardTitle>
            <CardDescription>
              Basic information about your component
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={name}
                onChange={handleNameChange}
                placeholder="e.g., Product Details"
                disabled={loading}
              />
              {errors.name && (
                <p className="text-sm text-destructive">{errors.name}</p>
              )}
              <p className="text-sm text-muted-foreground">
                The display name of your component
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="apiId">API ID</Label>
              <Input
                id="apiId"
                value={apiId}
                onChange={(e) => setApiId(e.target.value)}
                placeholder="e.g., product_details"
                disabled={loading}
              />
              {errors.apiId && (
                <p className="text-sm text-destructive">{errors.apiId}</p>
              )}
              <p className="text-sm text-muted-foreground">
                Used in the API. Only lowercase letters, numbers, and underscores
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Describe what this component is used for"
                disabled={loading}
              />
              <p className="text-sm text-muted-foreground">
                Helps others understand the purpose of this component
              </p>
            </div>

            <div className="flex items-center space-x-2 pt-2">
              <Checkbox
                id="isActive"
                checked={isActive}
                onCheckedChange={(checked) => setIsActive(checked as boolean)}
                disabled={loading}
              />
              <Label htmlFor="isActive" className="cursor-pointer">
                Active
              </Label>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              variant="outline"
              onClick={() => navigate('/components')}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Creating...' : 'Create Component'}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  );
}

package com.cms.controller;

import com.cms.payload.JwtAuthResponse;
import com.cms.payload.LoginRequest;
import com.cms.security.JwtTokenProvider;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller specifically for Swagger authentication
 * This provides a dedicated endpoint for Swagger UI to authenticate
 */
@RestController
@RequestMapping("/swagger-auth")
@RequiredArgsConstructor
@Tag(name = "Swagger Authentication", description = "Authentication endpoints for Swagger UI")
public class SwaggerAuthController {

    private final AuthenticationManager authenticationManager;
    private final JwtTokenProvider tokenProvider;

    @PostMapping("/login")
    @Operation(
        summary = "Swagger Login", 
        description = "Authenticate for Swagger UI. After getting the token, click the Authorize button at the top and enter 'Bearer YOUR_TOKEN'",
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "Login credentials",
            content = @Content(
                mediaType = "application/json",
                examples = {
                    @ExampleObject(
                        name = "Admin Login",
                        summary = "Login with admin credentials",
                        value = "{\"username\":\"admin\",\"password\":\"password\"}"                        
                    )
                },
                schema = @Schema(implementation = LoginRequest.class)
            )
        ),
        responses = {
            @ApiResponse(
                responseCode = "200", 
                description = "Login successful, returns JWT token to use with the Authorize button",
                content = @Content(
                    mediaType = "application/json",
                    examples = {
                        @ExampleObject(
                            value = "{\"accessToken\":\"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTYyMDY0NzU0MCwiZXhwIjoxNjIwNzMzOTQwfQ.example_token\"}"
                        )
                    }
                )
            ),
            @ApiResponse(
                responseCode = "401", 
                description = "Invalid credentials",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<JwtAuthResponse> swaggerLogin(@Valid @RequestBody LoginRequest loginRequest) {
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                        loginRequest.getUsername(),
                        loginRequest.getPassword()
                )
        );

        SecurityContextHolder.getContext().setAuthentication(authentication);
        String jwt = tokenProvider.generateToken(authentication);

        return ResponseEntity.ok(new JwtAuthResponse(jwt));
    }
}

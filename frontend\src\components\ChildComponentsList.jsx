import React, { useState, useEffect } from 'react';
import { <PERSON>, Card, Button, Typography, Space, Tooltip, Modal, message, Empty } from 'antd';
import { EditOutlined, DeleteOutlined, DragOutlined, PlusOutlined } from '@ant-design/icons';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import axios from 'axios';
import AddChildComponentModal from './AddChildComponentModal';

const { Title, Text } = Typography;

const ChildComponentsList = ({ parentComponent, onUpdate }) => {
  const [childComponents, setChildComponents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [selectedComponent, setSelectedComponent] = useState(null);

  useEffect(() => {
    if (parentComponent && parentComponent.id) {
      fetchChildComponents();
    } else {
      setChildComponents([]);
      setLoading(false);
    }
  }, [parentComponent]);

  const fetchChildComponents = async () => {
    setLoading(true);
    try {
      const response = await axios.get(`/api/component-components/getByParentId/${parentComponent.id}`);
      setChildComponents(response.data);
    } catch (error) {
      console.error('Error fetching child components:', error);
      message.error('Failed to load child components');
    } finally {
      setLoading(false);
    }
  };

  const handleAddComponent = () => {
    setAddModalVisible(true);
  };

  const handleAddSuccess = () => {
    setAddModalVisible(false);
    fetchChildComponents();
    if (onUpdate) onUpdate();
    message.success('Child component added successfully');
  };

  const handleDeleteComponent = (component) => {
    setSelectedComponent(component);
    setDeleteModalVisible(true);
  };

  const confirmDelete = async () => {
    try {
      await axios.delete(`/api/component-components/delete/${selectedComponent.id}`);
      setDeleteModalVisible(false);
      fetchChildComponents();
      if (onUpdate) onUpdate();
      message.success('Child component removed successfully');
    } catch (error) {
      console.error('Error deleting child component:', error);
      message.error('Failed to remove child component');
    }
  };

  const handleDragEnd = async (result) => {
    if (!result.destination) return;
    
    const items = Array.from(childComponents);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    
    setChildComponents(items);
    
    // Update display preferences on the server
    try {
      const componentIds = items.map(item => item.id);
      await axios.post(`/api/component-components/reorder/${parentComponent.id}`, componentIds);
      if (onUpdate) onUpdate();
    } catch (error) {
      console.error('Error reordering components:', error);
      message.error('Failed to update component order');
      // Revert to original order if the server update fails
      fetchChildComponents();
    }
  };

  const renderComponentItem = (item, index) => {
    const childComponent = item.childComponent;
    
    return (
      <Draggable key={item.id} draggableId={item.id.toString()} index={index}>
        {(provided) => (
          <div
            ref={provided.innerRef}
            {...provided.draggableProps}
            className="child-component-item"
          >
            <Card 
              size="small" 
              className="component-card"
              title={
                <div className="component-card-header">
                  <span {...provided.dragHandleProps} className="drag-handle">
                    <DragOutlined />
                  </span>
                  <span className="component-name">{childComponent.componentDisplayName}</span>
                </div>
              }
              extra={
                <Space>
                  <Tooltip title="Edit">
                    <Button 
                      icon={<EditOutlined />} 
                      size="small" 
                      onClick={() => window.location.href = `/components/edit/${childComponent.id}`}
                    />
                  </Tooltip>
                  <Tooltip title="Remove">
                    <Button 
                      icon={<DeleteOutlined />} 
                      size="small" 
                      danger
                      onClick={() => handleDeleteComponent(item)}
                    />
                  </Tooltip>
                </Space>
              }
            >
              <div className="component-details">
                <div className="component-info">
                  <div><Text type="secondary">API ID:</Text> {childComponent.componentApiId}</div>
                  {item.isRepeatable && (
                    <div className="repeatable-info">
                      <Text type="secondary">Repeatable:</Text> Yes 
                      <Text type="secondary"> (Min: {item.minRepeatOccurrences || 0}, Max: {item.maxRepeatOccurrences || 'unlimited'})</Text>
                    </div>
                  )}
                </div>
              </div>
            </Card>
          </div>
        )}
      </Draggable>
    );
  };

  return (
    <div className="child-components-container">
      <div className="section-header">
        <Title level={5}>Child Components</Title>
        <Button 
          type="primary" 
          icon={<PlusOutlined />} 
          onClick={handleAddComponent}
        >
          Add Component
        </Button>
      </div>

      {childComponents.length > 0 ? (
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="child-components-list">
            {(provided) => (
              <div
                {...provided.droppableProps}
                ref={provided.innerRef}
                className="components-list"
              >
                {childComponents.map((item, index) => renderComponentItem(item, index))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      ) : (
        <Empty 
          description="No child components yet" 
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={handleAddComponent}
          >
            Add First Component
          </Button>
        </Empty>
      )}

      {parentComponent && (
        <AddChildComponentModal
          visible={addModalVisible}
          parentComponent={parentComponent}
          onCancel={() => setAddModalVisible(false)}
          onSuccess={handleAddSuccess}
        />
      )}

      <Modal
        title="Remove Child Component"
        open={deleteModalVisible}
        onOk={confirmDelete}
        onCancel={() => setDeleteModalVisible(false)}
        okText="Remove"
        okButtonProps={{ danger: true }}
      >
        <p>Are you sure you want to remove this child component from {parentComponent?.componentDisplayName}?</p>
        <p>This will only remove the relationship, not delete the component itself.</p>
      </Modal>
    </div>
  );
};

export default ChildComponentsList;

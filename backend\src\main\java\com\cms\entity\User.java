package com.cms.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "users")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class User extends Auditable {

    @Id
     @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "cms_user_seq")
    @SequenceGenerator(name = "cms_user_seq", sequenceName = "cms_user_seq", initialValue = 100, allocationSize = 1)
    private Long id;

    @NotBlank
    @Size(max = 50)
    @Column(name = "username", nullable = false)
    private String username;

    @NotBlank
    @Size(max = 100)
    @Email
    @Column(name = "email", nullable = false)
    private String email;

    @NotBlank
    @Size(max = 120)
    private String password;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @Column(name = "is_logged_in")
    private Boolean isLoggedIn = false;
}

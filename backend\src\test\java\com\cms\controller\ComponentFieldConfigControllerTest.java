package com.cms.controller;

import com.cms.dto.ComponentFieldConfigDTO;
import com.cms.dto.ComponentFieldWithComponentDTO;
import com.cms.entity.ComponentField;
import com.cms.entity.ComponentFieldConfig;
import com.cms.entity.ComponentListing;
import com.cms.entity.FieldConfig;
import com.cms.entity.FieldType;
import com.cms.mapper.ComponentFieldConfigMapper;
import com.cms.service.ComponentFieldConfigService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class ComponentFieldConfigControllerTest {

    @Mock
    private ComponentFieldConfigService componentFieldConfigService;

    @Mock
    private ComponentFieldConfigMapper componentFieldConfigMapper;

    @InjectMocks
    private ComponentFieldConfigController componentFieldConfigController;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testCreateComponentFieldConfig_ReturnsFullObjectInResponse() {
        // Arrange
        ComponentListing component = new ComponentListing();
        component.setId(187);
        component.setComponentName("Registered Agent");
        component.setComponentApiId("Registered_Agent");
        component.setIsActive(true);

        ComponentField componentField = new ComponentField();
        componentField.setId(201);
        componentField.setComponent(component);
        componentField.setAdditionalInformation("Title field");

        FieldConfig fieldConfig = new FieldConfig();
        fieldConfig.setId(5);
        fieldConfig.setConfigName("minLength");

        ComponentFieldConfig config = new ComponentFieldConfig();
        config.setComponentField(componentField);
        config.setFieldConfig(fieldConfig);
        config.setFieldConfigValue("3");

        ComponentFieldWithComponentDTO componentFieldDTO = ComponentFieldWithComponentDTO.fromEntity(componentField);

        ComponentFieldConfigDTO dto = new ComponentFieldConfigDTO();
        dto.setId(1);
        dto.setComponentField(componentFieldDTO);
        dto.setFieldConfig(fieldConfig);
        dto.setFieldConfigValue("3");

        when(componentFieldConfigService.createComponentFieldConfig(any(ComponentFieldConfig.class))).thenReturn(config);
        when(componentFieldConfigMapper.toDTO(any(ComponentFieldConfig.class))).thenReturn(dto);

        // Act
        ResponseEntity<ComponentFieldConfigDTO> response = componentFieldConfigController.createComponentFieldConfig(config);

        // Assert
        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().getId());
        assertNotNull(response.getBody().getComponentField());
        assertEquals(187, response.getBody().getComponentField().getComponent().getId());
        assertEquals("Registered Agent", response.getBody().getComponentField().getComponent().getComponentName());
        assertEquals("Registered_Agent", response.getBody().getComponentField().getComponent().getComponentApiId());
        assertEquals(fieldConfig, response.getBody().getFieldConfig());
        assertEquals("3", response.getBody().getFieldConfigValue());
    }
}

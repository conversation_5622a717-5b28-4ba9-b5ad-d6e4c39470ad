package com.cms.service.impl;

import com.cms.entity.CollectionListing;
import com.cms.entity.ContentEntry;
import com.cms.exception.ResourceNotFoundException;
import com.cms.repository.CollectionListingRepository;
import com.cms.repository.ContentEntryRepository;
import com.cms.service.ContentEntryService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class ContentEntryServiceImpl implements ContentEntryService {

    private final ContentEntryRepository contentEntryRepository;
    private final CollectionListingRepository collectionListingRepository;

    @Override
    public List<ContentEntry> getAllContentEntries() {
        return contentEntryRepository.findAll();
    }

    @Override
    public Optional<ContentEntry> getContentEntryById(Integer id) {
        return contentEntryRepository.findById(id);
    }

    @Override
    public List<ContentEntry> getContentEntriesByCollectionId(Integer collectionId) {
        CollectionListing collection = collectionListingRepository.findById(collectionId)
                .orElseThrow(() -> new ResourceNotFoundException("Collection not found with id: " + collectionId));
        return contentEntryRepository.findByCollection(collection);
    }

    @Override
    public List<ContentEntry> searchContentEntriesByCollectionId(Integer collectionId, String searchText) {
        // Verify collection exists
        if (!collectionListingRepository.existsById(collectionId)) {
            throw new ResourceNotFoundException("Collection not found with id: " + collectionId);
        }
        return contentEntryRepository.findByCollectionIdAndDataJsonContaining(collectionId, searchText);
    }

    @Override
    public List<ContentEntry> findContentEntriesByJsonPath(String jsonPath) {
        return contentEntryRepository.findByJsonPath(jsonPath);
    }

    @Override
    public List<ContentEntry> findContentEntriesByJsonPathAndValue(String jsonPath, String jsonValue) {
        return contentEntryRepository.findByJsonPathAndValue(jsonPath, jsonValue);
    }

    @Override
    public ContentEntry createContentEntry(ContentEntry contentEntry) {
        return contentEntryRepository.save(contentEntry);
    }

    @Override
    public ContentEntry updateContentEntry(Integer id, ContentEntry contentEntry) {
        ContentEntry existingContentEntry = contentEntryRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Content entry not found with id: " + id));

        existingContentEntry.setCollection(contentEntry.getCollection());
        existingContentEntry.setDataJson(contentEntry.getDataJson());

        return contentEntryRepository.save(existingContentEntry);
    }

    @Override
    public void deleteContentEntry(Integer id) {
        if (!contentEntryRepository.existsById(id)) {
            throw new ResourceNotFoundException("Content entry not found with id: " + id);
        }
        contentEntryRepository.deleteById(id);
    }
}

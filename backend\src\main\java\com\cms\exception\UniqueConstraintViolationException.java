package com.cms.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

// Custom status code 701 is used for unique constraint violations
@ResponseStatus(value = HttpStatus.CONFLICT, reason = "Data already exists")
public class UniqueConstraintViolationException extends RuntimeException {

    private final String fieldName;
    private final Object fieldValue;

    public UniqueConstraintViolationException(String fieldName, Object fieldValue) {
        super(String.format("Entity with %s: '%s' already exists", fieldName, fieldValue));
        this.fieldName = fieldName;
        this.fieldValue = fieldValue;
    }

    public String getFieldName() {
        return fieldName;
    }

    public Object getFieldValue() {
        return fieldValue;
    }
}

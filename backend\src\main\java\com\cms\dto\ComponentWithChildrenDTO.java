package com.cms.dto;

import com.cms.entity.ComponentComponent;
import com.cms.entity.ComponentField;
import com.cms.entity.ComponentListing;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class ComponentWithChildrenDTO {
    private Integer id;
    private String createdBy;
    private LocalDateTime createdAt;
    private String modifiedBy;
    private LocalDateTime modifiedAt;
    private String componentName;
    private String componentDisplayName;
    private String componentApiId;
    private Boolean isActive;
    private String getUrl;
    private String postUrl;
    private String updateUrl;
    private List<ComponentField> fields = new ArrayList<>();
    private List<ChildComponentDTO> childComponents = new ArrayList<>();

    public ComponentWithChildrenDTO(ComponentListing component) {
        this.id = component.getId();
        this.createdBy = component.getCreatedBy();
        this.createdAt = component.getCreatedAt();
        this.modifiedBy = component.getModifiedBy();
        this.modifiedAt = component.getModifiedAt();
        this.componentName = component.getComponentName();
        this.componentDisplayName = component.getComponentDisplayName();
        this.componentApiId = component.getComponentApiId();
        this.isActive = component.getIsActive();
        this.getUrl = component.getGetUrl();
        this.postUrl = component.getPostUrl();
        this.updateUrl = component.getUpdateUrl();
        this.fields = component.getFields();

        // Convert child components to DTOs
        if (component.getChildComponents() != null) {
            this.childComponents = component.getChildComponents().stream()
                .map(ChildComponentDTO::new)
                .collect(Collectors.toList());
        }
    }

    @Data
    public static class ChildComponentDTO {
        private Integer id;
        private ComponentDetailsDTO childComponent;
        private Integer displayPreference;
        private Boolean isRepeatable;
        private Boolean isActive;
        private String additionalInformation;

        public ChildComponentDTO(ComponentComponent componentComponent) {
            this.id = componentComponent.getId();
            this.childComponent = new ComponentDetailsDTO(componentComponent.getChildComponent());
            this.displayPreference = componentComponent.getDisplayPreference();
            this.isRepeatable = componentComponent.getIsRepeatable();
            this.isActive = componentComponent.getIsActive();
            this.additionalInformation = componentComponent.getAdditionalInformation();
        }
    }

    @Data
    public static class ComponentDetailsDTO {
        private Integer id;
        private String componentName;
        private String componentDisplayName;
        private String componentApiId;
        private Boolean isActive;

        public ComponentDetailsDTO(ComponentListing component) {
            this.id = component.getId();
            this.componentName = component.getComponentName();
            this.componentDisplayName = component.getComponentDisplayName();
            this.componentApiId = component.getComponentApiId();
            this.isActive = component.getIsActive();
        }
    }
}

package com.cms.dto;

import com.cms.entity.ComponentField;
import com.cms.entity.ComponentFieldConfig;
import com.cms.entity.ComponentListing;
import com.cms.entity.FieldType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ComponentFieldWithComponentDTO {
    private Integer id;
    private ComponentListing component;
    private FieldType fieldType;
    private Integer displayPreference;
    private String additionalInformation;
    private List<ComponentFieldConfig> configs;
    private String createdBy;
    private LocalDateTime createdAt;
    private String modifiedBy;
    private LocalDateTime modifiedAt;
    
    public static ComponentFieldWithComponentDTO fromEntity(ComponentField entity) {
        if (entity == null) {
            return null;
        }
        
        ComponentFieldWithComponentDTO dto = new ComponentFieldWithComponentDTO();
        dto.setId(entity.getId());
        dto.setComponent(entity.getComponent());
        dto.setFieldType(entity.getFieldType());
        dto.setDisplayPreference(entity.getDisplayPreference());
        dto.setAdditionalInformation(entity.getAdditionalInformation());
        dto.setConfigs(entity.getConfigs());
        dto.setCreatedBy(entity.getCreatedBy());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setModifiedBy(entity.getModifiedBy());
        dto.setModifiedAt(entity.getModifiedAt());
        
        return dto;
    }
}

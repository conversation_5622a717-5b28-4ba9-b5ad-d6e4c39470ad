-- =============================================
-- CLIENTS TABLE INITIALIZATION
-- =============================================

-- Create sequence for Client entity if it doesn't exist
CREATE SEQUENCE IF NOT EXISTS cms_client_seq START WITH 100 INCREMENT BY 1;

-- Create the clients table if it doesn't exist
CREATE TABLE IF NOT EXISTS clients (
    id INTEGER PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    created_by VARCHAR(50),
    created_at TIMESTAMP,
    modified_by VA<PERSON><PERSON><PERSON>(50),
    modified_at TIMESTAMP
);

-- Create indexes for clients table
CREATE INDEX IF NOT EXISTS idx_clients_name ON clients(name);

import React, { useState, useEffect } from 'react';
import { Loader2 } from 'lucide-react';

interface SafeImageProps {
  src: string;
  alt: string;
  className?: string;
  assetId?: number | string;
  fallbackSrc?: string;
}

/**
 * A component that safely displays images with ORB prevention
 * Uses multiple strategies to ensure images load properly
 */
export function SafeImage({ src, alt, className, assetId, fallbackSrc }: SafeImageProps) {
  const [imageSrc, setImageSrc] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  useEffect(() => {
    // Reset state when src changes
    setLoading(true);
    setError(false);
    
    // Try to load the image using fetch to bypass ORB
    const loadImage = async () => {
      try {
        // If we have an assetId, use the content endpoint
        if (assetId) {
          const contentUrl = `/api/media/assets/${assetId}/content?t=${new Date().getTime()}`;
          const response = await fetch(contentUrl);
          
          if (!response.ok) {
            throw new Error(`Failed to load image: ${response.status}`);
          }
          
          const blob = await response.blob();
          const objectUrl = URL.createObjectURL(blob);
          setImageSrc(objectUrl);
          setLoading(false);
          
          // Clean up the object URL when component unmounts or src changes
          return () => {
            URL.revokeObjectURL(objectUrl);
          };
        } else {
          // Otherwise use the provided src
          setImageSrc(src);
          setLoading(false);
        }
      } catch (error) {
        console.error('Error loading image:', error);
        setError(true);
        setLoading(false);
        
        // Use fallback if provided
        if (fallbackSrc) {
          setImageSrc(fallbackSrc);
        } else {
          setImageSrc(src); // Try the original src as last resort
        }
      }
    };
    
    loadImage();
  }, [src, assetId, fallbackSrc]);

  if (loading) {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error && !imageSrc) {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <span className="text-destructive text-sm">Failed to load image</span>
      </div>
    );
  }

  return (
    <img
      src={imageSrc || ''}
      alt={alt}
      className={className}
      crossOrigin="anonymous"
      loading="lazy"
      onError={(e) => {
        console.error('Image load error, trying fallback');
        if (fallbackSrc) {
          e.currentTarget.src = fallbackSrc;
        } else if (src !== imageSrc) {
          e.currentTarget.src = src; // Try original src as fallback
        }
      }}
    />
  );
}

package com.cms.repository;

import com.cms.entity.Client;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Client entity operations.
 */
@Repository
public interface ClientRepository extends JpaRepository<Client, Integer> {
    
    /**
     * Find a client by name.
     * 
     * @param name the client name to search for
     * @return an Optional containing the client if found
     */
    Optional<Client> findByName(String name);
    
    /**
     * Check if a client with the given name exists.
     * 
     * @param name the client name to check
     * @return true if a client with the name exists, false otherwise
     */
    boolean existsByName(String name);
    
    /**
     * Find clients created by a specific user.
     * 
     * @param createdBy the username of the creator
     * @return a list of clients created by the specified user
     */
    List<Client> findByCreatedBy(String createdBy);
}

@echo off
echo Executing SQL migration script to add category_id column to all tenant schemas...

REM Set your PostgreSQL connection details
set PGHOST=localhost
set PGPORT=5432
set PGDATABASE=CMS
set PGUSER=postgres
set PGPASSWORD=postgres

REM Execute the SQL script
psql -h %PGHOST% -p %PGPORT% -d %PGDATABASE% -U %PGUSER% -f migration/V2__add_category_id_to_all_tenant_schemas.sql

echo Migration completed.
pause

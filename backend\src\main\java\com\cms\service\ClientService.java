package com.cms.service;

import com.cms.entity.Client;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for Client entity operations
 */
public interface ClientService {
    /**
     * Get all clients
     * @return List of all clients
     */
    List<Client> getAllClients();
    
    /**
     * Get client by ID
     * @param id Client ID
     * @return Optional containing the client if found
     */
    Optional<Client> getClientById(Integer id);
    
    /**
     * Get client by name
     * @param name Client name
     * @return Optional containing the client if found
     */
    Optional<Client> getClientByName(String name);
    
    /**
     * Create a new client
     * @param client Client to create
     * @return Created client
     */
    Client createClient(Client client);
    
    /**
     * Update an existing client
     * @param id Client ID
     * @param client Updated client data
     * @return Updated client
     */
    Client updateClient(Integer id, Client client);
    
    /**
     * Delete a client
     * @param id Client ID
     */
    void deleteClient(Integer id);
    
    /**
     * Check if a client with the given name exists
     * @param name Client name
     * @return true if a client with the name exists, false otherwise
     */
    boolean existsByName(String name);
}

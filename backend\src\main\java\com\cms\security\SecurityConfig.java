package com.cms.security;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.config.Customizer;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import com.cms.util.TenantUtils;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity
@RequiredArgsConstructor
public class SecurityConfig {

    private final JwtAuthenticationFilter jwtAuthenticationFilter;
    private final ApiTokenAuthenticationFilter apiTokenAuthenticationFilter;
    private final JwtAuthenticationEntryPoint unauthorizedHandler;

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
                // Use default CORS configuration from WebConfig
                .cors(Customizer.withDefaults())
                .csrf(csrf -> csrf.disable())
                .exceptionHandling(exception -> exception.authenticationEntryPoint(unauthorizedHandler))
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .authorizeHttpRequests(auth -> auth
                        // Authentication endpoints
                        .requestMatchers("/auth/**").permitAll()
                        .requestMatchers("/swagger-auth/**").permitAll()

                        // Test endpoints
                        .requestMatchers("/test/**").permitAll()
                        .requestMatchers("/swagger-test/public").permitAll()

                        // Public endpoints
                        .requestMatchers("/public/**").permitAll()

                        // Core API endpoints
                        .requestMatchers("/collections/**").permitAll()
                        .requestMatchers("/categories/**").permitAll()
                        .requestMatchers("/clients/**").permitAll()
                        .requestMatchers("/content-entries/**").permitAll()
                        .requestMatchers("/api-tokens/**").permitAll()
                        .requestMatchers("/simplified-collections/**").permitAll()
                        .requestMatchers("/collection-fields/**").permitAll()
                        .requestMatchers("/field-types/**").permitAll()
                        .requestMatchers("/field-configs/**").permitAll()
                        .requestMatchers("/components/**").permitAll()
                        .requestMatchers("/component-management/**").permitAll()
                        .requestMatchers("/component-fields/**").permitAll()
                        .requestMatchers("/component-field-configs/**").permitAll()
                        .requestMatchers("/collection-components/**").permitAll()
                        .requestMatchers("/collection-field-configs/**").permitAll()
                        .requestMatchers("/config-types/**").permitAll()

                        // Media endpoints - permit all media endpoints
                        .requestMatchers("/media/**").permitAll()
                        .requestMatchers("/media/upload").permitAll()
                        .requestMatchers("/media/share/**").permitAll()
                        .requestMatchers("/media/files/**").permitAll()
                        .requestMatchers("/media/assets/**").permitAll()
                        .requestMatchers("/media/folders/**").permitAll()

                        // Debug endpoints
                        .requestMatchers("/debug/**").permitAll()

                        // Tenant endpoints
                        .requestMatchers("/tenants/**").permitAll()

                        // Swagger endpoints
                        .requestMatchers("/api-docs/**").permitAll()
                        .requestMatchers("/swagger-ui/**").permitAll()
                        .requestMatchers("/swagger-ui.html").permitAll()
                        .requestMatchers("/swagger-resources/**").permitAll()
                        .requestMatchers("/v3/api-docs/**").permitAll()
                        .requestMatchers("/swagger-examples/**").permitAll()

                        // Static resources
                        .requestMatchers("/static/**").permitAll()
                        .requestMatchers("/swagger-helper.js").permitAll()
                        .anyRequest().authenticated()
                );

        // Add JWT filter first, then API token filter
        http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(apiTokenAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration authenticationConfiguration) throws Exception {
        // Use our custom tenant-aware authentication provider
        return authenticationConfiguration.getAuthenticationManager();
    }

    /**
     * Create a TenantAwareAuthenticationProvider bean
     */
    @Bean
    public TenantAwareAuthenticationProvider tenantAwareAuthenticationProvider(
            UserDetailsService userDetailsService,
            PasswordEncoder passwordEncoder,
            TenantUtils tenantUtils) {
        return new TenantAwareAuthenticationProvider(userDetailsService, passwordEncoder, tenantUtils);
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}

// Field Type definition
export interface FieldType {
  id: number;
  fieldTypeName: string;
  fieldTypeDesc: string;
  displayName: string;
  helpText: string;
  logoImagePath?: string;
  isActive: boolean;
}

// Field Config definition
export interface FieldConfig {
  configName: string;
  valueType: 'text' | 'number' | 'boolean' | 'url' | 'regex' | 'options' | 'icon' | 'date' | 'password' | 'code' | 'upload';
  isActive?: boolean;
  defaultValue?: any;
  options?: string[];
  description?: string;
}

// Field definition
export interface Field {
  id?: string;
  name: string;
  apiId: string;
  type: string;
  required: boolean;
  unique: boolean;
  description?: string;
  fieldTypeId: number;
  fieldTypeName: string;
  attributes?: Record<string, any>;
  validations?: Record<string, any>;
}

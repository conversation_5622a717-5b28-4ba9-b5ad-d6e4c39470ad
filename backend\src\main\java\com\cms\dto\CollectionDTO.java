package com.cms.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CollectionDTO {
    private Integer id;
    private String collectionName;
    private String collectionDesc;
    private String additionalInformation;
    private String disclaimerText;
    private String collectionApiId;
    private List<ComponentDTO> components = new ArrayList<>();
}


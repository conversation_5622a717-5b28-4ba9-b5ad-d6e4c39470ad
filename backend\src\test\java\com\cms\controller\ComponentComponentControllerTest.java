package com.cms.controller;

import com.cms.dto.ComponentComponentDTO;
import com.cms.entity.ComponentComponent;
import com.cms.entity.ComponentListing;
import com.cms.mapper.ComponentComponentMapper;
import com.cms.service.ComponentComponentService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class ComponentComponentControllerTest {

    @Mock
    private ComponentComponentService componentComponentService;

    @Mock
    private ComponentComponentMapper componentComponentMapper;

    @InjectMocks
    private ComponentComponentController componentComponentController;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testCreateComponentComponent_ReturnsFullObjectInResponse() {
        // Arrange
        ComponentListing parentComponent = new ComponentListing();
        parentComponent.setId(183);
        parentComponent.setComponentName("ParentComponent");
        parentComponent.setComponentDisplayName("Parent Component");
        parentComponent.setComponentApiId("parent_component");
        parentComponent.setIsActive(true);

        ComponentListing childComponent = new ComponentListing();
        childComponent.setId(184);
        childComponent.setComponentName("ChildComponent");
        childComponent.setComponentDisplayName("Child Component");
        childComponent.setComponentApiId("child_component");
        childComponent.setIsActive(true);

        ComponentComponentDTO inputDto = new ComponentComponentDTO();
        inputDto.setParentComponent(parentComponent);
        inputDto.setChildComponent(childComponent);
        inputDto.setIsRepeatable(false);
        inputDto.setDisplayPreference(10);
        inputDto.setIsActive(true);
        inputDto.setAdditionalInformation("{\"name\":\"child_component\",\"description\":\"Child component\"}");

        ComponentComponent entity = new ComponentComponent();
        entity.setId(100);
        entity.setParentComponent(parentComponent);
        entity.setChildComponent(childComponent);
        entity.setIsRepeatable(false);
        entity.setDisplayPreference(10);
        entity.setIsActive(true);
        entity.setAdditionalInformation("{\"name\":\"child_component\",\"description\":\"Child component\"}");

        when(componentComponentMapper.toEntity(any(ComponentComponentDTO.class))).thenReturn(entity);
        when(componentComponentService.createComponentComponent(any(ComponentComponent.class))).thenReturn(entity);

        // Act
        ResponseEntity<ComponentComponentDTO> response = componentComponentController.createComponentComponent(inputDto);

        // Assert
        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(100, response.getBody().getId());
        assertEquals(parentComponent, response.getBody().getParentComponent());
        assertEquals(childComponent, response.getBody().getChildComponent());
        assertEquals(false, response.getBody().getIsRepeatable());
        assertEquals(10, response.getBody().getDisplayPreference());
        assertEquals(true, response.getBody().getIsActive());
        assertEquals("{\"name\":\"child_component\",\"description\":\"Child component\"}", response.getBody().getAdditionalInformation());
    }
}

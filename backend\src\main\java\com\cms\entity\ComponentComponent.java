package com.cms.entity;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIdentityReference;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "component_components")
@Getter
@Setter
@ToString(exclude = "parentComponent")
@NoArgsConstructor
@AllArgsConstructor
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
public class ComponentComponent extends Auditable {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "cms_component_component_seq")
    @SequenceGenerator(name = "cms_component_component_seq", sequenceName = "cms_component_component_seq", initialValue = 100, allocationSize = 1)
    private Integer id;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "parent_component_id")
    @JsonIdentityReference(alwaysAsId = true)
    private ComponentListing parentComponent;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "child_component_id")
    private ComponentListing childComponent;

    @Column(name = "display_preference")
    private Integer displayPreference;

    @Column(name = "is_repeatable")
    private Boolean isRepeatable = false;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @Column(name = "additional_information", columnDefinition = "TEXT")
    private String additionalInformation;
}

package com.cms.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Controller for testing Swagger authentication
 */
@RestController
@RequestMapping("/swagger-test")
@Tag(name = "Swagger Test", description = "Endpoints for testing Swagger authentication")
public class SwaggerTestController {

    @GetMapping("/public")
    @Operation(summary = "Public test endpoint", description = "This endpoint is accessible without authentication")
    public ResponseEntity<Map<String, Object>> publicTest() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "This is a public endpoint");
        response.put("authenticated", SecurityContextHolder.getContext().getAuthentication() != null);
        
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/protected")
    @Operation(
        summary = "Protected test endpoint", 
        description = "This endpoint requires authentication. Use the /swagger-auth/login endpoint to get a token, then click the Authorize button.",
        security = @SecurityRequirement(name = "Bearer Authentication")
    )
    public ResponseEntity<Map<String, Object>> protectedTest() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        
        Map<String, Object> response = new HashMap<>();
        response.put("message", "This is a protected endpoint");
        response.put("authenticated", true);
        response.put("username", auth.getName());
        
        return ResponseEntity.ok(response);
    }
}

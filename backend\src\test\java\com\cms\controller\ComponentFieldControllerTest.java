package com.cms.controller;

import com.cms.dto.ComponentFieldDTO;
import com.cms.entity.ComponentField;
import com.cms.entity.ComponentListing;
import com.cms.entity.FieldType;
import com.cms.mapper.ComponentFieldMapper;
import com.cms.service.ComponentFieldService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class ComponentFieldControllerTest {

    @Mock
    private ComponentFieldService componentFieldService;

    @Mock
    private ComponentFieldMapper componentFieldMapper;

    @InjectMocks
    private ComponentFieldController componentFieldController;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testCreateComponentField_ReturnsFullObjectInResponse() {
        // Arrange
        ComponentListing component = new ComponentListing();
        component.setId(183);
        component.setComponentName("ParentComponent");
        component.setComponentApiId("parent_component");

        FieldType fieldType = new FieldType();
        fieldType.setId(1);
        fieldType.setFieldTypeName("Text");
        fieldType.setDisplayName("Text Field");

        ComponentField componentField = new ComponentField();
        componentField.setId(201);
        componentField.setComponent(component);
        componentField.setFieldType(fieldType);
        componentField.setDisplayPreference(10);
        componentField.setAdditionalInformation("Title field");

        ComponentFieldDTO dto = new ComponentFieldDTO();
        dto.setId(201);
        dto.setComponentId(183);
        dto.setComponentName("ParentComponent");
        dto.setComponentApiId("parent_component");
        dto.setFieldType(fieldType);
        dto.setDisplayPreference(10);
        dto.setAdditionalInformation("Title field");

        when(componentFieldService.createComponentField(any(ComponentField.class))).thenReturn(componentField);
        when(componentFieldMapper.toDTO(any(ComponentField.class))).thenReturn(dto);

        // Act
        ResponseEntity<ComponentFieldDTO> response = componentFieldController.createComponentField(componentField);

        // Assert
        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(201, response.getBody().getId());
        assertEquals(183, response.getBody().getComponentId());
        assertEquals("ParentComponent", response.getBody().getComponentName());
        assertEquals("parent_component", response.getBody().getComponentApiId());
        assertEquals(fieldType, response.getBody().getFieldType());
        assertEquals(10, response.getBody().getDisplayPreference());
        assertEquals("Title field", response.getBody().getAdditionalInformation());
    }
}

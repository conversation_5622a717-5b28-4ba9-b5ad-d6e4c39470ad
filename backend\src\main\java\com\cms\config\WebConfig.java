package com.cms.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
@PropertySource("classpath:cors-config.properties")
public class WebConfig implements WebMvcConfigurer {

    @Value("${cors.allowed-origins:*}")
    private String allowedOrigins;

    @Value("${cors.allowed-methods:GET,POST,PUT,DELETE,OPTIONS,HEAD,PATCH}")
    private String allowedMethods;

    @Value("${cors.allowed-headers:*}")
    private String allowedHeaders;

    @Value("${cors.allow-credentials:false}")
    private boolean allowCredentials;

    @Value("${cors.max-age:3600}")
    private long maxAge;

    @Value("${cors.exposed-headers:Content-Disposition,Cross-Origin-Resource-Policy,Cross-Origin-Embedder-Policy,Cross-Origin-Opener-Policy}")
    private String exposedHeaders;

    @Value("${cors.resource-policy:cross-origin}")
    private String resourcePolicy;

    @Value("${cors.embedder-policy:unsafe-none}")
    private String embedderPolicy;

    @Value("${cors.opener-policy:unsafe-none}")
    private String openerPolicy;

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        // For security in production, you should restrict origins
        // For development, we'll use a more permissive configuration
        if ("*".equals(allowedOrigins)) {
            // When using wildcard, we can't use allowCredentials=true
            registry.addMapping("/**")
                    .allowedOriginPatterns("*") // Use wildcard pattern for origins
                    .allowedMethods(allowedMethods.split(",")) // Use configured methods
                    .allowedHeaders(allowedHeaders.split(",")) // Use configured headers
                    .exposedHeaders(exposedHeaders.split(",")) // Expose configured headers
                    .allowCredentials(false) // Must be false with wildcard origins
                    .maxAge(maxAge); // Use configured max age
        } else {
            // When using specific origins, we can use allowCredentials=true
            registry.addMapping("/**")
                    .allowedOrigins(allowedOrigins.split(",")) // Use configured origins
                    .allowedMethods(allowedMethods.split(",")) // Use configured methods
                    .allowedHeaders(allowedHeaders.split(",")) // Use configured headers
                    .exposedHeaders(exposedHeaders.split(",")) // Expose configured headers
                    .allowCredentials(allowCredentials) // Use configured credentials setting
                    .maxAge(maxAge); // Use configured max age
        }

        System.out.println("CORS configuration applied:");
        System.out.println("Allowed Origins: " + allowedOrigins);
        System.out.println("Allowed Methods: " + allowedMethods);
        System.out.println("Allowed Headers: " + allowedHeaders);
        System.out.println("Allow Credentials: " + allowCredentials);
        System.out.println("Max Age: " + maxAge);
    }

    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();

        // Configure CORS based on whether we're using wildcard origins
        if ("*".equals(allowedOrigins)) {
            // When using wildcard, we can't use allowCredentials=true
            config.setAllowCredentials(false);
            config.addAllowedOriginPattern("*");
            System.out.println("Using wildcard origin pattern with credentials=false");
        } else {
            // When using specific origins, we can use allowCredentials=true
            config.setAllowCredentials(allowCredentials);

            // Add allowed origins
            String[] origins = allowedOrigins.split(",");
            for (String origin : origins) {
                config.addAllowedOrigin(origin.trim());
            }
            System.out.println("Using specific origins with credentials=" + allowCredentials);
        }

        // Add allowed headers and methods
        for (String header : allowedHeaders.split(",")) {
            config.addAllowedHeader(header.trim());
        }

        for (String method : allowedMethods.split(",")) {
            config.addAllowedMethod(method.trim());
        }

        // Add exposed headers
        config.addExposedHeader("Access-Control-Allow-Origin");
        config.addExposedHeader("Access-Control-Allow-Credentials");
        config.addExposedHeader("Access-Control-Allow-Headers");
        config.addExposedHeader("Access-Control-Allow-Methods");
        config.addExposedHeader("Authorization");

        // Set max age
        config.setMaxAge(maxAge);

        source.registerCorsConfiguration("/**", config);
        System.out.println("CorsFilter bean created with specific origins and credentials setting: " + allowCredentials);
        return new CorsFilter(source);
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/swagger-ui/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/swagger-ui/");

        // Add handler for static resources
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");

        // Add specific handler for swagger-helper.js
        registry.addResourceHandler("/swagger-helper.js")
                .addResourceLocations("classpath:/static/swagger-helper.js");
    }

    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        registry.addRedirectViewController("/swagger-ui", "/swagger-ui/index.html");
        registry.addRedirectViewController("/api/swagger-ui", "/swagger-ui/index.html");
        registry.addRedirectViewController("/swagger-ui/", "/swagger-ui/index.html");
    }
}

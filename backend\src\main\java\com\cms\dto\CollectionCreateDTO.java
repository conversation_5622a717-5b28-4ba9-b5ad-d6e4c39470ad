package com.cms.dto;

import com.cms.entity.CollectionListing;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for creating a new collection
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Data transfer object for creating a new collection")
public class CollectionCreateDTO {

    @Schema(description = "Collection name", example = "Blog Posts", required = true)
    private String collectionName;

    @Schema(description = "Collection description", example = "A collection of blog posts")
    private String collectionDesc;

    @Schema(description = "Additional information about the collection", example = "Use this collection to manage all blog content")
    private String additionalInformation;

    @Schema(description = "Disclaimer text for the collection", example = "All blog posts must be approved before publishing")
    private String disclaimerText;

    @Schema(description = "Collection API ID", example = "blog_posts")
    private String collectionApiId;



    /**
     * Convert this DTO to a CollectionListing entity
     * @return CollectionListing entity
     */
    public CollectionListing toEntity() {
        CollectionListing entity = new CollectionListing();
        entity.setCollectionName(this.collectionName);
        entity.setCollectionDesc(this.collectionDesc);
        entity.setAdditionalInformation(this.additionalInformation);
        entity.setDisclaimerText(this.disclaimerText);
        entity.setCollectionApiId(this.collectionApiId);
        return entity;
    }
}

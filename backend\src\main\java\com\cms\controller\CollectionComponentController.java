package com.cms.controller;

import com.cms.config.TenantContextHolder;
import com.cms.dto.CollectionComponentDTO;
import com.cms.entity.CollectionComponent;
import com.cms.exception.ResourceNotFoundException;
import com.cms.mapper.CollectionComponentMapper;
import com.cms.service.CollectionComponentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/collection-components")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Collection Component Management", description = "Collection Component Management API")
public class CollectionComponentController {

    private final CollectionComponentService collectionComponentService;
    private final CollectionComponentMapper collectionComponentMapper;

    @GetMapping("/getAll")
    @Operation(summary = "Get all collection components", description = "Returns a list of all collection components for the current tenant")
    public ResponseEntity<List<CollectionComponentDTO>> getAllCollectionComponents() {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Getting all collection components for tenant: {}", currentTenant);

        List<CollectionComponent> components = collectionComponentService.getAllCollectionComponents();
        log.info("Returning {} collection components for tenant: {}", components.size(), currentTenant);

        return ResponseEntity.ok(collectionComponentMapper.toDTOList(components));
    }

    @GetMapping("/getById/{id}")
    @Operation(summary = "Get collection component by ID", description = "Returns a collection component by its ID for the current tenant")
    public ResponseEntity<CollectionComponentDTO> getCollectionComponentById(@PathVariable Integer id) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Getting collection component by ID: {} for tenant: {}", id, currentTenant);

        CollectionComponent component = collectionComponentService.getCollectionComponentById(id);
        log.info("Found collection component with ID: {} for tenant: {}", id, currentTenant);

        return ResponseEntity.ok(collectionComponentMapper.toDTO(component));
    }

    @GetMapping("/getByCollectionId/{collectionId}")
    @Operation(summary = "Get collection components by collection ID", description = "Returns a list of collection components for a specific collection in the current tenant")
    public ResponseEntity<List<CollectionComponentDTO>> getCollectionComponentsByCollectionId(@PathVariable Integer collectionId) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Getting collection components for collection ID: {} in tenant: {}", collectionId, currentTenant);

        List<CollectionComponent> components = collectionComponentService.getCollectionComponentsByCollectionId(collectionId);
        log.info("Returning {} collection components for collection ID: {} in tenant: {}",
                components.size(), collectionId, currentTenant);

        return ResponseEntity.ok(collectionComponentMapper.toDTOList(components));
    }

    @PostMapping("/create")
    @Operation(
        summary = "Create a new collection component",
        description = "Creates a new collection component",
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "Collection component object to be created",
            content = @Content(
                mediaType = "application/json",
                examples = {
                    @ExampleObject(
                        name = "Standard Collection Component",
                        summary = "Standard collection component creation example",
                        value = "{\"collection\":{\"id\":100},\"component\":{\"id\":101},\"isRepeatable\":false,\"minRepeatOccurrences\":0,\"maxRepeatOccurrences\":0,\"displayPreference\":1,\"displayName\":\"Product Specifications\",\"description\":\"Technical specifications and details for the product\",\"sortOrder\":3,\"isRequired\":true,\"isVisible\":true,\"validationRules\":{\"requiredMessage\":\"Product specifications are required\"}}"
                    )
                },
                schema = @Schema(implementation = CollectionComponentDTO.class)
            )
        ),
        responses = {
            @ApiResponse(
                responseCode = "201",
                description = "Collection component created successfully",
                content = @Content(mediaType = "application/json")
            ),
            @ApiResponse(
                responseCode = "400",
                description = "Invalid input",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<CollectionComponentDTO> createCollectionComponent(@Valid @RequestBody CollectionComponentDTO collectionComponentDTO) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Creating collection component in tenant: {} with collection ID: {} and component ID: {}, thread: {}",
                currentTenant,
                collectionComponentDTO.getCollection() != null ? collectionComponentDTO.getCollection().getId() : null,
                collectionComponentDTO.getComponent() != null ? collectionComponentDTO.getComponent().getId() : null,
                Thread.currentThread().getName());

        // Force the tenant context to ensure it's set correctly for this request
        TenantContextHolder.forceTenantContext(currentTenant);
        log.info("Forced tenant context to: {}, verified: {}",
                currentTenant, TenantContextHolder.getTenantId());

        try {
            // Create a copy of the DTO to preserve the full collection details
            CollectionComponentDTO responseCopy = new CollectionComponentDTO();
            responseCopy.setCollection(collectionComponentDTO.getCollection());
            responseCopy.setComponent(collectionComponentDTO.getComponent());
            responseCopy.setIsRepeatable(collectionComponentDTO.getIsRepeatable());
            responseCopy.setMinRepeatOccurrences(collectionComponentDTO.getMinRepeatOccurrences());
            responseCopy.setMaxRepeatOccurrences(collectionComponentDTO.getMaxRepeatOccurrences());
            responseCopy.setDisplayPreference(collectionComponentDTO.getDisplayPreference());
            responseCopy.setIsActive(collectionComponentDTO.getIsActive());
            responseCopy.setDisplayName(collectionComponentDTO.getDisplayName());
            responseCopy.setDescription(collectionComponentDTO.getDescription());
            responseCopy.setSortOrder(collectionComponentDTO.getSortOrder());
            responseCopy.setIsRequired(collectionComponentDTO.getIsRequired());
            responseCopy.setIsVisible(collectionComponentDTO.getIsVisible());
            responseCopy.setValidationRules(collectionComponentDTO.getValidationRules());

            // Process the entity creation
            log.info("Converting DTO to entity for tenant: {}", currentTenant);
            CollectionComponent collectionComponent = collectionComponentMapper.toEntity(collectionComponentDTO);

            // Verify tenant context before calling service
            String verifiedTenant = TenantContextHolder.getTenantId();
            if (!currentTenant.equals(verifiedTenant)) {
                log.error("CRITICAL: Tenant context changed before service call! Expected: {}, Actual: {}",
                        currentTenant, verifiedTenant);
                // Force it back to the correct tenant
                TenantContextHolder.forceTenantContext(currentTenant);
                log.info("Forced tenant context back to: {} before service call", currentTenant);
            }

            log.info("Creating collection component in service layer for tenant: {}", currentTenant);
            CollectionComponent createdComponent = collectionComponentService.createCollectionComponent(collectionComponent);

            // Set the ID from the created component
            responseCopy.setId(createdComponent.getId());

            log.info("Successfully created collection component with ID: {} in tenant: {}",
                    createdComponent.getId(), currentTenant);

            return new ResponseEntity<>(responseCopy, HttpStatus.CREATED);
        } catch (Exception e) {
            log.error("Error creating collection component in tenant {}: {}", currentTenant, e.getMessage(), e);
            throw e;
        }
    }

    @PutMapping("/update/{id}")
    @Operation(
        summary = "Update a collection component",
        description = "Updates an existing collection component",
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "Collection component object with updated values",
            content = @Content(
                mediaType = "application/json",
                examples = {
                    @ExampleObject(
                        name = "Update Collection Component",
                        summary = "Collection component update example",
                        value = "{\"id\":100,\"collection\":{\"id\":100},\"component\":{\"id\":101},\"isRepeatable\":true,\"minRepeatOccurrences\":1,\"maxRepeatOccurrences\":5,\"displayPreference\":2,\"displayName\":\"Updated Product Specifications\",\"description\":\"Updated technical specifications and details for the product\",\"sortOrder\":3,\"isRequired\":true,\"isVisible\":true,\"validationRules\":{\"requiredMessage\":\"Product specifications are required\"}}"
                    )
                },
                schema = @Schema(implementation = CollectionComponentDTO.class)
            )
        ),
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "Collection component updated successfully",
                content = @Content(mediaType = "application/json")
            ),
            @ApiResponse(
                responseCode = "404",
                description = "Collection component not found",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<CollectionComponentDTO> updateCollectionComponent(
            @PathVariable Integer id,
            @Valid @RequestBody CollectionComponentDTO collectionComponentDTO) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Updating collection component with ID: {} in tenant: {} with collection ID: {} and component ID: {}",
                id, currentTenant,
                collectionComponentDTO.getCollection() != null ? collectionComponentDTO.getCollection().getId() : null,
                collectionComponentDTO.getComponent() != null ? collectionComponentDTO.getComponent().getId() : null);

        try {
            // Create a copy of the DTO to preserve the full collection details
            CollectionComponentDTO responseCopy = new CollectionComponentDTO();
            responseCopy.setCollection(collectionComponentDTO.getCollection());
            responseCopy.setComponent(collectionComponentDTO.getComponent());
            responseCopy.setIsRepeatable(collectionComponentDTO.getIsRepeatable());
            responseCopy.setMinRepeatOccurrences(collectionComponentDTO.getMinRepeatOccurrences());
            responseCopy.setMaxRepeatOccurrences(collectionComponentDTO.getMaxRepeatOccurrences());
            responseCopy.setDisplayPreference(collectionComponentDTO.getDisplayPreference());
            responseCopy.setIsActive(collectionComponentDTO.getIsActive());
            responseCopy.setDisplayName(collectionComponentDTO.getDisplayName());
            responseCopy.setDescription(collectionComponentDTO.getDescription());
            responseCopy.setSortOrder(collectionComponentDTO.getSortOrder());
            responseCopy.setIsRequired(collectionComponentDTO.getIsRequired());
            responseCopy.setIsVisible(collectionComponentDTO.getIsVisible());
            responseCopy.setValidationRules(collectionComponentDTO.getValidationRules());

            // Process the entity update
            log.info("Converting DTO to entity for tenant: {}", currentTenant);
            CollectionComponent collectionComponent = collectionComponentMapper.toEntity(collectionComponentDTO);

            log.info("Updating collection component in service layer for tenant: {}", currentTenant);
            CollectionComponent updatedComponent = collectionComponentService.updateCollectionComponent(id, collectionComponent);

            // Set the ID from the updated component
            responseCopy.setId(updatedComponent.getId());

            log.info("Successfully updated collection component with ID: {} in tenant: {}",
                    updatedComponent.getId(), currentTenant);

            return ResponseEntity.ok(responseCopy);
        } catch (Exception e) {
            log.error("Error updating collection component with ID: {} in tenant {}: {}",
                    id, currentTenant, e.getMessage(), e);
            throw e;
        }
    }

    @DeleteMapping("/deleteById/{id}")
    @Operation(summary = "Delete a collection component", description = "Deletes a collection component by its ID in the current tenant")
    public ResponseEntity<Map<String, Boolean>> deleteCollectionComponent(@PathVariable Integer id) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Deleting collection component with ID: {} in tenant: {}", id, currentTenant);

        try {
            collectionComponentService.deleteCollectionComponent(id);
            Map<String, Boolean> response = new HashMap<>();
            response.put("deleted", Boolean.TRUE);
            log.info("Successfully deleted collection component with ID: {} in tenant: {}", id, currentTenant);
            return ResponseEntity.ok(response);
        } catch (ResourceNotFoundException e) {
            // If the component doesn't exist, return 204 (No Content) instead of 404
            log.info("Collection component with ID: {} not found in tenant: {}, returning 204 No Content",
                    id, currentTenant);
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            // Log the error and return a 500 with a more informative message
            log.error("Error deleting collection component with ID: {} in tenant {}: {}",
                    id, currentTenant, e.getMessage(), e);
            Map<String, Boolean> response = new HashMap<>();
            response.put("deleted", Boolean.FALSE);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/getNextId")
    @Operation(summary = "Get next available ID", description = "Returns the next available ID for a collection component in the current tenant")
    public ResponseEntity<Map<String, Integer>> getNextId() {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Getting next available ID for collection component in tenant: {}", currentTenant);

        Integer nextId = collectionComponentService.getNextAvailableId();
        Map<String, Integer> response = new HashMap<>();
        response.put("nextId", nextId);

        log.info("Next available ID for collection component in tenant {}: {}", currentTenant, nextId);
        return ResponseEntity.ok(response);
    }
}

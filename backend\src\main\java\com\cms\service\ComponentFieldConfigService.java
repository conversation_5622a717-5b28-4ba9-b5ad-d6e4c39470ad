package com.cms.service;

import com.cms.entity.ComponentFieldConfig;

import java.util.List;
import java.util.Optional;

public interface ComponentFieldConfigService {
    List<ComponentFieldConfig> getAllComponentFieldConfigs();
    List<ComponentFieldConfig> getComponentFieldConfigsByComponentFieldId(Integer componentFieldId);
    Optional<ComponentFieldConfig> getComponentFieldConfigById(Integer id);
    ComponentFieldConfig createComponentFieldConfig(ComponentFieldConfig componentFieldConfig);
    List<ComponentFieldConfig> createComponentFieldConfigs(List<ComponentFieldConfig> componentFieldConfigs);
    ComponentFieldConfig updateComponentFieldConfig(Integer id, ComponentFieldConfig componentFieldConfig);
    void deleteComponentFieldConfig(Integer id);
}

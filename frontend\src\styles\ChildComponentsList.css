.child-components-container {
  margin-top: 24px;
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h5 {
  margin-bottom: 0;
}

.components-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.child-component-item {
  margin-bottom: 8px;
}

.component-card {
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.component-card:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.component-card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.drag-handle {
  cursor: grab;
  color: #bfbfbf;
  padding: 4px;
}

.drag-handle:hover {
  color: #1890ff;
}

.component-name {
  font-weight: 500;
}

.component-details {
  padding: 8px 0;
}

.component-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.repeatable-info {
  display: flex;
  gap: 4px;
  align-items: center;
}

.empty-state {
  text-align: center;
  padding: 32px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px dashed #d9d9d9;
}

.empty-state-icon {
  font-size: 32px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

.empty-state-text {
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 16px;
}

package com.cms.controller;

import com.cms.entity.ContentEntry;
import com.cms.exception.NoContentException;
import com.cms.service.ContentEntryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/content-entries")
@RequiredArgsConstructor
@Tag(name = "Content Entry", description = "Content Entry API")
public class ContentEntryController {

    private final ContentEntryService contentEntryService;

    @GetMapping("/getAll")
    @Operation(
        summary = "Get all content entries",
        description = "Returns a list of all content entries",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "List of content entries retrieved successfully",
                content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ContentEntry.class)
                )
            ),
            @ApiResponse(
                responseCode = "204",
                description = "No content entries found",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<List<ContentEntry>> getAllContentEntries() {
        List<ContentEntry> entries = contentEntryService.getAllContentEntries();
        if (entries.isEmpty()) {
            throw new NoContentException("No content entries found");
        }
        return ResponseEntity.ok(entries);
    }

    @GetMapping("/getById/{id}")
    @Operation(
        summary = "Get content entry by ID",
        description = "Returns a content entry by its ID",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "Content entry retrieved successfully",
                content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ContentEntry.class),
                    examples = {
                        @ExampleObject(
                            value = "{\"id\":100,\"collection\":{\"id\":100},\"dataJson\":\"{\\\"title\\\":\\\"Example Title\\\",\\\"content\\\":\\\"Example content text\\\",\\\"author\\\":\\\"John Doe\\\"}\"}"
                        )
                    }
                )
            ),
            @ApiResponse(
                responseCode = "204",
                description = "Content entry not found",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<ContentEntry> getContentEntryById(@PathVariable Integer id) {
        return contentEntryService.getContentEntryById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.noContent().build());
    }

    @GetMapping("/getByCollectionId/{collectionId}")
    @Operation(
        summary = "Get content entries by collection ID",
        description = "Returns a list of content entries by collection ID",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "List of content entries retrieved successfully",
                content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ContentEntry.class)
                )
            ),
            @ApiResponse(
                responseCode = "204",
                description = "No content entries found for the specified collection",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<List<ContentEntry>> getContentEntriesByCollectionId(@PathVariable Integer collectionId) {
        List<ContentEntry> entries = contentEntryService.getContentEntriesByCollectionId(collectionId);
        if (entries.isEmpty()) {
            throw new NoContentException("No content entries found for collection with ID: " + collectionId);
        }
        return ResponseEntity.ok(entries);
    }

    @GetMapping("/searchByCollectionId/{collectionId}")
    @Operation(summary = "Search content entries by collection ID and text", description = "Returns a list of content entries by collection ID that contain the search text")
    public ResponseEntity<List<ContentEntry>> searchContentEntriesByCollectionId(
            @PathVariable Integer collectionId,
            @RequestParam String searchText) {
        List<ContentEntry> entries = contentEntryService.searchContentEntriesByCollectionId(collectionId, searchText);
        if (entries.isEmpty()) {
            throw new NoContentException("No content entries found matching search criteria for collection with ID: " + collectionId);
        }
        return ResponseEntity.ok(entries);
    }

    @GetMapping("/searchByJsonPath")
    @Operation(summary = "Find content entries by JSON path", description = "Returns a list of content entries that match the JSON path expression")
    public ResponseEntity<List<ContentEntry>> findContentEntriesByJsonPath(@RequestParam String jsonPath) {
        List<ContentEntry> entries = contentEntryService.findContentEntriesByJsonPath(jsonPath);
        if (entries.isEmpty()) {
            throw new NoContentException("No content entries found matching JSON path: " + jsonPath);
        }
        return ResponseEntity.ok(entries);
    }

    @GetMapping("/searchByJsonPathAndValue")
    @Operation(summary = "Find content entries by JSON path and value", description = "Returns a list of content entries that match the JSON path expression and value")
    public ResponseEntity<List<ContentEntry>> findContentEntriesByJsonPathAndValue(
            @RequestParam String jsonPath,
            @RequestParam String jsonValue) {
        List<ContentEntry> entries = contentEntryService.findContentEntriesByJsonPathAndValue(jsonPath, jsonValue);
        if (entries.isEmpty()) {
            throw new NoContentException("No content entries found matching JSON path: " + jsonPath + " and value: " + jsonValue);
        }
        return ResponseEntity.ok(entries);
    }

    @PostMapping("/create")
    @Operation(
        summary = "Create a new content entry",
        description = "Creates a new content entry",
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "Content entry object to be created",
            content = @Content(
                mediaType = "application/json",
                examples = {
                    @ExampleObject(
                        name = "Standard Content Entry",
                        summary = "Standard content entry creation example",
                        value = "{\"collection\":{\"id\":100},\"dataJson\":\"{\\\"title\\\":\\\"Example Title\\\",\\\"content\\\":\\\"Example content text\\\",\\\"author\\\":\\\"John Doe\\\"}\"}"
                    )
                },
                schema = @Schema(implementation = ContentEntry.class)
            )
        ),
        responses = {
            @ApiResponse(
                responseCode = "201",
                description = "Content entry created successfully",
                content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ContentEntry.class)
                )
            ),
            @ApiResponse(
                responseCode = "400",
                description = "Invalid input",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<ContentEntry> createContentEntry(@Valid @RequestBody ContentEntry contentEntry) {
        return ResponseEntity.status(HttpStatus.CREATED).body(contentEntryService.createContentEntry(contentEntry));
    }

    @PutMapping("/update/{id}")
    @Operation(
        summary = "Update a content entry",
        description = "Updates an existing content entry",
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "Content entry object with updated values",
            content = @Content(
                mediaType = "application/json",
                examples = {
                    @ExampleObject(
                        name = "Update Content Entry",
                        summary = "Content entry update example",
                        value = "{\"id\":100,\"collection\":{\"id\":100},\"dataJson\":\"{\\\"title\\\":\\\"Updated Title\\\",\\\"content\\\":\\\"Updated content text\\\",\\\"author\\\":\\\"Jane Smith\\\"}\"}"
                    )
                },
                schema = @Schema(implementation = ContentEntry.class)
            )
        ),
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "Content entry updated successfully",
                content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ContentEntry.class)
                )
            ),
            @ApiResponse(
                responseCode = "404",
                description = "Content entry not found",
                content = @Content(mediaType = "application/json")
            ),
            @ApiResponse(
                responseCode = "400",
                description = "Invalid input",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<ContentEntry> updateContentEntry(@PathVariable Integer id, @Valid @RequestBody ContentEntry contentEntry) {
        return ResponseEntity.ok(contentEntryService.updateContentEntry(id, contentEntry));
    }

    @DeleteMapping("/deleteById/{id}")
    @Operation(
        summary = "Delete content entry by ID",
        description = "Deletes a content entry by its ID",
        responses = {
            @ApiResponse(
                responseCode = "204",
                description = "Content entry deleted successfully",
                content = @Content(mediaType = "application/json")
            ),
            @ApiResponse(
                responseCode = "404",
                description = "Content entry not found",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<Void> deleteContentEntry(@PathVariable Integer id) {
        contentEntryService.deleteContentEntry(id);
        return ResponseEntity.noContent().build();
    }
}

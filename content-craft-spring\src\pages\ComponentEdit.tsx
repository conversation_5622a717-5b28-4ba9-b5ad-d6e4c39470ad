import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, Edit, Plus, Trash2, Layers, ChevronDown, ChevronRight, FileText, ArrowUpDown } from 'lucide-react';
import { componentsApi, componentFieldsApi, fieldTypesApi, fieldConfigsApi, componentFieldConfigsApi, componentComponentsApi } from '@/lib/api';
import { useComponentStore, Field, FieldTypeEnum, Component } from '@/lib/store';
import { useToast } from '@/hooks/use-toast';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import DirectFieldTypeSelector from '@/components/content-type/DirectFieldTypeSelector';
import FieldConfigDialog from '@/components/content-type/FieldConfigDialog';
import ChildComponentSelectionDialog from '@/components/content-type/ChildComponentSelectionDialog';
import { ComponentFieldDisplayPreferencesEditor } from '@/components/component-field/ComponentFieldDisplayPreferencesEditor';

export default function ComponentEdit() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const {
    components,
    selectedComponent,
    setSelectedComponent,
    updateComponent,
    setLoading,
    loading
  } = useComponentStore();

  // Component state
  const [name, setName] = useState('');
  const [apiId, setApiId] = useState('');
  const [description, setDescription] = useState('');
  const [isActive, setIsActive] = useState(true);
  const [fields, setFields] = useState<Field[]>([]);
  const [getUrl, setGetUrl] = useState('');
  const [postUrl, setPostUrl] = useState('');
  const [updateUrl, setUpdateUrl] = useState('');

  // Field management state
  const [fieldTypeSelectorOpen, setFieldTypeSelectorOpen] = useState(false);
  const [fieldConfigOpen, setFieldConfigOpen] = useState(false);
  const [selectedFieldType, setSelectedFieldType] = useState<FieldTypeEnum | null>(null);
  const [selectedFieldTypeId, setSelectedFieldTypeId] = useState<number | undefined>(undefined);
  const [fieldToEdit, setFieldToEdit] = useState<Field | undefined>(undefined);
  const [deleteFieldDialogOpen, setDeleteFieldDialogOpen] = useState(false);
  const [fieldToDelete, setFieldToDelete] = useState<string | null>(null);

  // Display preferences state
  const [displayPreferencesOpen, setDisplayPreferencesOpen] = useState(false);

  // Child component management state
  const [childComponentDialogOpen, setChildComponentDialogOpen] = useState(false);
  const [childComponents, setChildComponents] = useState<any[]>([]);
  const [loadingChildComponents, setLoadingChildComponents] = useState(false);
  const [deleteChildComponentDialogOpen, setDeleteChildComponentDialogOpen] = useState(false);
  const [childComponentToDelete, setChildComponentToDelete] = useState<any | null>(null);
  const [expandedChildComponents, setExpandedChildComponents] = useState<{[key: string]: boolean}>({});
  const [childComponentFields, setChildComponentFields] = useState<{[key: string]: any[]}>({});
  const [loadingChildComponentFields, setLoadingChildComponentFields] = useState<{[key: string]: boolean}>({});

  // Form validation
  const [errors, setErrors] = useState<{
    name?: string;
    apiId?: string;
  }>({});

  // Fetch component details
  useEffect(() => {
    const fetchComponent = async () => {
      if (!id) return;

      setLoading(true);
      try {
        console.log('Fetching component with ID:', id);
        const response = await componentsApi.getById(id);
        console.log('Component data received:', response.data);

        if (!response.data) {
          throw new Error('No component data received');
        }

        // Set component data
        setName(response.data.componentName || '');
        setApiId(response.data.componentApiId || '');
        setDescription(response.data.componentDesc || '');
        setIsActive(response.data.isActive !== false);
        setGetUrl(response.data.getUrl || '');
        setPostUrl(response.data.postUrl || '');
        setUpdateUrl(response.data.updateUrl || '');

        // Format the component data to match the expected structure
        const formattedComponent = {
          id: response.data.id.toString(),
          name: response.data.componentName,
          apiId: response.data.componentApiId,
          fields: [],
          isActive: response.data.isActive !== false,
          getUrl: response.data.getUrl || '',
          postUrl: response.data.postUrl || '',
          updateUrl: response.data.updateUrl || '',
          createdAt: '',
          updatedAt: ''
        };

        // Fetch the fields for this component
        try {
          console.log('Fetching fields for component ID:', id);
          const fieldsResponse = await componentFieldsApi.getByComponentId(id);
          console.log('Fields data received:', fieldsResponse.data);

          if (fieldsResponse.data && Array.isArray(fieldsResponse.data)) {
            // Process each field
            const processedFields = await Promise.all(fieldsResponse.data.map(async (fieldData) => {
              try {
                // Parse the additional information to get field metadata
                let fieldMetadata = {};
                if (fieldData.additionalInformation) {
                  try {
                    fieldMetadata = JSON.parse(fieldData.additionalInformation);
                    console.log('Parsed field metadata:', fieldMetadata);
                  } catch (error) {
                    console.error('Error parsing additionalInformation JSON:', error);
                  }
                }

                // If the type is specified in the metadata, use it
                let fieldType = FieldTypeEnum.TEXT; // Default to TEXT
                if (fieldMetadata.type) {
                  console.log('Field type from metadata:', fieldMetadata.type);
                  fieldType = fieldMetadata.type;
                } else if (fieldData.fieldType && fieldData.fieldType.fieldTypeName) {
                  // If type is not in metadata but fieldType object exists, map from fieldTypeName
                  console.log('Field type from fieldType object:', fieldData.fieldType.fieldTypeName);
                  // Map the field type name to enum
                  const fieldTypeName = fieldData.fieldType.fieldTypeName.toUpperCase().replace(/-/g, '_');
                  if (Object.values(FieldTypeEnum).includes(fieldTypeName as FieldTypeEnum)) {
                    fieldType = fieldTypeName as FieldTypeEnum;
                  } else {
                    // Try to find a matching enum
                    for (const key in FieldTypeEnum) {
                      if (key === fieldTypeName) {
                        fieldType = FieldTypeEnum[key as keyof typeof FieldTypeEnum];
                        break;
                      }
                    }
                  }
                }

                // Create the field object with proper type information
                let fieldName = 'Unnamed Field';

                // Try to get the field name from metadata - this is the custom name provided during field configuration
                if (fieldMetadata.name) {
                  fieldName = fieldMetadata.name;
                  console.log('Using field name from metadata:', fieldName);
                } else {
                  // If not in metadata, try to generate a name based on field type
                  if (fieldData.fieldType && fieldData.fieldType.displayName) {
                    fieldName = fieldData.fieldType.displayName;
                    console.log('Using field name from fieldType.displayName:', fieldName);
                  } else if (fieldData.fieldType && fieldData.fieldType.fieldTypeName) {
                    // Format the field type name to be more readable
                    fieldName = fieldData.fieldType.fieldTypeName
                      .split('_')
                      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                      .join(' ');
                    console.log('Using formatted field type name:', fieldName);
                  }
                }

                // Log the field name source for debugging
                console.log(`Field name for field ID ${fieldData.id}: ${fieldName} (source: ${fieldMetadata.name ? 'metadata' : 'generated'})`, fieldData);

                const fieldObject = {
                  id: fieldData.id.toString(),
                  name: fieldName,
                  apiId: fieldMetadata.apiId || `field_${fieldData.id}`,
                  type: fieldType,
                  fieldTypeId: fieldData.fieldType?.id,
                  displayPreference: fieldData.displayPreference || (999), // Use the display preference from the database
                  required: fieldMetadata.required || false,
                  unique: fieldMetadata.unique || false,
                  description: fieldMetadata.description || '',
                  attributes: fieldMetadata.attributes || {},
                  validations: fieldMetadata.validations || {}
                };

                console.log('Processed field object:', fieldObject);
                return fieldObject;
              } catch (parseError) {
                console.error('Error processing field:', parseError);
                return null;
              }
            }));

            // Filter out any null values from processing errors
            let validFields = processedFields.filter(field => field !== null) as Field[];

            // Sort fields by display preference
            validFields = validFields.sort((a, b) => {
              const prefA = typeof a.displayPreference === 'number' ? a.displayPreference : 999;
              const prefB = typeof b.displayPreference === 'number' ? b.displayPreference : 999;
              return prefA - prefB;
            });

            console.log('Sorted fields by display preference:', validFields.map(f => ({ id: f.id, name: f.name, displayPref: f.displayPreference })));

            setFields(validFields);
            formattedComponent.fields = validFields;
          }
        } catch (fieldsError) {
          console.error('Error fetching fields:', fieldsError);
          // Continue with empty fields array
        }

        setSelectedComponent(formattedComponent);
      } catch (error) {
        console.error('Error fetching component:', error);
        toast({
          title: 'Error',
          description: 'Failed to load component details',
          variant: 'destructive',
        });
        navigate('/components');
      } finally {
        setLoading(false);
      }
    };

    if (!selectedComponent || selectedComponent.id !== id) {
      fetchComponent();
    } else {
      // Use data from the store
      setName(selectedComponent.name);
      setApiId(selectedComponent.apiId);
      setFields(selectedComponent.fields);
      setIsActive(selectedComponent.isActive);
      setGetUrl(selectedComponent.getUrl || '');
      setPostUrl(selectedComponent.postUrl || '');
      setUpdateUrl(selectedComponent.updateUrl || '');
    }

    // Fetch child components whenever the component ID changes
    if (id) {
      fetchChildComponents();
    }
  }, [id, setSelectedComponent, navigate, toast, selectedComponent, setLoading]);

  // Fetch child components for this component
  const fetchChildComponents = async () => {
    if (!id) return;

    setLoadingChildComponents(true);
    try {
      const response = await componentComponentsApi.getByParentId(id);
      console.log('Child components data:', response.data);
      const childComponentsData = response.data || [];
      setChildComponents(childComponentsData);

      // Initialize expanded state for each child component
      const initialExpandedState: {[key: string]: boolean} = {};
      childComponentsData.forEach((component: any) => {
        // Start expanded by default for better user experience
        initialExpandedState[component.id] = true;
        // Also fetch fields for each component right away
        console.log('Child component data:', component);
        // Make sure we're using the correct ID from the childComponent object
        if (component.childComponent && component.childComponent.id) {
          fetchChildComponentFields(component.childComponent.id.toString());
        } else {
          console.error('Missing childComponent.id for component:', component);
        }
      });
      setExpandedChildComponents(initialExpandedState);
    } catch (error) {
      console.error('Error fetching child components:', error);
      toast({
        title: 'Error',
        description: 'Failed to load child components',
        variant: 'destructive',
      });
    } finally {
      setLoadingChildComponents(false);
    }
  };

  // Fetch fields for a specific child component
  const fetchChildComponentFields = async (childComponentId: string) => {
    if (!childComponentId) return;

    // Set loading state for this specific child component
    setLoadingChildComponentFields(prev => ({
      ...prev,
      [childComponentId]: true
    }));

    try {
      console.log(`Fetching fields for child component ID: ${childComponentId}`);

      // Use the componentFieldsApi directly but handle the response manually
      const response = await componentFieldsApi.getByComponentId(childComponentId)
        .then(response => {
          console.log(`Fields for child component ${childComponentId}:`, response.data);
          return { success: true, data: response.data };
        })
        .catch(error => {
          // Check if it's a 204 No Content response
          if (error.response && error.response.status === 204) {
            console.log(`No fields found for child component ${childComponentId} (204 response)`);
            return { success: true, data: [] };
          }
          // For other errors, rethrow
          throw error;
        });

      // Log the raw field data for debugging
      console.log(`Raw field data for child component ${childComponentId}:`, response.data);

      // Update the state with the fields
      setChildComponentFields(prev => ({
        ...prev,
        [childComponentId]: response.data || []
      }));

      // Log the fields for debugging
      console.log(`Updated fields for child component ${childComponentId}:`, response.data);

    } catch (error) {
      console.error(`Error fetching fields for child component ${childComponentId}:`, error);
      toast({
        title: 'Error',
        description: `Failed to load fields for child component`,
        variant: 'destructive',
      });
      // Set empty array on error to avoid showing loading indefinitely
      setChildComponentFields(prev => ({
        ...prev,
        [childComponentId]: []
      }));
    } finally {
      setLoadingChildComponentFields(prev => ({
        ...prev,
        [childComponentId]: false
      }));
    }
  };

  // Toggle expanded state for a child component
  const toggleChildComponentExpanded = (relationshipId: string, actualComponentId: string) => {
    // Toggle the expanded state
    setExpandedChildComponents(prev => {
      const newState = {
        ...prev,
        [relationshipId]: !prev[relationshipId]
      };

      // If we're expanding and we haven't loaded the fields yet, fetch them
      if (newState[relationshipId] &&
          (!childComponentFields[actualComponentId] || childComponentFields[actualComponentId].length === 0)) {
        console.log(`Fetching fields for component ${actualComponentId} when expanding relationship ${relationshipId}`);
        fetchChildComponentFields(actualComponentId);
      }

      return newState;
    });
  };

  // Generate API ID from name
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    setName(newName);
  };

  // Validate form
  const validateForm = () => {
    const newErrors: {
      name?: string;
      apiId?: string;
    } = {};

    if (!name.trim()) {
      newErrors.name = 'Component name is required';
    }

    if (!apiId.trim()) {
      newErrors.apiId = 'API ID is required';
    } else if (!/^[a-z0-9_]+$/.test(apiId)) {
      newErrors.apiId = 'API ID can only contain lowercase letters, numbers, and underscores';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm() || !id) {
      return;
    }

    setLoading(true);

    try {
      // Prepare component data
      const componentData = {
        id: parseInt(id),
        componentName: name,
        componentApiId: apiId,
        componentDesc: description,
        isActive,
        getUrl,
        postUrl,
        updateUrl
      };

      // Update component
      await componentsApi.update(id, componentData);

      // Update in store
      updateComponent(id, {
        name,
        apiId,
        isActive,
        getUrl,
        postUrl,
        updateUrl,
        updatedAt: new Date().toISOString()
      });

      toast({
        title: 'Success',
        description: 'Component updated successfully',
      });
    } catch (error) {
      console.error('Error updating component:', error);
      toast({
        title: 'Error',
        description: 'Failed to update component',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle field reordering
  const handleFieldsReordered = async () => {
    if (!id) return;

    try {
      // Fetch the updated fields
      const response = await componentFieldsApi.getByComponentId(id);

      if (response.data && Array.isArray(response.data)) {
        // Process fields from the API response
        const processedFields: Field[] = [];

        response.data.forEach(field => {
          processedFields.push({
            id: field.id.toString(),
            name: field.name || `Field ${field.id}`,
            type: field.fieldType?.name as FieldTypeEnum || FieldTypeEnum.TEXT,
            fieldTypeId: field.fieldType?.id,
            displayPreference: field.displayPreference,
            apiId: field.apiId || '',
            description: field.description || '',
            required: field.required || false,
            unique: field.unique || false,
            attributes: field.attributes || {},
            validations: field.validations || {},
          });
        });

        // Sort fields by display preference
        processedFields.sort((a, b) => {
          const prefA = typeof a.displayPreference === 'number' ? a.displayPreference : 999;
          const prefB = typeof b.displayPreference === 'number' ? b.displayPreference : 999;
          return prefA - prefB;
        });

        // Update the fields state
        setFields(processedFields);

        toast({
          title: 'Success',
          description: 'Fields reordered successfully',
        });
      }
    } catch (error) {
      console.error('Error fetching updated fields:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch updated fields',
        variant: 'destructive',
      });
    }
  };

  // Handle adding a new field - show the field type selector
  const handleAddField = () => {
    setFieldTypeSelectorOpen(true);
  };

  // Handle field type selection
  const handleFieldTypeSelect = (fieldType: FieldTypeEnum, fieldTypeId?: number) => {
    console.log('Selected field type:', fieldType, 'ID:', fieldTypeId);
    setSelectedFieldType(fieldType);
    setSelectedFieldTypeId(fieldTypeId);
    setFieldToEdit(undefined);
    setFieldTypeSelectorOpen(false);
    setFieldConfigOpen(true);
  };

  // Handle editing a field
  const handleEditField = async (field: Field) => {
    console.log('Editing field:', field);

    try {
      // If we have a field ID, fetch the complete field data from the API
      if (field.id && !field.id.startsWith('temp-')) {
        console.log(`Fetching field data for ID ${field.id}`);
        const response = await componentFieldsApi.getById(field.id);
        const fieldData = response.data;
        console.log('Field data from API:', fieldData);

        // Parse the additionalInformation JSON string
        if (fieldData.additionalInformation) {
          try {
            const additionalInfo = JSON.parse(fieldData.additionalInformation);
            console.log('Parsed additional information:', additionalInfo);

            // Create a base field with the parsed basic information
            const baseField = {
              ...field,
              name: additionalInfo.name || field.name,
              apiId: additionalInfo.apiId || field.apiId,
              description: additionalInfo.description || field.description,
              required: additionalInfo.required !== undefined ? additionalInfo.required : field.required,
              unique: additionalInfo.unique !== undefined ? additionalInfo.unique : field.unique,
              attributes: {},
              validations: {}
            };

            // Fetch field configurations from the API
            console.log(`Fetching field configurations for field ID ${field.id}`);
            const configsResponse = await componentFieldConfigsApi.getByComponentFieldId(field.id);
            const fieldConfigs = configsResponse.data;
            console.log('Field configurations from API:', fieldConfigs);

            // Process field configurations into attributes and validations
            if (fieldConfigs && Array.isArray(fieldConfigs)) {
              // Get field config definitions to determine which configs are validations vs attributes
              const fieldConfigDefsResponse = await fieldConfigsApi.getByFieldType(field.fieldTypeId.toString());
              const fieldConfigDefs = fieldConfigDefsResponse.data;
              console.log('Field config definitions:', fieldConfigDefs);

              // Group configs by type (validations vs attributes)
              for (const config of fieldConfigs) {
                if (!config.fieldConfig || !config.fieldConfig.configName) continue;

                const configName = config.fieldConfig.configName;
                const configValue = config.fieldConfigValue;

                // Try to parse the value if it's JSON
                let parsedValue = configValue;
                try {
                  // Check if the value is a JSON string
                  if (configValue &&
                      (configValue.startsWith('{') || configValue.startsWith('[')) &&
                      (configValue.endsWith('}') || configValue.endsWith(']'))) {
                    parsedValue = JSON.parse(configValue);
                  }
                } catch (e) {
                  // If parsing fails, use the original string value
                  console.log(`Failed to parse config value for ${configName}:`, e);
                }

                // Find the config definition to determine if it's a validation or attribute
                const configDef = fieldConfigDefs.find(def => def.configName === configName);

                if (configDef) {
                  // Check if this is a validation config (based on naming convention or config type)
                  const isValidation = [
                    'required', 'minLength', 'maxLength', 'regex', 'api-url', 'minValue', 'maxValue',
                    'minFractionDigits', 'maxFractionDigits'
                  ].includes(configName);

                  if (isValidation) {
                    baseField.validations[configName] = parsedValue;
                  } else {
                    baseField.attributes[configName] = parsedValue;
                  }
                }
              }
            }

            console.log('Processed field data for editing:', baseField);
            setFieldToEdit(baseField);
          } catch (parseError) {
            console.error('Error processing field data:', parseError);
            setFieldToEdit(field);
          }
        } else {
          setFieldToEdit(field);
        }
      } else {
        // For new fields or fields without an ID, use the field data as is
        setFieldToEdit(field);
      }

      // Set the field type and open the config dialog
      setSelectedFieldType(field.type as FieldTypeEnum);
      setSelectedFieldTypeId(field.fieldTypeId);
      setFieldConfigOpen(true);
    } catch (error) {
      console.error('Error fetching field data for editing:', error);
      toast({
        title: 'Error',
        description: 'Failed to load field data for editing',
        variant: 'destructive',
      });

      // Fall back to using the field data as is
      setFieldToEdit(field);
      setSelectedFieldType(field.type as FieldTypeEnum);
      setSelectedFieldTypeId(field.fieldTypeId);
      setFieldConfigOpen(true);
    }
  };

  // Handle saving a field
  const handleSaveField = async (field: Field) => {
    if (!selectedComponent) {
      console.error('Cannot save field: No component selected');
      toast({
        title: 'Error',
        description: 'Cannot save field: No component selected',
        variant: 'destructive',
      });
      return;
    }

    // Validate field data
    if (!field.name || field.name.trim() === '') {
      console.error('Cannot save field: Field name is required');
      toast({
        title: 'Validation Error',
        description: 'Field name is required',
        variant: 'destructive',
      });
      return;
    }

    if (!field.apiId || field.apiId.trim() === '') {
      console.error('Cannot save field: API ID is required');
      toast({
        title: 'Validation Error',
        description: 'API ID is required',
        variant: 'destructive',
      });
      return;
    }

    if (!selectedFieldTypeId) {
      console.error('Cannot save field: Field type ID is required');
      toast({
        title: 'Validation Error',
        description: 'Field type is required',
        variant: 'destructive',
      });
      return;
    }

    console.log('Saving field:', field);
    console.log('Field type:', field.type);
    console.log('Field attributes:', field.attributes);
    console.log('Field validations:', field.validations);

    try {
      // First, get the field type object from the API
      const fieldTypeResponse = await fieldTypesApi.getById(selectedFieldTypeId.toString());
      const fieldType = fieldTypeResponse.data;
      console.log('Field type from API:', fieldType);

      // Get field configurations for this field type
      const fieldConfigsResponse = await fieldConfigsApi.getByFieldType(selectedFieldTypeId.toString());
      const fieldConfigs = fieldConfigsResponse.data;
      console.log('Field configs from API:', fieldConfigs);

      let fieldId;

      if (field.id && !field.id.startsWith('temp-')) {
        // Use existing field ID for updates
        fieldId = parseInt(field.id, 10);
      } else {
        // For new fields, get the next available ID from the backend
        try {
          // This endpoint should be implemented on the backend to return the next available ID
          const nextIdResponse = await componentFieldsApi.getNextId();
          fieldId = nextIdResponse.data.nextId;
          console.log('Got next ID from backend:', fieldId);
        } catch (error) {
          // Fallback: use a sequential ID based on timestamp
          fieldId = Math.floor(Date.now() / 1000);
          console.log('Using fallback ID generation:', fieldId);
        }
      }

      console.log('Using field ID:', fieldId);

      // Create a properly formatted field object for the backend
      const componentFieldData = {
        // Only include ID for updates, not for new fields
        ...(field.id && !field.id.startsWith('temp-') ? { id: fieldId } : {}),
        component: {
          id: parseInt(selectedComponent.id, 10)
        },
        fieldType: {
          id: selectedFieldTypeId
        },
        // Don't set displayPreference - let the backend handle it automatically
        // Store basic data as JSON string (but not configurations)
        additionalInformation: JSON.stringify({
          name: field.name,
          apiId: field.apiId,
          description: field.description || '',
          required: field.required,
          unique: field.unique,
          type: field.type // Store the actual field type enum value
        })
      };

      console.log('Component field data for backend:', componentFieldData);

      let response;
      if (field.id && !field.id.startsWith('temp-')) {
        // Update existing field
        console.log(`Updating existing field with ID ${field.id}`);
        response = await componentFieldsApi.update(field.id, componentFieldData);
      } else {
        // Create new field
        console.log(`Creating new field with generated ID ${fieldId}`);
        response = await componentFieldsApi.create(componentFieldData);
      }

      console.log('API response:', response);

      // After creating/updating the field, create field configurations
      if (response && response.data && response.data.id) {
        const componentFieldId = response.data.id;
        console.log('Saved field ID:', componentFieldId);

        // Process field configurations
        const configsToSave = [];

        // Process attributes
        if (field.attributes) {
          for (const [key, value] of Object.entries(field.attributes)) {
            // Find the corresponding field config
            const config = fieldConfigs.find(c => c.configName === key);
            if (config) {
              configsToSave.push({
                componentField: { id: componentFieldId },
                fieldConfig: { id: config.id },
                fieldConfigValue: typeof value === 'object' ? JSON.stringify(value) : String(value)
                // Note: We don't set an ID here, it will be generated by the database
              });
            }
          }
        }

        // Process validations
        if (field.validations) {
          for (const [key, value] of Object.entries(field.validations)) {
            // Find the corresponding field config
            const config = fieldConfigs.find(c => c.configName === key);
            if (config) {
              configsToSave.push({
                componentField: { id: componentFieldId },
                fieldConfig: { id: config.id },
                fieldConfigValue: typeof value === 'object' ? JSON.stringify(value) : String(value)
                // Note: We don't set an ID here, it will be generated by the database
              });
            }
          }
        }

        // Save field configurations if there are any
        if (configsToSave.length > 0) {
          console.log('Saving field configurations:', configsToSave);
          try {
            const configResponse = await componentFieldConfigsApi.bulkCreate(configsToSave);
            console.log('Field configurations saved:', configResponse.data);
          } catch (configError) {
            console.error('Error saving field configurations:', configError);
            // Continue with the process even if configs fail to save
          }
        }
      }

      // Create a formatted field object for the frontend state
      const formattedField = {
        ...field,
        id: response.data.id.toString(),
        fieldTypeId: selectedFieldTypeId,
        // Ensure the field type is correctly set
        type: field.type
      };

      console.log('Formatted field for frontend:', formattedField);

      // Update the local state
      let updatedFields: Field[];
      if (field.id && !field.id.startsWith('temp-')) {
        // Update existing field
        updatedFields = fields.map(f =>
          f.id === field.id ? formattedField : f
        );
      } else {
        // Add new field
        updatedFields = [
          ...fields,
          formattedField
        ];
      }

      // Update local state
      setFields(updatedFields);

      // Update in store
      updateComponent(selectedComponent.id, { fields: updatedFields });

      toast({
        title: field.id && !field.id.startsWith('temp-') ? 'Field updated' : 'Field added',
        description: `Field "${field.name}" has been ${field.id && !field.id.startsWith('temp-') ? 'updated' : 'added'} successfully`,
      });

      // Close the field config dialog
      setFieldConfigOpen(false);
    } catch (error: any) {
      console.error('Error saving field:', error);

      // Extract more detailed error information
      let errorMessage = `Failed to ${field.id && !field.id.startsWith('temp-') ? 'update' : 'add'} field`;

      if (error.response) {
        console.error('Error response:', error.response.data);
        if (error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        }
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  // Handle deleting a field
  const confirmDeleteField = (fieldId: string) => {
    setFieldToDelete(fieldId);
    setDeleteFieldDialogOpen(true);
  };

  const handleDeleteField = async () => {
    if (!selectedComponent || !fieldToDelete) return;

    try {
      // Delete the field using the API
      await componentFieldsApi.delete(fieldToDelete);

      // Update the local state
      const updatedFields = fields.filter(f => f.id !== fieldToDelete);
      setFields(updatedFields);

      // Update in store
      updateComponent(selectedComponent.id, { fields: updatedFields });

      toast({
        title: 'Field deleted',
        description: 'Field has been deleted successfully',
      });
    } catch (error: any) {
      console.error('Error deleting field:', error);

      // Extract more detailed error information
      let errorMessage = 'Failed to delete field';

      if (error.response) {
        console.error('Error response:', error.response.data);
        if (error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        }
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setDeleteFieldDialogOpen(false);
      setFieldToDelete(null);
    }
  };

  // Handle adding a child component
  const handleAddChildComponent = () => {
    setChildComponentDialogOpen(true);
  };

  const handleChildComponentSelect = async (component: Component, isRepeatable: boolean) => {
    if (!id) return;

    try {
      // Create the payload for adding a child component
      const payload = {
        parentComponent: {
          id: parseInt(id)
        },
        childComponent: {
          id: parseInt(component.id)
        },
        isRepeatable: isRepeatable,
        // Don't set displayPreference - let the backend handle it automatically
        // The backend will set it to the max value + 10 (in the 10, 20, 30 format)
        isActive: true
      };

      // Add the child component using the API
      const response = await componentComponentsApi.create(payload);
      console.log('Child component added:', response.data);

      // Refresh the child components list
      fetchChildComponents();

      toast({
        title: 'Success',
        description: `${component.name} added as a child component`,
      });
    } catch (error: any) {
      console.error('Error adding child component:', error);

      // Extract more detailed error information
      let errorMessage = 'Failed to add child component';

      if (error.response) {
        console.error('Error response:', error.response.data);
        if (error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        }
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setChildComponentDialogOpen(false);
    }
  };

  // Handle deleting a child component
  const confirmDeleteChildComponent = (childComponent: any) => {
    setChildComponentToDelete(childComponent);
    setDeleteChildComponentDialogOpen(true);
  };

  const handleDeleteChildComponent = async () => {
    if (!childComponentToDelete) return;

    try {
      // Delete the child component using the API
      await componentComponentsApi.delete(childComponentToDelete.id);

      // Refresh the child components list
      fetchChildComponents();

      toast({
        title: 'Success',
        description: 'Child component removed successfully',
      });
    } catch (error: any) {
      console.error('Error deleting child component:', error);

      // Extract more detailed error information
      let errorMessage = 'Failed to remove child component';

      if (error.response) {
        console.error('Error response:', error.response.data);
        if (error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        }
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setDeleteChildComponentDialogOpen(false);
      setChildComponentToDelete(null);
    }
  };

  // Helper function to render field type icon and label
  const renderFieldTypeIcon = (type: FieldTypeEnum) => {
    // This is a simplified version - you might want to use actual icons
    return <div className="w-4 h-4 rounded-full bg-primary/20 text-primary text-[10px] flex items-center justify-center">
      {type.charAt(0).toUpperCase()}
    </div>;
  };

  // Helper function to extract field name from field data
  const getFieldName = (field: any): string => {
    // Try direct properties first
    if (field.fieldDisplayName) return field.fieldDisplayName;
    if (field.fieldName) return field.fieldName;
    if (field.name) return field.name;

    // Try to parse from additionalInformation
    if (field.additionalInformation) {
      try {
        const metadata = JSON.parse(field.additionalInformation);
        if (metadata.name) return metadata.name;
      } catch (e) {
        console.error('Error parsing additionalInformation:', e);
      }
    }

    // If all else fails, use the field type name
    if (field.fieldType) {
      if (field.fieldType.displayName) {
        return field.fieldType.displayName;
      }
      if (field.fieldType.fieldTypeName) {
        // Format the field type name to be more readable
        return field.fieldType.fieldTypeName
          .split('_')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' ');
      }
    }

    // Last resort
    return 'Unnamed Field';
  };

  const getFieldTypeLabel = (type: FieldTypeEnum): string => {
    // Map specific field types to proper display names
    const typeMap: Record<string, string> = {
      'date': 'Date',
      'text': 'Text',
      'number': 'Number',
      'boolean': 'Boolean',
      'json': 'JSON',
      'email': 'Email',
      'media': 'Media',
      'enumeration': 'Enumeration',
      'relation': 'Relation',
      'uid': 'UID',
      'component': 'Component',
      'dynamic_zone': 'Dynamic Zone',
      'input_mask': 'Input Mask',
      'input_textarea': 'Textarea',
      'mask': 'Mask',
      'calendar': 'Calendar',
      'editor': 'Editor',
      'password': 'Password',
      'autocomplete': 'Autocomplete',
      'cascade_select': 'Cascade Select',
      'dropdown': 'Dropdown',
      'file': 'File',
      'multi_state_checkbox': 'Multi State Checkbox',
      'multi_select': 'Multi Select',
      'mention': 'Mention',
      'textarea': 'Textarea',
      'otp': 'OTP'
    };

    // Check if we have a direct mapping
    if (typeMap[type]) {
      return typeMap[type];
    }

    // Fall back to the default conversion
    return type
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-8 pb-4 border-b">
        <div className="flex items-center">
          <Button
            variant="outline"
            size="icon"
            onClick={() => navigate('/components')}
            className="mr-4 rounded-full border-gray-200"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{name || 'Edit Component'}</h1>
            <p className="text-muted-foreground mt-1">
              Edit component details and manage fields
            </p>
          </div>
        </div>
        <div>
          <Button
            variant="outline"
            onClick={() => navigate('/components')}
            className="mr-2"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="details" className="space-y-6">
        <TabsList>
          <TabsTrigger value="details">Component Details</TabsTrigger>
          <TabsTrigger value="fields">Fields</TabsTrigger>
          <TabsTrigger value="childComponents">Child Components</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-6">
          <form onSubmit={handleSubmit}>
            <Card>
              <CardHeader>
                <CardTitle>Component Information</CardTitle>
                <CardDescription>
                  Basic information about your component
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-base font-medium">Name</Label>
                    <Input
                      id="name"
                      value={name}
                      onChange={handleNameChange}
                      placeholder="e.g., Product Details"
                      disabled={loading}
                      className="h-10"
                    />
                    {errors.name && (
                      <p className="text-sm text-destructive">{errors.name}</p>
                    )}
                    <p className="text-sm text-muted-foreground">
                      The display name of your component
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="apiId" className="text-base font-medium">API ID</Label>
                    <Input
                      id="apiId"
                      value={apiId}
                      onChange={(e) => setApiId(e.target.value)}
                      placeholder="e.g., product_details"
                      disabled={loading}
                      className="h-10"
                    />
                    {errors.apiId && (
                      <p className="text-sm text-destructive">{errors.apiId}</p>
                    )}
                    <p className="text-sm text-muted-foreground">
                      Used in the API. Only lowercase letters, numbers, and underscores
                    </p>
                  </div>
                </div>

                <div className="space-y-2 mb-6">
                  <Label htmlFor="description" className="text-base font-medium">Description (Optional)</Label>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Describe what this component is used for"
                    disabled={loading}
                    className="min-h-[100px] resize-y"
                  />
                  <p className="text-sm text-muted-foreground">
                    Helps others understand the purpose of this component
                  </p>
                </div>

                <div className="space-y-4 mt-6 border-t pt-6">
                  <h3 className="text-base font-medium">API Endpoints</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Configure API endpoints for this component
                  </p>

                  <div className="space-y-2">
                    <Label htmlFor="getUrl" className="text-base font-medium">GET URL</Label>
                    <Input
                      id="getUrl"
                      value={getUrl}
                      onChange={(e) => setGetUrl(e.target.value)}
                      placeholder="e.g., /api/products"
                      disabled={loading}
                      className="h-10"
                    />
                    <p className="text-sm text-muted-foreground">
                      URL for retrieving component data
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="postUrl" className="text-base font-medium">POST URL</Label>
                    <Input
                      id="postUrl"
                      value={postUrl}
                      onChange={(e) => setPostUrl(e.target.value)}
                      placeholder="e.g., /api/products"
                      disabled={loading}
                      className="h-10"
                    />
                    <p className="text-sm text-muted-foreground">
                      URL for creating new component data
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="updateUrl" className="text-base font-medium">UPDATE URL</Label>
                    <Input
                      id="updateUrl"
                      value={updateUrl}
                      onChange={(e) => setUpdateUrl(e.target.value)}
                      placeholder="e.g., /api/products/{id}"
                      disabled={loading}
                      className="h-10"
                    />
                    <p className="text-sm text-muted-foreground">
                      URL for updating existing component data
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3 pt-2 border-t mt-6">
                  <Checkbox
                    id="isActive"
                    checked={isActive}
                    onCheckedChange={(checked) => setIsActive(checked as boolean)}
                    disabled={loading}
                    className="h-5 w-5"
                  />
                  <Label htmlFor="isActive" className="cursor-pointer text-base font-medium">
                    Active
                  </Label>
                  <p className="text-sm text-muted-foreground ml-2">
                    When active, this component can be used in content types
                  </p>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end border-t bg-muted/20 py-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => navigate('/components')}
                  disabled={loading}
                  className="mr-2"
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={loading} className="px-6">
                  {loading ? 'Saving...' : 'Save Changes'}
                </Button>
              </CardFooter>
            </Card>
          </form>
        </TabsContent>

        <TabsContent value="fields" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between border-b">
              <div>
                <CardTitle>Component Fields</CardTitle>
                <CardDescription>
                  Define the structure of your component
                </CardDescription>
              </div>
              <div className="w-full flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setDisplayPreferencesOpen(true)}
                  className="flex items-center bg-primary/5 hover:bg-primary/10 border-primary/20"
                >
                  <ArrowUpDown className="mr-2 h-4 w-4 text-primary" />
                  <span className="text-primary font-medium">Edit Display Preferences</span>
                </Button>
                <Button onClick={handleAddField} className="bg-primary hover:bg-primary/90">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Field
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-6">
              {loading ? (
                <div className="py-12 text-center">
                  <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4"></div>
                  <p className="text-muted-foreground">Loading fields...</p>
                </div>
              ) : fields.length === 0 ? (
                <div className="py-12 text-center border border-dashed rounded-lg bg-muted/10">
                  <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center mb-4">
                    <Plus className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-medium mb-2">No fields yet</h3>
                  <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                    Add fields to define the structure of your component. Fields determine what data can be stored in this component.
                  </p>
                  <div className="flex justify-center">
                    <Button onClick={handleAddField} className="bg-primary hover:bg-primary/90 px-6 w-full max-w-md">
                      <Plus className="mr-2 h-4 w-4" />
                      Add Your First Field
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-0 border rounded-md overflow-hidden">
                  <div className="grid grid-cols-12 gap-4 p-3 bg-muted/30 border-b text-sm font-medium text-muted-foreground">
                    <div className="col-span-1 text-center">ORDER</div>
                    <div className="col-span-4">FIELD NAME</div>
                    <div className="col-span-5">TYPE</div>
                    <div className="col-span-2 text-right pr-4">ACTIONS</div>
                  </div>
                  {fields.map((field, index) => (
                    <div
                      key={field.id}
                      className={`grid grid-cols-12 gap-4 p-3 items-center hover:bg-muted/20 ${index !== fields.length - 1 ? 'border-b' : ''}`}
                    >
                      <div className="col-span-1 text-center">
                        <div className="w-6 h-6 rounded-full bg-primary/10 text-primary flex items-center justify-center mx-auto border border-border">
                          {field.displayPreference ? Math.floor(field.displayPreference / 10) : '?'}
                        </div>
                      </div>
                      <div className="col-span-4 font-medium">{field.name}</div>
                      <div className="col-span-5 flex items-center">
                        <div className="w-6 h-6 rounded-md bg-primary/10 text-primary flex items-center justify-center mr-2 border border-border">
                          {(() => {
                            // Determine the correct field type based on fieldTypeId
                            let displayType = field.type || 'TEXT';

                            // Map fieldTypeId to correct type for display
                            if (field.fieldTypeId) {
                              switch (field.fieldTypeId) {
                                case 1: return 'T'; // TEXT
                                case 2: return 'N'; // NUMBER
                                case 3: return 'D'; // DATE
                                case 4: return 'I'; // IMAGE
                                case 12: return '▼'; // DROPDOWN
                                case 19: return '@'; // EMAIL
                                case 20: return '✓'; // BOOLEAN
                                default: return displayType.charAt(0).toUpperCase();
                              }
                            }

                            return displayType.charAt(0).toUpperCase();
                          })()}
                        </div>
                        <span>
                          {(() => {
                            // Map fieldTypeId to correct type name for display
                            if (field.fieldTypeId) {
                              switch (field.fieldTypeId) {
                                case 1: return 'Text';
                                case 2: return 'Number';
                                case 3: return 'Date';
                                case 4: return 'Image';
                                case 12: return 'Dropdown';
                                case 19: return 'Email';
                                case 20: return 'Boolean';
                                default: return field.type ? getFieldTypeLabel(field.type) : 'Unknown';
                              }
                            }

                            return field.type ? getFieldTypeLabel(field.type) : 'Unknown';
                          })()}
                        </span>
                      </div>
                      <div className="col-span-2 flex justify-end gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditField(field)}
                          className="h-8 w-8 p-0"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => confirmDeleteField(field.id!)}
                          className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
            {fields.length > 0 && (
              <CardFooter className="border-t bg-muted/20 py-4">
                <Button
                  onClick={handleAddField}
                  className="w-full bg-primary hover:bg-primary/90"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Add Another Field
                </Button>
              </CardFooter>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="childComponents" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between border-b">
              <div>
                <CardTitle>Child Components</CardTitle>
                <CardDescription>
                  Add nested components to this component
                </CardDescription>
              </div>
              <Button onClick={handleAddChildComponent} className="bg-primary hover:bg-primary/90">
                <Plus className="mr-2 h-4 w-4" />
                Add Component
              </Button>
            </CardHeader>
            <CardContent className="p-6">
              {loadingChildComponents ? (
                <div className="py-12 text-center">
                  <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4"></div>
                  <p className="text-muted-foreground">Loading child components...</p>
                </div>
              ) : childComponents.length === 0 ? (
                <div className="py-12 text-center border border-dashed rounded-lg bg-muted/10">
                  <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center mb-4">
                    <Layers className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-medium mb-2">No child components yet</h3>
                  <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                    Add child components to create nested structures. Child components can be used to build complex content models.
                  </p>
                  <Button onClick={handleAddChildComponent} className="bg-primary hover:bg-primary/90 px-6">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Your First Child Component
                  </Button>
                </div>
              ) : (
                <div className="space-y-0 border rounded-md overflow-hidden">
                  <div className="grid grid-cols-12 gap-4 p-3 bg-muted/30 border-b text-sm font-medium text-muted-foreground">
                    <div className="col-span-5">COMPONENT NAME</div>
                    <div className="col-span-5">DETAILS</div>
                    <div className="col-span-2 text-right pr-4">ACTIONS</div>
                  </div>
                  {childComponents.map((childComponent, index) => (
                    <div
                      key={childComponent.id}
                      className={`border-b`}
                    >
                      <div className="grid grid-cols-12 gap-4 p-3 items-center hover:bg-muted/20">
                        <div className="col-span-5 font-medium flex items-center">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleChildComponentExpanded(childComponent.id, childComponent.childComponent.id.toString())}
                            className="h-8 w-8 p-0 mr-2 text-primary"
                          >
                            {expandedChildComponents[childComponent.id] ?
                              <ChevronDown className="h-4 w-4" /> :
                              <ChevronRight className="h-4 w-4" />}
                          </Button>
                          <div className="flex flex-col">
                            <span>{childComponent.childComponent.componentDisplayName || childComponent.childComponent.componentName}</span>
                            {!expandedChildComponents[childComponent.id] && (
                              <span className="text-xs text-muted-foreground">
                                Click to view fields
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="col-span-5">
                          <div className="flex flex-col">
                            <span className="text-sm text-muted-foreground">
                              API ID: {childComponent.childComponent.componentApiId}
                            </span>
                            {childComponent.isRepeatable && (
                              <span className="text-sm text-muted-foreground">
                                Repeatable: Yes
                                {childComponent.minRepeatOccurrences !== null && (
                                  <span> (Min: {childComponent.minRepeatOccurrences})</span>
                                )}
                                {childComponent.maxRepeatOccurrences !== null && (
                                  <span> (Max: {childComponent.maxRepeatOccurrences})</span>
                                )}
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="col-span-2 flex justify-end gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => navigate(`/components/edit/${childComponent.childComponent.id}`)}
                            className="h-8 w-8 p-0"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => confirmDeleteChildComponent(childComponent)}
                            className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      {/* Fields section - only visible when expanded */}
                      {expandedChildComponents[childComponent.id] && (
                        <div className="pl-12 pr-4 pb-3 bg-muted/5">
                          {loadingChildComponentFields[childComponent.childComponent.id] ? (
                            <div className="py-4 flex items-center justify-center">
                              <div className="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                              <span className="text-sm text-muted-foreground">Loading fields...</span>
                            </div>
                          ) : childComponentFields[childComponent.childComponent.id]?.length > 0 ? (
                            <div className="space-y-2 py-2">
                              <div className="text-sm font-medium text-muted-foreground mb-2 flex items-center">
                                <FileText className="h-4 w-4 mr-2" />
                                Fields:
                              </div>
                              <div className="space-y-1">
                                {childComponentFields[childComponent.childComponent.id].map((field: any) => (
                                  <div key={field.id} className="flex items-center py-1 px-2 rounded-sm hover:bg-muted/20">
                                    <div className="w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mr-2">
                                      <span className="text-xs text-blue-600 font-medium">{field.fieldType.fieldTypeName.charAt(0).toUpperCase()}</span>
                                    </div>
                                    <span className="text-sm">
                                      {getFieldName(field)}
                                      <span className="text-xs text-muted-foreground ml-2">
                                        ({field.fieldType.displayName || field.fieldType.fieldTypeName})
                                      </span>
                                    </span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          ) : (
                            <div className="py-3 text-center">
                              <span className="text-sm text-muted-foreground">
                                No fields found for this component.
                                <Button
                                  variant="link"
                                  className="p-0 h-auto text-sm text-primary"
                                  onClick={() => navigate(`/components/edit/${childComponent.childComponent.id}`)}
                                >
                                  Click here to add fields
                                </Button>
                              </span>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
            {childComponents.length > 0 && (
              <CardFooter className="border-t bg-muted/20 py-4">
                <Button
                  onClick={handleAddChildComponent}
                  className="w-full"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Add Another Component
                </Button>
              </CardFooter>
            )}
          </Card>
        </TabsContent>
      </Tabs>

      {/* Field Type Selector Dialog */}
      <DirectFieldTypeSelector
        open={fieldTypeSelectorOpen}
        onClose={() => setFieldTypeSelectorOpen(false)}
        onSelect={handleFieldTypeSelect}
      />

      {/* Field Configuration Dialog */}
      <FieldConfigDialog
        open={fieldConfigOpen}
        onClose={() => setFieldConfigOpen(false)}
        onSave={handleSaveField}
        field={fieldToEdit}
        fieldType={selectedFieldType || FieldTypeEnum.TEXT}
        fieldTypeId={selectedFieldTypeId}
        collectionName={name}
      />

      {/* Delete Field Confirmation Dialog */}
      <Dialog open={deleteFieldDialogOpen} onOpenChange={setDeleteFieldDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Field</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this field? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteFieldDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteField}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Child Component Selection Dialog */}
      <ChildComponentSelectionDialog
        open={childComponentDialogOpen}
        onClose={() => setChildComponentDialogOpen(false)}
        onSelect={handleChildComponentSelect}
        parentComponentId={id || ''}
      />

      {/* Delete Child Component Confirmation Dialog */}
      <Dialog open={deleteChildComponentDialogOpen} onOpenChange={setDeleteChildComponentDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Remove Child Component</DialogTitle>
            <DialogDescription>
              Are you sure you want to remove this child component? This will only remove the relationship, not the component itself.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteChildComponentDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteChildComponent}
            >
              Remove
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Component Field Display Preferences Editor */}
      {id && (
        <ComponentFieldDisplayPreferencesEditor
          open={displayPreferencesOpen}
          onOpenChange={setDisplayPreferencesOpen}
          componentId={id}
          fields={fields}
          onFieldsReordered={handleFieldsReordered}
        />
      )}
    </div>
  );
}

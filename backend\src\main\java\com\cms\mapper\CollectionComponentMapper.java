package com.cms.mapper;

import com.cms.dto.CollectionComponentDTO;
import com.cms.entity.CollectionComponent;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class CollectionComponentMapper {

    /**
     * Convert an entity to a DTO
     * @param entity the entity to convert
     * @return the DTO
     */
    public CollectionComponentDTO toDTO(CollectionComponent entity) {
        return CollectionComponentDTO.fromEntity(entity);
    }

    /**
     * Convert a list of entities to a list of DTOs
     * @param entities the entities to convert
     * @return the DTOs
     */
    public List<CollectionComponentDTO> toDTOList(List<CollectionComponent> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Convert a DTO to an entity
     * @param dto the DTO to convert
     * @return the entity
     */
    public CollectionComponent toEntity(CollectionComponentDTO dto) {
        return dto.toEntity();
    }

    /**
     * Convert a list of DTOs to a list of entities
     * @param dtos the DTOs to convert
     * @return the entities
     */
    public List<CollectionComponent> toEntityList(List<CollectionComponentDTO> dtos) {
        if (dtos == null) {
            return null;
        }
        return dtos.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }
}

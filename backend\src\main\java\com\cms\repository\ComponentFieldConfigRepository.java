package com.cms.repository;

import com.cms.entity.ComponentField;
import com.cms.entity.ComponentFieldConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ComponentFieldConfigRepository extends JpaRepository<ComponentFieldConfig, Integer> {
    List<ComponentFieldConfig> findByComponentField(ComponentField componentField);
    List<ComponentFieldConfig> findByComponentFieldId(Integer componentFieldId);
}

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>CMS API Documentation</title>
  <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui.css">
  <style>
    html {
      box-sizing: border-box;
      overflow: -moz-scrollbars-vertical;
      overflow-y: scroll;
    }

    *,
    *:before,
    *:after {
      box-sizing: inherit;
    }

    body {
      margin: 0;
      background: #fafafa;
    }

    .swagger-token-helper {
      margin: 10px 0;
      padding: 15px;
      background-color: #f0f0f0;
      border-radius: 4px;
      border-left: 4px solid #4990e2;
    }

    .swagger-token-helper h3 {
      margin-top: 0;
      color: #3b4151;
    }

    .swagger-token-helper code {
      background-color: #e8e8e8;
      padding: 2px 4px;
      border-radius: 3px;
    }

    .swagger-token-helper input {
      width: 100%;
      padding: 8px;
      margin: 8px 0;
      box-sizing: border-box;
      border: 1px solid #d8dde3;
      border-radius: 4px;
    }

    .swagger-token-helper button {
      padding: 8px 16px;
      background-color: #4990e2;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }

    .swagger-token-helper button:hover {
      background-color: #3672b9;
    }

    .auth-notice {
      background-color: #fffacd;
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
      border-left: 4px solid #ffd700;
    }
  </style>
</head>
<body>
  <div id="swagger-ui"></div>

  <script src="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui-bundle.js"></script>
  <script src="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui-standalone-preset.js"></script>
  <script src="/api/swagger-helper.js"></script>
  <script>
    window.onload = function() {
      // Begin Swagger UI call region
      const ui = SwaggerUIBundle({
        url: "/api/openapi.json",
        dom_id: '#swagger-ui',
        deepLinking: true,
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        plugins: [
          SwaggerUIBundle.plugins.DownloadUrl
        ],
        layout: "StandaloneLayout",
        docExpansion: "none",
        tagsSorter: "alpha",
        operationsSorter: "method",
        defaultModelsExpandDepth: 0,
        defaultModelExpandDepth: 2,
        displayRequestDuration: true,
        filter: true,
        tryItOutEnabled: true,
        onComplete: function() {
          // Add authentication notice
          const infoContainer = document.querySelector('.swagger-ui .information-container');
          if (infoContainer) {
            const notice = document.createElement('div');
            notice.className = 'auth-notice';
            notice.innerHTML = '<strong>Authentication Required</strong>: Most endpoints require authentication. ' +
              'Use the <code>/auth/login</code> endpoint to get a token, then click the Authorize button and enter ' +
              '<code>Bearer YOUR_TOKEN</code> (with the word "Bearer" and a space before your token).';
            infoContainer.appendChild(notice);
          }
        }
      });

      window.ui = ui;
    };
  </script>
</body>
</html>

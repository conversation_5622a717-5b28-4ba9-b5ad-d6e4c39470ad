package com.cms.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "content_entries")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class ContentEntry extends Auditable {

    @Id
     @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "cms_content_entry_seq")
    @SequenceGenerator(name = "cms_content_entry_seq", sequenceName = "cms_content_entry_seq", initialValue = 100, allocationSize = 1)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "collection_id")
    private CollectionListing collection;

    @Column(name = "data_json", columnDefinition = "jsonb")
    private String dataJson;
}

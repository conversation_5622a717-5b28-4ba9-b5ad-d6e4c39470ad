package com.cms.dto;

import com.cms.entity.CollectionComponent;
import com.cms.entity.CollectionListing;
import com.cms.entity.ComponentListing;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class CollectionComponentDTO {
    private Integer id;
    private CollectionListing collection;
    private ComponentListing component;
    private Boolean isRepeatable;
    private Integer minRepeatOccurrences;
    private Integer maxRepeatOccurrences;
    private Integer displayPreference;
    private Boolean isActive;
    
    // Additional fields not in the entity
    private String displayName;
    private String description;
    private Integer sortOrder;
    private Boolean isRequired;
    private Boolean isVisible;
    private Map<String, String> validationRules;

    /**
     * Convert this DTO to a CollectionComponent entity
     * @return CollectionComponent entity
     */
    public CollectionComponent toEntity() {
        CollectionComponent entity = new CollectionComponent();
        entity.setId(this.id);
        entity.setCollection(this.collection);
        entity.setComponent(this.component);
        entity.setIsRepeatable(this.isRepeatable);
        entity.setMinRepeatOccurrences(this.minRepeatOccurrences);
        entity.setMaxRepeatOccurrences(this.maxRepeatOccurrences);
        entity.setDisplayPreference(this.displayPreference);
        entity.setIsActive(this.isActive);
        
        // Store additional fields as JSON in additionalInformation
        try {
            Map<String, Object> additionalFields = new HashMap<>();
            if (this.displayName != null) additionalFields.put("displayName", this.displayName);
            if (this.description != null) additionalFields.put("description", this.description);
            if (this.sortOrder != null) additionalFields.put("sortOrder", this.sortOrder);
            if (this.isRequired != null) additionalFields.put("isRequired", this.isRequired);
            if (this.isVisible != null) additionalFields.put("isVisible", this.isVisible);
            if (this.validationRules != null) additionalFields.put("validationRules", this.validationRules);
            
            if (!additionalFields.isEmpty()) {
                ObjectMapper mapper = new ObjectMapper();
                entity.setAdditionalInformation(mapper.writeValueAsString(additionalFields));
            }
        } catch (Exception e) {
            log.error("Error converting additional fields to JSON", e);
        }
        
        return entity;
    }

    /**
     * Create a DTO from an entity
     * @param entity the entity to convert
     * @return the DTO
     */
    public static CollectionComponentDTO fromEntity(CollectionComponent entity) {
        if (entity == null) {
            return null;
        }
        
        CollectionComponentDTO dto = new CollectionComponentDTO();
        dto.setId(entity.getId());
        dto.setCollection(entity.getCollection());
        dto.setComponent(entity.getComponent());
        dto.setIsRepeatable(entity.getIsRepeatable());
        dto.setMinRepeatOccurrences(entity.getMinRepeatOccurrences());
        dto.setMaxRepeatOccurrences(entity.getMaxRepeatOccurrences());
        dto.setDisplayPreference(entity.getDisplayPreference());
        dto.setIsActive(entity.getIsActive());
        
        // Extract additional fields from additionalInformation JSON
        if (entity.getAdditionalInformation() != null && !entity.getAdditionalInformation().isEmpty()) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                Map<String, Object> additionalFields = mapper.readValue(entity.getAdditionalInformation(), Map.class);
                
                if (additionalFields.containsKey("displayName")) 
                    dto.setDisplayName((String) additionalFields.get("displayName"));
                
                if (additionalFields.containsKey("description")) 
                    dto.setDescription((String) additionalFields.get("description"));
                
                if (additionalFields.containsKey("sortOrder")) {
                    Object sortOrder = additionalFields.get("sortOrder");
                    if (sortOrder instanceof Integer) {
                        dto.setSortOrder((Integer) sortOrder);
                    } else if (sortOrder instanceof Number) {
                        dto.setSortOrder(((Number) sortOrder).intValue());
                    }
                }
                
                if (additionalFields.containsKey("isRequired")) 
                    dto.setIsRequired((Boolean) additionalFields.get("isRequired"));
                
                if (additionalFields.containsKey("isVisible")) 
                    dto.setIsVisible((Boolean) additionalFields.get("isVisible"));
                
                if (additionalFields.containsKey("validationRules")) 
                    dto.setValidationRules((Map<String, String>) additionalFields.get("validationRules"));
                
            } catch (Exception e) {
                log.error("Error parsing additionalInformation JSON", e);
            }
        }
        
        return dto;
    }
}

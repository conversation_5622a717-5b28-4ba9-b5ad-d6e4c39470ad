package com.cms.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.FileCopyUtils;

import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;

/**
 * Utility class to clean up database tables
 */
@Component
public class DatabaseCleanupUtil {

    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public DatabaseCleanupUtil(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * Execute the cleanup script to remove all data from tables
     */
    public void cleanupDatabase() {
        try {
            // Load the SQL script from resources
            ClassPathResource resource = new ClassPathResource("db/cleanup_data.sql");
            Reader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8);
            String sql = FileCopyUtils.copyToString(reader);
            
            // Execute the script
            jdbcTemplate.execute(sql);
            
            System.out.println("Database cleanup completed successfully");
        } catch (IOException e) {
            System.err.println("Error reading cleanup script: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("Error executing cleanup script: " + e.getMessage());
            e.printStackTrace();
        }
    }
}

package com.cms.payload;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * Request payload for listing users in a specific tenant
 */
@Data
@Schema(description = "Request payload for listing users in a specific tenant")
public class ListUsersInTenantRequest {

    @NotBlank(message = "Tenant schema name is required")
    @Size(min = 1, max = 50, message = "Tenant schema name must be between 1 and 50 characters")
    @Schema(description = "Schema name of the tenant", example = "acme_com", required = true)
    private String tenantSchemaName;
}

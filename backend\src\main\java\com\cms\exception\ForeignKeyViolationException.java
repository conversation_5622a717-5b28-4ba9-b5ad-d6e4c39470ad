package com.cms.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class ForeignKeyViolationException extends RuntimeException {

    private final String entityName;
    private final String fieldName;
    private final Object fieldValue;

    public ForeignKeyViolationException(String entityName, String fieldName, Object fieldValue) {
        super(String.format("%s with %s: '%s' does not exist in the current tenant schema. This entity may exist in another tenant.",
                entityName, fieldName, fieldValue));
        this.entityName = entityName;
        this.fieldName = fieldName;
        this.fieldValue = fieldValue;
    }

    public ForeignKeyViolationException(String entityName, String fieldName, Object fieldValue, String customMessage) {
        super(customMessage);
        this.entityName = entityName;
        this.fieldName = fieldName;
        this.fieldValue = fieldValue;
    }

    public String getEntityName() {
        return entityName;
    }

    public String getFieldName() {
        return fieldName;
    }

    public Object getFieldValue() {
        return fieldValue;
    }
}

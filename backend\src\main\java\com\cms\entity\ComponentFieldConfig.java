package com.cms.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "component_field_config")
@Getter
@Setter
@ToString(exclude = {"componentField"})
@EqualsAndHashCode(callSuper = true, exclude = {"componentField"})
@NoArgsConstructor
@AllArgsConstructor
public class ComponentFieldConfig extends Auditable {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "cms_component_field_config_seq")
    @SequenceGenerator(name = "cms_component_field_config_seq", sequenceName = "cms_component_field_config_seq", initialValue = 100, allocationSize = 1)
    private Integer id;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "component_field_id")
    @JsonBackReference(value = "field-configs")
    private ComponentField componentField;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "field_config_id")
    private FieldConfig fieldConfig;

    @Column(name = "field_config_value")
    private String fieldConfigValue;
}

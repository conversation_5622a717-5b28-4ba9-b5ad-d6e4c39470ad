package com.cms.service;

import com.cms.dto.MediaFolderDTO;

import java.util.List;

public interface MediaFolderService {
    
    /**
     * Create a new media folder
     * 
     * @param folderName The name of the folder
     * @param description The description of the folder (optional)
     * @param parentId The parent folder ID (optional)
     * @return The created folder
     */
    MediaFolderDTO createFolder(String folderName, String description, Integer parentId);
    
    /**
     * Get a folder by ID
     * 
     * @param id The ID of the folder
     * @return The folder
     */
    MediaFolderDTO getFolderById(Integer id);
    
    /**
     * Get all root folders
     * 
     * @return A list of root folders
     */
    List<MediaFolderDTO> getRootFolders();
    
    /**
     * Get subfolders by parent ID
     * 
     * @param parentId The parent folder ID
     * @return A list of subfolders
     */
    List<MediaFolderDTO> getSubfolders(Integer parentId);
    
    /**
     * Update a folder
     * 
     * @param id The ID of the folder
     * @param folderName The new folder name (optional)
     * @param description The new description (optional)
     * @param parentId The new parent folder ID (optional)
     * @return The updated folder
     */
    MediaFolderDTO updateFolder(Integer id, String folderName, String description, Integer parentId);
    
    /**
     * Delete a folder
     * 
     * @param id The ID of the folder
     */
    void deleteFolder(Integer id);
    
    /**
     * Search folders by name
     * 
     * @param query The search query
     * @return A list of folders
     */
    List<MediaFolderDTO> searchFolders(String query);
}

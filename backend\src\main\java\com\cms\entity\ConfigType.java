package com.cms.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "config_types")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class ConfigType extends Auditable {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "cms_config_type_seq")
    @SequenceGenerator(name = "cms_config_type_seq", sequenceName = "cms_config_type_seq", initialValue = 100, allocationSize = 1)
    private Integer id;

    @NotBlank(message = "Config type name is required")
    @Column(name = "config_type_name", nullable = false, unique=true)
    private String configTypeName;

    @Column(name = "config_type_desc")
    private String configTypeDesc;

    @Column(name = "display_name")
    private String displayName;

    @Column(name = "additional_info")
    private String additionalInfo;

    @Column(name = "disclaimer_text")
    private String disclaimerText;

    @Column(name = "placeholder_text")
    private String placeholderText;

    @Column(name = "is_active")
    private Boolean isActive = true;
}

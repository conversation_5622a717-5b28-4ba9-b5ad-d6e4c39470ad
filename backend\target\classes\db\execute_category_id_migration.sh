#!/bin/bash
echo "Executing SQL migration script to add category_id column to all tenant schemas..."

# Set your PostgreSQL connection details
export PGHOST=localhost
export PGPORT=5432
export PGDATABASE=CMS
export PGUSER=postgres
export PGPASSWORD=postgres

# Execute the SQL script
psql -h $PGHOST -p $PGPORT -d $PGDATABASE -U $PGUSER -f migration/V2__add_category_id_to_all_tenant_schemas.sql

echo "Migration completed."

package com.cms.dto;

import com.cms.entity.Client;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for creating a new client
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Data transfer object for creating a new client")
public class ClientCreateDTO {

    @Schema(description = "Client name", example = "Acme Corporation", required = true)
    @NotBlank(message = "Client name is required")
    private String name;

    /**
     * Convert this DTO to a Client entity
     * @return Client entity
     */
    public Client toEntity() {
        Client entity = new Client();
        entity.setName(this.name);
        return entity;
    }
}

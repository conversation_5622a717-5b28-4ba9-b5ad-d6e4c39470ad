package com.cms.mapper;

import com.cms.dto.ComponentFieldConfigDTO;
import com.cms.entity.ComponentFieldConfig;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class ComponentFieldConfigMapper {

    public ComponentFieldConfigDTO toDTO(ComponentFieldConfig entity) {
        return ComponentFieldConfigDTO.fromEntity(entity);
    }

    public List<ComponentFieldConfigDTO> toDTOList(List<ComponentFieldConfig> entities) {
        if (entities == null) {
            return null;
        }
        
        return entities.stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }
}

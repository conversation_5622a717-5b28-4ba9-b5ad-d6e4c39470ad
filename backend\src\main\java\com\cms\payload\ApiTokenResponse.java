package com.cms.payload;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiTokenResponse {
    private Long id;
    private String name;
    private String description;
    private String tokenValue;
    private LocalDateTime expiresAt;
    private LocalDateTime lastUsedAt;
    private Boolean isActive;
    private LocalDateTime createdAt;
}

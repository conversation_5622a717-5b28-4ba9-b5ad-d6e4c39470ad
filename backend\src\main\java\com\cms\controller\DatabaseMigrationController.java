package com.cms.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;

/**
 * Controller to execute database migrations on demand.
 */
@RestController
@RequestMapping("/admin/database")
@RequiredArgsConstructor
@Slf4j
public class DatabaseMigrationController {

    private final JdbcTemplate jdbcTemplate;

    /**
     * Execute the migration script to remove unique constraints from all tenant schemas.
     * This endpoint should be secured and only accessible to administrators.
     */
    @PostMapping("/execute-migration")
    public ResponseEntity<String> executeMigration() {
        try {
            log.info("Executing SQL migration script to remove unique constraints...");

            // Load the SQL script from the classpath
            ClassPathResource resource = new ClassPathResource("db/migration/V1__remove_unique_constraints.sql");
            String sql = readResourceAsString(resource);

            // Execute the SQL script
            jdbcTemplate.execute(sql);

            log.info("Migration completed successfully.");
            return ResponseEntity.ok("Migration completed successfully.");
        } catch (Exception e) {
            log.error("Error executing migration script: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body("Error executing migration: " + e.getMessage());
        }
    }

    /**
     * Execute the migration script to add category_id column to all tenant schemas.
     * This endpoint should be secured and only accessible to administrators.
     */
    @PostMapping("/execute-category-id-migration")
    public ResponseEntity<String> executeCategoryIdMigration() {
        try {
            log.info("Executing SQL migration script to add category_id column to all tenant schemas...");

            // Load the SQL script from the classpath
            ClassPathResource resource = new ClassPathResource("db/migration/V2__add_category_id_to_all_tenant_schemas.sql");
            String sql = readResourceAsString(resource);

            // Execute the SQL script
            jdbcTemplate.execute(sql);

            log.info("Migration completed successfully.");
            return ResponseEntity.ok("Migration completed successfully.");
        } catch (Exception e) {
            log.error("Error executing migration script: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body("Error executing migration: " + e.getMessage());
        }
    }

    private String readResourceAsString(ClassPathResource resource) throws IOException {
        try (Reader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8)) {
            return FileCopyUtils.copyToString(reader);
        }
    }
}

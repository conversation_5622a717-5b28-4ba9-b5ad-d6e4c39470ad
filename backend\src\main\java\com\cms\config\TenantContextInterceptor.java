package com.cms.config;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

/**
 * Interceptor to log tenant context information for each request.
 * This helps with debugging tenant context propagation issues.
 */
@Component
@Slf4j
public class TenantContextInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();
        String tenantId = TenantContextHolder.getTenantId();
        String requestMethod = request.getMethod();
        
        log.info("TenantContextInterceptor - Request: {} {} - Current tenant: {}", 
                requestMethod, requestURI, tenantId);
        
        // Add tenant information to response headers for debugging
        response.setHeader("X-Current-Tenant", tenantId);
        
        return true; // Always continue with the request
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        String requestURI = request.getRequestURI();
        String tenantId = TenantContextHolder.getTenantId();
        
        log.debug("TenantContextInterceptor - Post-handle for request: {} - Current tenant: {}", 
                requestURI, tenantId);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        String requestURI = request.getRequestURI();
        String tenantId = TenantContextHolder.getTenantId();
        
        log.debug("TenantContextInterceptor - After completion for request: {} - Final tenant: {}", 
                requestURI, tenantId);
        
        // Don't clear tenant context here - it should be managed by TenantFilter
    }
}

# CORS Configuration
# For development, we'll use a more permissive configuration
# In production, you should restrict origins appropriately
cors.allowed-origins=*
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS,HEAD,PATCH
cors.allowed-headers=*,Origin,X-Requested-With,Content-Type,Accept,Authorization,Access-Control-Request-Method,Access-Control-Request-Headers
# Setting credentials to false allows us to use wildcard origins
cors.allow-credentials=false
cors.max-age=3600

# Additional CORS settings for media
cors.exposed-headers=Content-Disposition,Cross-Origin-Resource-Policy,Cross-Origin-Embedder-Policy,Cross-Origin-Opener-Policy,Cache-Control,Pragma,Expires

# ORB Prevention settings
cors.resource-policy=cross-origin
cors.embedder-policy=unsafe-none
cors.opener-policy=unsafe-none

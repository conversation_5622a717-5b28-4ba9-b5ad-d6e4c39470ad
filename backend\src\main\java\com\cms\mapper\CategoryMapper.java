package com.cms.mapper;

import com.cms.dto.CategoryDTO;
import com.cms.entity.Category;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for converting between Category entity and CategoryDTO
 */
@Component
public class CategoryMapper {

    /**
     * Convert a Category entity to a CategoryDTO
     *
     * @param entity Category entity
     * @return CategoryDTO
     */
    public CategoryDTO toDTO(Category entity) {
        if (entity == null) {
            return null;
        }

        CategoryDTO dto = new CategoryDTO();
        dto.setId(entity.getId());
        dto.setCategoryName(entity.getCategoryName());
        dto.setClient(entity.getClient());
        dto.setCreatedBy(entity.getCreatedBy());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setModifiedBy(entity.getModifiedBy());
        dto.setModifiedAt(entity.getModifiedAt());

        // ✅ Add parentCategory mapping (shallow)
        if (entity.getParentCategory() != null) {
            CategoryDTO parentDTO = new CategoryDTO();
            parentDTO.setId(entity.getParentCategory().getId());
            parentDTO.setCategoryName(entity.getParentCategory().getCategoryName());
            dto.setParentCategory(parentDTO);
        }

        return dto;
    }


    /**
     * Convert a list of Category entities to a list of CategoryDTOs
     *
     * @param entities List of Category entities
     * @return List of CategoryDTOs
     */
    public List<CategoryDTO> toDTOList(List<Category> entities) {
        if (entities == null) {
            return null;
        }

        return entities.stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Convert a CategoryDTO to a Category entity
     *
     * @param dto CategoryDTO
     * @return Category entity
     */
    public Category toEntity(CategoryDTO dto) {
        if (dto == null) {
            return null;
        }

        Category entity = new Category();
        entity.setId(dto.getId());
        entity.setCategoryName(dto.getCategoryName());
        entity.setClient(dto.getClient());
        entity.setCreatedBy(dto.getCreatedBy());
        entity.setCreatedAt(dto.getCreatedAt());
        entity.setModifiedBy(dto.getModifiedBy());
        entity.setModifiedAt(dto.getModifiedAt());

        // ✅ Add parentCategory mapping
        if (dto.getParentCategory() != null && dto.getParentCategory().getId() != null) {
            Category parent = new Category();
            parent.setId(dto.getParentCategory().getId());
            entity.setParentCategory(parent);  // Only set the ID; actual entity will be fetched in the service layer
        }

        return entity;
    }

}

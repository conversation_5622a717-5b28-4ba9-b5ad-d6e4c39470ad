package com.cms.service;

import com.cms.entity.ComponentListing;

import java.util.List;
import java.util.Optional;

public interface ComponentListingService {
    List<ComponentListing> getAllComponents();
    List<ComponentListing> getActiveComponents();
    Optional<ComponentListing> getComponentById(Integer id);
    Optional<ComponentListing> getComponentByApiId(String apiId);
    ComponentListing createComponent(ComponentListing component);
    ComponentListing updateComponent(Integer id, ComponentListing component);
    void deleteComponent(Integer id);
    boolean existsByComponentName(String componentName);
}

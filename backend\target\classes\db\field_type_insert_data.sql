-- =============================================
-- FIELD TYPES DATA INSERTION
-- =============================================

-- Insert field types with ON CONFLICT to make the script idempotent
INSERT INTO field_types (id, display_name, field_type_desc, field_type_name, help_text, is_active, logo_image_path)
VALUES
(1, 'Text', 'Simple text field', 'text', 'Enter text here', TRUE, NULL),
(2, 'Number', 'Numeric field', 'number', 'Enter a number', TRUE, NULL),
(3, 'Date', 'Date field', 'date', 'Select a date', TRUE, NULL),
(4, 'Image', 'Image upload field', 'image', 'Upload an image', TRUE, NULL),
(5, 'Rich Text', 'Rich text editor', 'rich_text', 'Enter formatted text', TRUE, NULL),
(6, 'Masked', 'Masked input field', 'mask', 'Enter masked value (e.g., SSN, EIN)', TRUE, NULL),
(8, 'Editor', 'Rich text editor', 'editor', 'Enter formatted text', TRUE, NULL),
(9, 'Password', 'Password input field', 'password', 'Enter password', TRUE, NULL),
(10, 'Autocomplete', 'Autocomplete suggestions', 'autocomplete', 'Start typing for suggestions', TRUE, NULL),
(11, 'Cascade Select', 'Cascade selection field', 'cascade_select', 'Select dependent options', TRUE, NULL),
(12, 'Dropdown', 'Dropdown selection', 'dropdown', 'Select from dropdown', TRUE, NULL),
(13, 'File', 'File upload field', 'file', 'Upload a file', TRUE, NULL),
(14, 'Multi-State Checkbox', 'Multi-state checkbox', 'multi_state_checkbox', 'Select multiple states', TRUE, NULL),
(15, 'Multi-Select', 'Multi-select field', 'multi_select', 'Select multiple options', TRUE, NULL),
(16, 'Mention', 'Mention users or tags', 'mention', 'Mention users or tags', TRUE, NULL),
(17, 'Textarea', 'Multi-line text input', 'textarea', 'Enter multi-line text', TRUE, NULL),
(18, 'OTP', 'One-time password input', 'otp', 'Enter one-time password', TRUE, NULL),
(19, 'Checkbox', 'Checkbox input field', 'checkbox', 'Select one or more options', TRUE, NULL),
(20, 'RadioButton', 'Radio button selection field', 'radio_button', 'Select one option', TRUE, NULL),
(21, 'InputSwitch', 'Toggle switch input', 'input_switch', 'Toggle the option on or off', TRUE, NULL),
(22, 'Dummy', 'Dummy field', 'dummy', 'Dummy Field', TRUE, NULL),
(23, 'Api details', 'API details configuration', 'api_details', 'Configure API details', TRUE, NULL)
ON CONFLICT (id) DO UPDATE
SET
    display_name = EXCLUDED.display_name,
    field_type_desc = EXCLUDED.field_type_desc,
    field_type_name = EXCLUDED.field_type_name,
    help_text = EXCLUDED.help_text,
    is_active = EXCLUDED.is_active,
    logo_image_path = EXCLUDED.logo_image_path;

-- =============================================
-- CONFIG TYPES DATA INSERTION
-- =============================================

-- Insert config types with ON CONFLICT to make the script idempotent
INSERT INTO config_types (id, config_type_name, config_type_desc, display_name, disclaimer_text, placeholder_text, is_active)
VALUES
(1, 'properties', 'Basic properties for fields', 'Properties', 'Basic properties for fields', 'Properties', TRUE),
(2, 'attributes', 'UI attributes for fields', 'Attributes', 'Attributes for fields', 'Attributes', TRUE),
(3, 'validations', 'Validation rules for fields', 'Validations', 'Validation rules for fields', 'Validations', TRUE)
ON CONFLICT (id) DO UPDATE
SET
    config_type_name = EXCLUDED.config_type_name,
    config_type_desc = EXCLUDED.config_type_desc,
    display_name = EXCLUDED.display_name,
    disclaimer_text = EXCLUDED.disclaimer_text,
    placeholder_text = EXCLUDED.placeholder_text,
    is_active = EXCLUDED.is_active;

import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Layers, Trash2, Edit, ArrowUp, Search } from 'lucide-react';
import { useComponentStore } from '@/lib/store';
import { componentsApi, componentFieldsApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pagination, PaginationContent, PaginationEllipsis, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Input } from '@/components/ui/input';

export default function Components() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const {
    components,
    setComponents,
    setSelectedComponent,
    removeComponent,
    addComponent,
    setLoading,
    loading
  } = useComponentStore();

  const [deleteConfirmOpen, setDeleteConfirmOpen] = React.useState(false);
  const [componentToDelete, setComponentToDelete] = React.useState<string | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = React.useState(1);
  const [itemsPerPage, setItemsPerPage] = React.useState(10);
  const [totalPages, setTotalPages] = React.useState(1);

  // Scroll to top button state
  const [showScrollButton, setShowScrollButton] = React.useState(false);

  // Search state
  const [searchQuery, setSearchQuery] = React.useState('');

  // Function to check database connection
  const checkDatabaseConnection = async () => {
    try {
      await componentsApi.getAll();
      console.log('Database connection successful');
      return true;
    } catch (error: any) {
      console.error('Database connection error:', error);

      let errorMessage = 'Failed to connect to the database';
      if (error.response) {
        errorMessage = `Server error: ${error.response.status}`;
      } else if (error.request) {
        errorMessage = 'No response from server. Please check if the backend is running.';
      } else {
        errorMessage = `Error: ${error.message}`;
      }

      toast({
        title: 'Database Connection Error',
        description: errorMessage,
        variant: 'destructive',
      });

      return false;
    }
  };

  React.useEffect(() => {
    const fetchComponents = async () => {
      setLoading(true);
      try {
        // First check database connection
        const isConnected = await checkDatabaseConnection();
        if (!isConnected) {
          return;
        }

        const response = await componentsApi.getAll();
        console.log('Components fetched from API:', response.data);

        // Transform the backend data format to the frontend format
        const formattedComponents = [];

        for (const component of response.data) {
          // Create the formatted component object
          const formattedComponent = {
            id: component.id.toString(),
            name: component.componentName || 'Unnamed Component',
            apiId: component.componentApiId || '',
            description: component.componentDesc || '',
            fields: [],
            isActive: true,
            createdAt: '',
            updatedAt: ''
          };

          // Fetch fields for this component
          try {
            console.log(`Fetching fields for component ID: ${component.id}`);
            const fieldsResponse = await componentFieldsApi.getByComponentId(component.id.toString());
            console.log(`Fields for component ${component.id}:`, fieldsResponse.data);

            if (fieldsResponse.data && Array.isArray(fieldsResponse.data)) {
              formattedComponent.fields = fieldsResponse.data.map((field: any) => ({
                id: field.id.toString(),
                name: field.additionalInformation ? JSON.parse(field.additionalInformation).name || '' : '',
                apiId: field.additionalInformation ? JSON.parse(field.additionalInformation).apiId || '' : '',
                type: field.fieldType ? field.fieldType.id : 0,
                required: field.additionalInformation ? JSON.parse(field.additionalInformation).required || false : false,
                unique: field.additionalInformation ? JSON.parse(field.additionalInformation).unique || false : false
              }));
            }
          } catch (fieldsError) {
            console.error(`Error fetching fields for component ${component.id}:`, fieldsError);
          }

          formattedComponents.push(formattedComponent);
        }

        console.log('Formatted components with fields:', formattedComponents);
        setComponents(formattedComponents);
      } catch (error) {
        console.error('Error fetching components:', error);
        toast({
          title: 'Error',
          description: 'Failed to load components',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchComponents();
  }, [setComponents, setLoading, toast]);

  // Update total pages when components, search query, or itemsPerPage changes
  React.useEffect(() => {
    const filteredComponents = getFilteredComponents();
    if (filteredComponents.length > 0) {
      const newTotalPages = Math.max(1, Math.ceil(filteredComponents.length / itemsPerPage));
      setTotalPages(newTotalPages);

      // Reset to page 1 if current page is now invalid
      if (currentPage > newTotalPages) {
        setCurrentPage(1);
      }
    } else {
      setTotalPages(1);
    }
  }, [components.length, itemsPerPage, currentPage, searchQuery]);

  // Handle scroll event to show/hide scroll-to-top button
  React.useEffect(() => {
    const handleScroll = () => {
      // Show button when user scrolls down 300px
      if (window.scrollY > 300) {
        setShowScrollButton(true);
      } else {
        setShowScrollButton(false);
      }
    };

    window.addEventListener('scroll', handleScroll);

    // Clean up the event listener
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Filter components based on search query
  const getFilteredComponents = () => {
    if (!searchQuery.trim()) {
      return components;
    }

    const query = searchQuery.toLowerCase().trim();
    return components.filter(component =>
      component.name.toLowerCase().includes(query) ||
      component.apiId.toLowerCase().includes(query)
    );
  };

  // Get paginated components
  const getPaginatedComponents = () => {
    const filteredComponents = getFilteredComponents();
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredComponents.slice(startIndex, endIndex);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const handleCreateComponent = () => {
    navigate('/components/new');
  };

  const handleEditComponent = (id: string) => {
    navigate(`/components/edit/${id}`);
  };

  const confirmDeleteComponent = (id: string) => {
    setComponentToDelete(id);
    setDeleteConfirmOpen(true);
  };

  const handleDeleteComponent = async () => {
    if (!componentToDelete) return;

    try {
      await componentsApi.delete(componentToDelete);
      removeComponent(componentToDelete);
      toast({
        title: 'Success',
        description: 'Component deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting component:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete component',
        variant: 'destructive',
      });
    } finally {
      setDeleteConfirmOpen(false);
      setComponentToDelete(null);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row md:items-center md:justify-between">
        <h1 className="text-3xl font-bold">Components</h1>
        <div className="flex items-center gap-2">
          <div className="relative w-full md:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search components..."
              className="pl-8"
              value={searchQuery}
              onChange={handleSearchChange}
            />
          </div>
          <Button onClick={handleCreateComponent}>
            <Plus className="mr-2 h-4 w-4" />
            Create new component
          </Button>
        </div>
      </div>

      {loading ? (
        // Loading state
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="animate-pulse p-4 border rounded-md">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gray-200 rounded-full mr-3"></div>
                <div className="flex-1">
                  <div className="h-5 w-1/4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-4 w-1/3 bg-gray-200 rounded"></div>
                </div>
                <div className="h-8 w-16 bg-gray-200 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      ) : components.length === 0 ? (
        // Empty state
        <div className="flex flex-col items-center justify-center p-8 bg-muted/30 border border-dashed border-border rounded-lg">
          <Layers className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium">No components found</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Create your first component to start building reusable content blocks
          </p>
          <Button onClick={handleCreateComponent}>
            <Plus className="mr-2 h-4 w-4" />
            Create new component
          </Button>
        </div>
      ) : (
        // List of components
        <>
          <div className="flex justify-between items-center mb-4">
            <div className="text-sm text-muted-foreground">
              {searchQuery.trim() && (
                <span className="font-medium mr-2">Search results for "{searchQuery}":</span>
              )}
              <span className="pagination-info">Showing items</span>
            </div>
            <div className="items-per-page-selector">
              <span className="text-sm">Items per page:</span>
              <Select value={itemsPerPage.toString()} onValueChange={handleItemsPerPageChange}>
                <SelectTrigger className="w-[80px]">
                  <SelectValue placeholder="5" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="15">15</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          {getFilteredComponents().length === 0 ? (
            <div className="flex flex-col items-center justify-center p-8 bg-muted/30 border border-dashed border-border rounded-lg">
              <Layers className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium">No components found</h3>
              <p className="text-sm text-muted-foreground mb-4">
                No components match your search query "{searchQuery}"
              </p>
              <Button variant="outline" onClick={() => setSearchQuery('')}>
                Clear search
              </Button>
            </div>
          ) : (
            <div className="border border-border rounded-md overflow-hidden">
              {getPaginatedComponents().map((component, index) => (
              <div
                key={component.id}
                className={`flex items-center justify-between p-4 hover:bg-muted/50 ${index !== getPaginatedComponents().length - 1 ? 'border-b border-border' : ''} cursor-pointer transition-colors duration-200`}
                onClick={() => handleEditComponent(component.id)}
              >
                <div className="flex items-center flex-1">
                  <div className="w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center mr-3 flex-shrink-0 border border-border">
                    {(component.name || 'U')[0].toUpperCase()}
                  </div>
                  <div>
                    <h3 className="font-medium">{component.name || 'Unnamed Component'}</h3>
                    <p className="text-sm text-muted-foreground">API ID: {component.apiId}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="flex items-center mr-4">
                    <div className="bg-primary/10 text-primary text-xs font-medium px-2 py-1 rounded-full mr-2">
                      {component.fields?.length || 0} fields
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditComponent(component.id);
                    }}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-destructive hover:text-destructive hover:bg-destructive/10"
                    onClick={(e) => {
                      e.stopPropagation();
                      confirmDeleteComponent(component.id);
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
            </div>
          )}

          {/* Pagination controls - always show */}
          <div className="mt-4 flex flex-col sm:flex-row justify-between items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Items per page:</span>
              <Select
                value={itemsPerPage.toString()}
                onValueChange={handleItemsPerPageChange}
              >
                <SelectTrigger className="w-[80px]">
                  <SelectValue placeholder="10" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="15">15</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="30">30</SelectItem>
                </SelectContent>
              </Select>
              <span className="text-sm text-muted-foreground">
                Showing {Math.min((currentPage - 1) * itemsPerPage + 1, getFilteredComponents().length)} - {Math.min(currentPage * itemsPerPage, getFilteredComponents().length)} of {getFilteredComponents().length}
              </span>
            </div>

            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                    className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                  />
                </PaginationItem>

                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <PaginationItem key={page}>
                    <PaginationLink
                      isActive={page === currentPage}
                      onClick={() => handlePageChange(page)}
                      className="cursor-pointer"
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                ))}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                    className={currentPage === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </>
      )}



      {/* Delete confirmation dialog */}
      <Dialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete component</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this component? This action cannot be undone and all related content will be permanently removed.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteConfirmOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteComponent}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Scroll to top button */}
      {showScrollButton && (
        <Button
          className="fixed bottom-6 right-6 rounded-full w-12 h-12 shadow-lg flex items-center justify-center bg-primary hover:bg-primary/90 transition-all"
          onClick={scrollToTop}
          aria-label="Scroll to top"
        >
          <ArrowUp className="h-5 w-5 text-white" />
        </Button>
      )}
    </div>
  );
}

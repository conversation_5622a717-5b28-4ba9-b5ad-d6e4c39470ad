package com.cms.util;

import com.cms.entity.Tenant;
import com.cms.service.TenantService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * This class is used to test tenant schema creation directly.
 * It will run when the application starts with the "schema-test" profile.
 */
@Component
@Profile("schema-test")
public class TenantSchemaCreationTest implements CommandLineRunner {

    @Autowired
    private TenantService tenantService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void run(String... args) throws Exception {
        System.out.println("=== TESTING TENANT SCHEMA CREATION ===");
        
        // Create a unique schema name for testing
        String schemaName = "test_tenant_" + System.currentTimeMillis();
        System.out.println("Creating test tenant with schema: " + schemaName);
        
        try {
            // Create a new tenant
            Tenant tenant = new Tenant();
            tenant.setName("Test Tenant");
            tenant.setSchemaName(schemaName);
            tenant.setDescription("Test tenant for schema creation testing");
            tenant.setIsActive(true);
            
            // Save the tenant
            Tenant savedTenant = tenantService.createTenant(tenant);
            System.out.println("Tenant created with ID: " + savedTenant.getId());
            
            // Verify the schema was created
            List<Map<String, Object>> schemas = jdbcTemplate.queryForList(
                    "SELECT schema_name FROM information_schema.schemata WHERE schema_name = ?", 
                    schemaName);
            
            if (!schemas.isEmpty()) {
                System.out.println("Schema created successfully: " + schemaName);
                
                // Verify tables were created in the schema
                List<Map<String, Object>> tables = jdbcTemplate.queryForList(
                        "SELECT table_name FROM information_schema.tables WHERE table_schema = ?",
                        schemaName);
                
                System.out.println("Tables created in schema: " + tables.size());
                for (Map<String, Object> table : tables) {
                    System.out.println("  - " + table.get("table_name"));
                }
            } else {
                System.err.println("ERROR: Schema was not created: " + schemaName);
            }
            
            // Clean up - delete the tenant and schema
            tenantService.deleteTenant(savedTenant.getId());
            System.out.println("Tenant and schema deleted");
            
            // Verify the schema was deleted
            schemas = jdbcTemplate.queryForList(
                    "SELECT schema_name FROM information_schema.schemata WHERE schema_name = ?", 
                    schemaName);
            
            if (schemas.isEmpty()) {
                System.out.println("Schema deleted successfully");
            } else {
                System.err.println("ERROR: Schema was not deleted: " + schemaName);
            }
            
        } catch (Exception e) {
            System.err.println("ERROR during tenant schema creation test: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("=== TENANT SCHEMA CREATION TEST COMPLETED ===");
    }
}

package com.cms.service;

import com.cms.entity.*;
import com.cms.repository.CollectionFieldRepository;
import com.cms.repository.CollectionListingRepository;
import com.cms.repository.FieldTypeRepository;
import com.cms.service.impl.CollectionFieldServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class CollectionFieldServiceTest {

    @Mock
    private CollectionFieldRepository collectionFieldRepository;

    @Mock
    private CollectionListingRepository collectionListingRepository;

    @Mock
    private FieldTypeRepository fieldTypeRepository;

    @Mock
    private CollectionFieldConfigService collectionFieldConfigService;

    @Mock
    private CollectionOrderingService collectionOrderingService;

    @InjectMocks
    private CollectionFieldServiceImpl collectionFieldService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testCreateCollectionFieldWithConfigs() {
        // Prepare test data
        CollectionListing collection = new CollectionListing();
        collection.setId(1);

        // Create a field type
        FieldType fieldType = new FieldType();
        fieldType.setId(1);
        fieldType.setFieldTypeName("text");
        fieldType.setDisplayName("Text");
        fieldType.setIsActive(true);

        CollectionField field = new CollectionField();
        field.setId(1);
        field.setCollection(collection);
        field.setFieldType(fieldType);
        field.setAdditionalInformation("Test Field");

        // Create configs
        List<CollectionFieldConfig> configs = new ArrayList<>();
        CollectionFieldConfig config1 = new CollectionFieldConfig();
        config1.setId(1);

        FieldConfig fieldConfig1 = new FieldConfig();
        fieldConfig1.setId(1);
        fieldConfig1.setConfigName("required");

        config1.setFieldConfig(fieldConfig1);
        config1.setFieldConfigValue("true");

        configs.add(config1);
        field.setConfigs(configs);

        // Mock repository responses
        when(collectionListingRepository.findById(1)).thenReturn(Optional.of(collection));
        when(fieldTypeRepository.findById(1)).thenReturn(Optional.of(fieldType));
        when(collectionFieldRepository.save(any(CollectionField.class))).thenReturn(field);
        when(collectionFieldConfigService.createCollectionFieldConfigs(anyList())).thenReturn(configs);

        // Mock the CollectionOrderingService
        when(collectionOrderingService.getNextDisplayPreference(1)).thenReturn(10);

        // Call the service method
        CollectionField savedField = collectionFieldService.createCollectionField(field);

        // Verify the results
        assertNotNull(savedField);
        assertEquals(1, savedField.getId());
        assertEquals("Test Field", savedField.getAdditionalInformation());
        assertEquals(1, savedField.getConfigs().size());
        assertEquals("required", savedField.getConfigs().get(0).getFieldConfig().getConfigName());
        assertEquals("true", savedField.getConfigs().get(0).getFieldConfigValue());

        // Verify that the repository methods were called
        verify(collectionListingRepository, times(1)).findById(1);
        verify(fieldTypeRepository, times(1)).findById(1);
        verify(collectionFieldRepository, times(1)).save(any(CollectionField.class));
        verify(collectionFieldConfigService, times(1)).createCollectionFieldConfigs(anyList());
    }

    @Test
    public void testUpdateCollectionFieldWithConfigs() {
        // Prepare test data
        CollectionListing collection = new CollectionListing();
        collection.setId(1);

        // Create a field type
        FieldType fieldType = new FieldType();
        fieldType.setId(1);
        fieldType.setFieldTypeName("text");
        fieldType.setDisplayName("Text");
        fieldType.setIsActive(true);

        CollectionField existingField = new CollectionField();
        existingField.setId(1);
        existingField.setCollection(collection);
        existingField.setFieldType(fieldType);
        existingField.setAdditionalInformation("Existing Field");

        CollectionField updatedField = new CollectionField();
        updatedField.setId(1);
        updatedField.setCollection(collection);
        updatedField.setFieldType(fieldType);
        updatedField.setAdditionalInformation("Updated Field");

        // Create configs
        List<CollectionFieldConfig> existingConfigs = new ArrayList<>();
        CollectionFieldConfig existingConfig = new CollectionFieldConfig();
        existingConfig.setId(1);
        existingConfigs.add(existingConfig);

        List<CollectionFieldConfig> newConfigs = new ArrayList<>();
        CollectionFieldConfig newConfig = new CollectionFieldConfig();
        newConfig.setId(2);

        FieldConfig fieldConfig = new FieldConfig();
        fieldConfig.setId(2);
        fieldConfig.setConfigName("unique");

        newConfig.setFieldConfig(fieldConfig);
        newConfig.setFieldConfigValue("true");

        newConfigs.add(newConfig);
        updatedField.setConfigs(newConfigs);

        // Mock repository responses
        when(collectionFieldRepository.findById(1)).thenReturn(Optional.of(existingField));
        when(collectionListingRepository.findById(1)).thenReturn(Optional.of(collection));
        when(fieldTypeRepository.findById(1)).thenReturn(Optional.of(fieldType));
        when(collectionFieldRepository.save(any(CollectionField.class))).thenReturn(updatedField);
        when(collectionFieldConfigService.getCollectionFieldConfigsByCollectionFieldId(1)).thenReturn(existingConfigs);
        when(collectionFieldConfigService.createCollectionFieldConfigs(anyList())).thenReturn(newConfigs);

        // Mock the CollectionOrderingService
        when(collectionOrderingService.getNextDisplayPreference(1)).thenReturn(20);

        // Call the service method
        CollectionField result = collectionFieldService.updateCollectionField(1, updatedField);

        // Verify the results
        assertNotNull(result);
        assertEquals(1, result.getId());
        assertEquals("Updated Field", result.getAdditionalInformation());
        assertEquals(1, result.getConfigs().size());
        assertEquals("unique", result.getConfigs().get(0).getFieldConfig().getConfigName());
        assertEquals("true", result.getConfigs().get(0).getFieldConfigValue());

        // Verify that the repository methods were called
        verify(collectionFieldRepository, times(1)).findById(1);
        verify(collectionListingRepository, times(1)).findById(1);
        verify(fieldTypeRepository, times(1)).findById(1);
        verify(collectionFieldRepository, times(1)).save(any(CollectionField.class));
        verify(collectionFieldConfigService, times(1)).getCollectionFieldConfigsByCollectionFieldId(1);
        verify(collectionFieldConfigService, times(1)).deleteCollectionFieldConfig(1);
        verify(collectionFieldConfigService, times(1)).createCollectionFieldConfigs(anyList());
    }
}

package com.cms.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MediaFolderDTO {
    private Integer id;
    private String folderName;
    private String description;
    private Integer parentId;
    private String parentName;
    private List<MediaFolderDTO> subfolders;
    private Long mediaCount;
    private String createdByUsername;
    private LocalDateTime createdAt;
    private LocalDateTime modifiedAt;
}

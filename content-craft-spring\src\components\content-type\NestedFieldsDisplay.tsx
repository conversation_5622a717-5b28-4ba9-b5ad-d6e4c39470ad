import React, { useState } from 'react';
import { Edit, Trash2, Plus, Layers, MoveVertical, ChevronDown, ChevronRight, ListOrdered } from 'lucide-react';
import ComponentFieldPreferences from './ComponentFieldPreferences';
import { Field, FieldTypeEnum } from '@/lib/store';
import { Button } from '@/components/ui/button';
import { componentsApi, componentComponentsApi, componentFieldsApi, getBaseUrl } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

interface NestedFieldsDisplayProps {
  fields: Field[];
  onEditField: (field: Field) => void;
  onDeleteField: (fieldId: string) => void;
  onAddField: () => void;
  onAddFieldToComponent: (componentId: string) => void;
  onReorderFields?: (componentIds: number[], fieldIds: number[]) => void;
}

interface ComponentFieldsCache {
  [componentId: string]: {
    fields: Field[];
    loading: boolean;
    error: string | null;
    childComponents?: ChildComponent[];
    childComponentsLoading?: boolean;
    childComponentsError?: string | null;
    expanded?: boolean;
    // Track expanded state for each child component
    expandedChildComponents?: {[childComponentId: string]: boolean};
    // Cache child component fields
    childComponentFields?: {[childComponentId: string]: any[]};
    childComponentFieldsLoading?: {[childComponentId: string]: boolean};
  };
}

interface ChildComponent {
  id: string;
  childComponent: {
    id: string;
    componentName: string;
    componentDisplayName: string;
    componentApiId: string;
  };
  isRepeatable: boolean;
  displayPreference: number;
  minRepeatOccurrences?: number;
  maxRepeatOccurrences?: number;
}

export default function NestedFieldsDisplay({
  fields,
  onEditField,
  onDeleteField,
  onAddField,
  onAddFieldToComponent,
  onReorderFields
}: NestedFieldsDisplayProps) {
  // Initialize toast
  const { toast } = useToast();
  // State to cache component fields
  const [componentFieldsCache, setComponentFieldsCache] = React.useState<ComponentFieldsCache>({});

  // State for component field preferences dialog
  const [fieldPreferencesOpen, setFieldPreferencesOpen] = useState(false);
  const [selectedComponent, setSelectedComponent] = useState<{ id: string; name: string } | null>(null);

  // State for drag and drop
  const [draggedField, setDraggedField] = React.useState<Field | null>(null);
  const [dragOverFieldId, setDragOverFieldId] = React.useState<string | null>(null);
  const [reorderMode, setReorderMode] = React.useState<boolean>(false);

  // Function to fetch component fields
  const fetchComponentFields = React.useCallback(async (componentId: string) => {
    // If we're already loading this component's fields, don't fetch again
    if (componentFieldsCache[componentId]?.loading) {
      console.log(`Already loading fields for component ID: ${componentId}, skipping fetch`);
      return;
    }

    // Initialize or update the cache entry for this component
    setComponentFieldsCache(prev => ({
      ...prev,
      [componentId]: {
        ...prev[componentId],
        loading: true,
        error: null
      }
    }));

    try {
      // Enhanced debugging
      console.log('=== FETCHING COMPONENT FIELDS ===');
      console.log('Component ID to fetch:', componentId);
      console.log('Current fields array:', fields);

      const componentField = fields.find(f => f.id === componentId);
      console.log('Found component field in fields array:', componentField);

      // Get the component ID from either the direct property or from attributes
      // The componentId can be stored directly on the field or in the attributes object
      const actualComponentId = componentField?.componentId || componentField?.attributes?.componentId;
      console.log('Actual component ID extracted:', actualComponentId);
      console.log('Component ID type:', typeof actualComponentId);

      if (!actualComponentId) {
        console.error('Component ID not found in field. Field details:', componentField);
        throw new Error('Component ID not found in field');
      }

      // Fetch the component fields from the API
      console.log(`Making API request to get component details for ID: ${actualComponentId}`);
      console.log('API URL:', `${getBaseUrl()}/components/getByIdWithDetails/${actualComponentId}`);

      try {
        const response = await componentsApi.getByIdWithDetails(actualComponentId.toString());
        console.log('API response status:', response.status);
        console.log('Component details response data:', response.data);

        if (!response.data) {
          console.error('API returned empty response data');
          throw new Error('No data returned from API');
        }

        // Extract fields from the component details
        const componentFields = response.data.fields || [];
        console.log('Number of component fields found:', componentFields.length);
        console.log('Raw component fields data:', componentFields);

        if (componentFields.length === 0) {
          console.warn('No fields found for component ID:', actualComponentId);
        }

        // Process the fields
        const processedFields = componentFields.map((fieldData: any, index: number) => {
          try {
            console.log(`Processing field ${index} data:`, fieldData);
            console.log(`Field ${index} ID:`, fieldData.id);
            console.log(`Field ${index} display preference:`, fieldData.displayPreference);
            console.log(`Field ${index} field type:`, fieldData.fieldType);
            console.log(`Field ${index} additional information:`, fieldData.additionalInformation);

            // Parse the additional information
            let fieldMetadata: any = {};
            if (fieldData.additionalInformation) {
              try {
                fieldMetadata = JSON.parse(fieldData.additionalInformation);
                console.log(`Parsed field ${index} metadata:`, fieldMetadata);
              } catch (error) {
                console.error(`Error parsing additionalInformation JSON for field ${index}:`, error);
                console.log('Raw additionalInformation value:', fieldData.additionalInformation);

                // Try to create a default metadata object if parsing fails
                fieldMetadata = {
                  name: fieldData.fieldType?.displayName || fieldData.fieldType?.fieldTypeName || `Field ${fieldData.id}`,
                  apiId: `field_${fieldData.id}`,
                  type: fieldData.fieldType?.fieldTypeName?.toUpperCase() || 'TEXT',
                  required: false,
                  unique: false,
                  description: '',
                  attributes: {},
                  validations: {}
                };
                console.log(`Created default metadata for field ${index}:`, fieldMetadata);
              }
            } else {
              console.warn(`Field ${index} has no additionalInformation`);

              // Create default metadata for fields without additionalInformation
              fieldMetadata = {
                name: fieldData.fieldType?.displayName || fieldData.fieldType?.fieldTypeName || `Field ${fieldData.id}`,
                apiId: `field_${fieldData.id}`,
                type: fieldData.fieldType?.fieldTypeName?.toUpperCase() || 'TEXT',
                required: false,
                unique: false,
                description: '',
                attributes: {},
                validations: {}
              };
              console.log(`Created default metadata for field ${index} without additionalInformation:`, fieldMetadata);
            }

            // Get display preference from the field data
            const displayPreference = fieldData.displayPreference || 0;
            console.log(`Field ${fieldMetadata.name || 'Unnamed Field'} has display preference: ${displayPreference}`);

            // Create a field object with display preference
            const processedField = {
              id: fieldData.id.toString(),
              name: fieldMetadata.name || `Field ${fieldData.id}`,
              apiId: fieldMetadata.apiId || `field_${fieldData.id}`,
              type: fieldMetadata.type as FieldTypeEnum || FieldTypeEnum.TEXT,
              required: fieldMetadata.required || false,
              unique: fieldMetadata.unique || false,
              description: fieldMetadata.description || '',
              attributes: fieldMetadata.attributes || {},
              validations: fieldMetadata.validations || {},
              fieldTypeId: fieldData.fieldType?.id,
              displayPreference: displayPreference
            };

            console.log(`Processed field ${index}:`, processedField);
            return processedField;
          } catch (error) {
            console.error(`Error processing field ${index}:`, error);
            // Instead of returning null, return a default field object
            return {
              id: fieldData.id?.toString() || `error_${index}`,
              name: `Error Processing Field ${fieldData.id || index}`,
              type: FieldTypeEnum.TEXT,
              displayPreference: (index + 1) * 10,
              fieldTypeId: fieldData.fieldType?.id
            };
          }
        }).filter(Boolean);

        console.log('Final processed fields:', processedFields);

        // Update the cache with the processed fields
        setComponentFieldsCache(prev => ({
          ...prev,
          [componentId]: {
            ...prev[componentId],
            fields: processedFields,
            loading: false,
            error: null
          }
        }));

        // Also fetch child components for this component
        console.log('Fetching child components for component ID:', actualComponentId);
        fetchChildComponents(componentId, actualComponentId);
      } catch (apiError: any) {
        console.error('API request failed:', apiError);
        console.error('API error response:', apiError.response);
        throw apiError;
      }
    } catch (error: any) {
      console.error('Error in fetchComponentFields:', error);
      console.error('Error stack:', error.stack);
      setComponentFieldsCache(prev => ({
        ...prev,
        [componentId]: {
          fields: [],
          loading: false,
          error: error.message || 'Failed to load component fields'
        }
      }));
    }
  }, [fields]);

  // Drag and drop handlers
  const handleDragStart = (field: Field) => (e: React.DragEvent) => {
    if (!reorderMode) return;
    setDraggedField(field);
    e.dataTransfer.setData('text/plain', field.id || '');
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (field: Field) => (e: React.DragEvent) => {
    if (!reorderMode || !draggedField) return;
    e.preventDefault();
    setDragOverFieldId(field.id || null);
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDragEnd = () => {
    setDraggedField(null);
    setDragOverFieldId(null);
  };

  const handleDrop = (targetField: Field) => (e: React.DragEvent) => {
    if (!reorderMode || !draggedField || !onReorderFields) return;
    e.preventDefault();

    // Get the current order of fields
    const reorderedFields = [...fields];

    // Find the indices of the dragged and target fields
    const draggedIndex = reorderedFields.findIndex(f => f.id === draggedField.id);
    const targetIndex = reorderedFields.findIndex(f => f.id === targetField.id);

    if (draggedIndex === -1 || targetIndex === -1) return;

    // Reorder the fields
    const [removed] = reorderedFields.splice(draggedIndex, 1);
    reorderedFields.splice(targetIndex, 0, removed);

    // Extract component and field IDs
    const componentIds: number[] = [];
    const fieldIds: number[] = [];

    // Update display preferences for all fields
    reorderedFields.forEach((field, index) => {
      // Set display preference based on position (multiply by 10 to leave room for insertion)
      const displayPreference = (index + 1) * 10;

      // Update the display preference in the field object
      field.displayPreference = displayPreference;

      // Extract IDs for the API call
      if (field.type === FieldTypeEnum.COMPONENT && field.attributes?.collectionComponentId) {
        const componentId = parseInt(field.attributes.collectionComponentId.toString(), 10);
        if (!isNaN(componentId)) {
          componentIds.push(componentId);
        }
      } else if (field.id && !field.id.startsWith('component_')) {
        try {
          const fieldId = parseInt(field.id, 10);
          if (!isNaN(fieldId)) {
            fieldIds.push(fieldId);
          }
        } catch (error) {
          // Ignore parsing errors
        }
      }
    });

    // Call the reorder function
    onReorderFields(componentIds, fieldIds);

    // Reset drag state
    setDraggedField(null);
    setDragOverFieldId(null);
  };

  // Toggle reorder mode
  const toggleReorderMode = () => {
    setReorderMode(!reorderMode);
    setDraggedField(null);
    setDragOverFieldId(null);
  };

  // Toggle component expanded state
  const toggleComponentExpanded = (componentId: string) => {
    setComponentFieldsCache(prev => ({
      ...prev,
      [componentId]: {
        ...prev[componentId],
        expanded: !prev[componentId]?.expanded
      }
    }));
  };

  // Open component field preferences dialog
  const openComponentFieldPreferences = (component: Field) => {
    const componentId = component.componentId || component.attributes?.componentId;
    if (!componentId) {
      toast({
        title: 'Error',
        description: 'Component ID not found',
        variant: 'destructive'
      });
      return;
    }

    setSelectedComponent({
      id: componentId.toString(),
      name: component.name || 'Component'
    });
    setFieldPreferencesOpen(true);
  };

  // Open child component field preferences dialog
  const openChildComponentFieldPreferences = (childComponent: any) => {
    if (!childComponent || !childComponent.childComponent || !childComponent.childComponent.id) {
      toast({
        title: 'Error',
        description: 'Child component ID not found',
        variant: 'destructive'
      });
      return;
    }

    setSelectedComponent({
      id: childComponent.childComponent.id.toString(),
      name: childComponent.childComponent.componentDisplayName || childComponent.childComponent.componentName || 'Component'
    });
    setFieldPreferencesOpen(true);
  };

  // Process all fields together and sort by display preference
  const allSortedFields = React.useMemo(() => {
    // Safety check - if fields is not an array, return empty array
    if (!Array.isArray(fields)) {
      console.error('Fields is not an array:', fields);
      return [];
    }

    // Filter out any null or undefined fields
    const validFields = fields.filter(field => field != null);

    if (validFields.length !== fields.length) {
      console.warn(`Filtered out ${fields.length - validFields.length} null or undefined fields`);
    }

    // Create a copy of the fields array to avoid mutating the original
    const allFields = [...validFields];

    // Assign default display preferences to fields that don't have them
    allFields.forEach((field, index) => {
      if (typeof field.displayPreference !== 'number') {
        console.log(`Field ${field.name || 'Unnamed'} has no display preference, assigning default: ${(index + 1) * 10}`);
        field.displayPreference = (index + 1) * 10;
      }
    });

    // Sort all fields by display preference
    allFields.sort((a, b) => {
      // Use 999 as default for undefined display preferences to push them to the end
      const prefA = typeof a.displayPreference === 'number' ? a.displayPreference : 999;
      const prefB = typeof b.displayPreference === 'number' ? b.displayPreference : 999;

      console.log(`Sorting fields: ${a.name || 'Unnamed'} (${prefA}) vs ${b.name || 'Unnamed'} (${prefB})`);
      return prefA - prefB;
    });

    console.log('All sorted fields:', allFields.map(f => ({ id: f.id, name: f.name, displayPref: f.displayPreference })));
    return allFields;
  }, [fields]);

  // Group fields by type (regular fields vs component fields) for rendering
  const groupedFields = React.useMemo(() => {
    const regularFields: Field[] = [];
    const componentFields: { component: Field }[] = [];

    // Separate fields by type while maintaining the sorted order
    allSortedFields.forEach(field => {
      if (field.type === FieldTypeEnum.COMPONENT) {
        componentFields.push({ component: field });
      } else {
        regularFields.push(field);
      }
    });

    return { regularFields, componentFields };
  }, [allSortedFields]);

  // Function to fetch child components for a component
  const fetchChildComponents = React.useCallback(async (componentId: string, actualComponentId: string) => {
    // If we're already loading this component's child components, don't fetch again
    if (componentFieldsCache[componentId]?.childComponentsLoading) return;

    // Initialize or update the cache entry for this component
    setComponentFieldsCache(prev => ({
      ...prev,
      [componentId]: {
        ...prev[componentId],
        childComponentsLoading: true,
        childComponentsError: null,
        // Initialize expanded state for child components
        expandedChildComponents: prev[componentId]?.expandedChildComponents || {},
        // Initialize child component fields cache
        childComponentFields: prev[componentId]?.childComponentFields || {},
        childComponentFieldsLoading: prev[componentId]?.childComponentFieldsLoading || {}
      }
    }));

    try {
      // Fetch child components from the API
      console.log('Fetching child components for component ID:', actualComponentId);
      const response = await componentComponentsApi.getByParentId(actualComponentId.toString());
      console.log('Child components response:', response.data);

      // Initialize expanded state for each child component
      const initialExpandedState: {[key: string]: boolean} = {};
      const childComponents = response.data || [];
      childComponents.forEach((component: ChildComponent) => {
        // Start expanded by default
        initialExpandedState[component.id] = true;
        // Fetch fields for each child component right away
        if (component.childComponent && component.childComponent.id) {
          fetchChildComponentFields(componentId, component.id, component.childComponent.id.toString());
        }
      });

      // Update the cache with the child components
      setComponentFieldsCache(prev => ({
        ...prev,
        [componentId]: {
          ...prev[componentId],
          childComponents: childComponents,
          childComponentsLoading: false,
          expanded: true, // Start expanded by default
          expandedChildComponents: {
            ...prev[componentId]?.expandedChildComponents,
            ...initialExpandedState
          }
        }
      }));
    } catch (error) {
      console.error('Error fetching child components:', error);
      setComponentFieldsCache(prev => ({
        ...prev,
        [componentId]: {
          ...prev[componentId],
          childComponentsLoading: false,
          childComponentsError: error instanceof Error ? error.message : 'Unknown error'
        }
      }));
    }
  }, [componentFieldsCache]);

  // Function to fetch fields for a specific child component
  const fetchChildComponentFields = React.useCallback(async (parentComponentId: string, relationshipId: string, childComponentId: string) => {
    if (!childComponentId || !parentComponentId) return;

    // Set loading state for this specific child component
    setComponentFieldsCache(prev => ({
      ...prev,
      [parentComponentId]: {
        ...prev[parentComponentId],
        childComponentFieldsLoading: {
          ...prev[parentComponentId]?.childComponentFieldsLoading,
          [childComponentId]: true
        }
      }
    }));

    try {
      console.log(`Fetching fields for child component ID: ${childComponentId}`);

      // Fetch component details to get fields
      const response = await componentsApi.getByIdWithDetails(childComponentId);
      console.log(`Fields for child component ${childComponentId}:`, response.data?.fields);

      // Update the state with the fields
      setComponentFieldsCache(prev => ({
        ...prev,
        [parentComponentId]: {
          ...prev[parentComponentId],
          childComponentFields: {
            ...prev[parentComponentId]?.childComponentFields,
            [childComponentId]: response.data?.fields || []
          },
          childComponentFieldsLoading: {
            ...prev[parentComponentId]?.childComponentFieldsLoading,
            [childComponentId]: false
          }
        }
      }));

    } catch (error) {
      console.error(`Error fetching fields for child component ${childComponentId}:`, error);

      // Set empty array on error to avoid showing loading indefinitely
      setComponentFieldsCache(prev => ({
        ...prev,
        [parentComponentId]: {
          ...prev[parentComponentId],
          childComponentFields: {
            ...prev[parentComponentId]?.childComponentFields,
            [childComponentId]: []
          },
          childComponentFieldsLoading: {
            ...prev[parentComponentId]?.childComponentFieldsLoading,
            [childComponentId]: false
          }
        }
      }));
    }
  }, []);

  // Toggle expanded state for a child component
  const toggleChildComponentExpanded = React.useCallback((parentComponentId: string, relationshipId: string, childComponentId: string) => {
    // Toggle the expanded state
    setComponentFieldsCache(prev => {
      const currentExpandedState = prev[parentComponentId]?.expandedChildComponents?.[relationshipId] || false;

      return {
        ...prev,
        [parentComponentId]: {
          ...prev[parentComponentId],
          expandedChildComponents: {
            ...prev[parentComponentId]?.expandedChildComponents,
            [relationshipId]: !currentExpandedState
          }
        }
      };
    });
  }, []);

  // Handle editing a child component field
  const handleEditChildField = React.useCallback(async (componentId: string, fieldId: string) => {
    try {
      // Redirect to the field edit page
      window.open(`/components/${componentId}/fields/${fieldId}/edit`, '_blank');
    } catch (error) {
      console.error('Error navigating to edit field:', error);
      toast({
        title: 'Error',
        description: 'Could not open field editor',
        variant: 'destructive'
      });
    }
  }, [toast]);

  // Handle deleting a child component field
  const handleDeleteChildField = React.useCallback(async (componentId: string, fieldId: string, fieldName: string) => {
    if (!confirm(`Are you sure you want to delete the field "${fieldName}"?`)) {
      return;
    }

    try {
      // Call the API to delete the field
      await componentFieldsApi.delete(fieldId);

      // Update the cache to remove the deleted field
      setComponentFieldsCache(prev => {
        const componentCache = prev[componentId];
        if (!componentCache) return prev;

        // Find the child component ID that contains this field
        let updatedChildComponentFields = { ...componentCache.childComponentFields };

        // For each child component, check if it contains the field and remove it
        Object.keys(updatedChildComponentFields).forEach(childComponentId => {
          const fields = updatedChildComponentFields[childComponentId] || [];
          const updatedFields = fields.filter(field => field.id !== fieldId);
          updatedChildComponentFields[childComponentId] = updatedFields;
        });

        return {
          ...prev,
          [componentId]: {
            ...componentCache,
            childComponentFields: updatedChildComponentFields
          }
        };
      });

      toast({
        title: 'Success',
        description: `Field "${fieldName}" has been deleted`,
      });
    } catch (error) {
      console.error('Error deleting field:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete field',
        variant: 'destructive'
      });
    }
  }, [toast]);

  // Fetch component fields when the component mounts or fields change
  React.useEffect(() => {
    // Find all component fields
    console.log('All fields:', fields);
    console.log('Fields data type:', typeof fields);
    console.log('Is fields an array?', Array.isArray(fields));

    // Enhanced debugging for all fields
    if (Array.isArray(fields)) {
      fields.forEach((field, index) => {
        console.log(`Field ${index}:`, field);
        console.log(`Field ${index} ID:`, field.id);
        console.log(`Field ${index} type:`, field.type);
        console.log(`Field ${index} name:`, field.name);
        console.log(`Field ${index} display preference:`, field.displayPreference);
        console.log(`Field ${index} attributes:`, field.attributes);

        // Check if this is a component field
        if (field.type === FieldTypeEnum.COMPONENT) {
          console.log(`Component field ${index} componentId:`, field.componentId);
          console.log(`Component field ${index} attributes.componentId:`, field?.attributes?.componentId);
        }
      });
    }

    const componentFields = Array.isArray(fields) ? fields.filter(field => field.type === FieldTypeEnum.COMPONENT) : [];
    console.log('Component fields found:', componentFields);
    console.log('Number of component fields:', componentFields.length);

    // Log display preferences for debugging
    componentFields.forEach(field => {
      console.log(`Component ${field.name} display preference: ${field.displayPreference || 'not set'}`);
    });

    // Fetch fields for each component
    componentFields.forEach(field => {
      console.log('Processing component field:', field);
      if (field.id) {
        console.log('Fetching fields for component ID:', field.id);
        fetchComponentFields(field.id);
      } else {
        console.log('Field ID is missing, cannot fetch component fields');
      }
    });

    // Also log regular fields for debugging
    const regularFields = Array.isArray(fields) ? fields.filter(field => field.type !== FieldTypeEnum.COMPONENT) : [];
    console.log('Regular fields found:', regularFields);
    console.log('Number of regular fields:', regularFields.length);

    // Check if there are any fields at all
    if (!Array.isArray(fields) || fields.length === 0) {
      console.error('No fields provided to NestedFieldsDisplay component');
    }
  }, [fields, fetchComponentFields]);

  // Render a field row
  const renderFieldRow = (field: Field, isNested: boolean = false, isLast: boolean = false) => {
    // Safety check - if field is undefined or null, return null
    if (!field) {
      console.error('Attempted to render undefined or null field');
      return null;
    }

    console.log('Rendering field row for field:', field);

    // Determine field type for icon
    let fieldType = field.type || FieldTypeEnum.TEXT;
    let fieldTypeName = '';

    // Get display preference
    const displayPreference = field.displayPreference || 0;
    console.log(`Rendering field ${field.name || 'Unnamed'} with display preference: ${displayPreference}`);

    // If field has fieldTypeId, try to get the field type name from it
    if (field.fieldTypeId) {
      console.log('Field has fieldTypeId:', field.fieldTypeId);
      // Map common field type IDs to enum values
      switch (field.fieldTypeId) {
        case 1: fieldType = FieldTypeEnum.TEXT; fieldTypeName = 'Text'; break;
        case 2: fieldType = FieldTypeEnum.NUMBER; fieldTypeName = 'Number'; break;
        case 3: fieldType = FieldTypeEnum.DATE; fieldTypeName = 'Date'; break;
        case 4: fieldType = FieldTypeEnum.IMAGE; fieldTypeName = 'Image'; break;
        case 5: fieldType = FieldTypeEnum.RICH_TEXT; fieldTypeName = 'Rich Text'; break;
        case 6: fieldType = FieldTypeEnum.MASK; fieldTypeName = 'Mask'; break;
        case 7: fieldType = FieldTypeEnum.CALENDAR; fieldTypeName = 'Calendar'; break;
        case 8: fieldType = FieldTypeEnum.EDITOR; fieldTypeName = 'Editor'; break;
        case 9: fieldType = FieldTypeEnum.PASSWORD; fieldTypeName = 'Password'; break;
        case 10: fieldType = FieldTypeEnum.AUTOCOMPLETE; fieldTypeName = 'Autocomplete'; break;
        case 11: fieldType = FieldTypeEnum.CASCADE_SELECT; fieldTypeName = 'Cascade Select'; break;
        case 12: fieldType = FieldTypeEnum.DROPDOWN; fieldTypeName = 'Dropdown'; break;
        case 13: fieldType = FieldTypeEnum.FILE; fieldTypeName = 'File'; break;
        case 14: fieldType = FieldTypeEnum.MULTI_STATE_CHECKBOX; fieldTypeName = 'Multi State Checkbox'; break;
        case 15: fieldType = FieldTypeEnum.MULTI_SELECT; fieldTypeName = 'Multi Select'; break;
        case 16: fieldType = FieldTypeEnum.MENTION; fieldTypeName = 'Mention'; break;
        case 17: fieldType = FieldTypeEnum.TEXTAREA; fieldTypeName = 'Textarea'; break;
        case 18: fieldType = FieldTypeEnum.OTP; fieldTypeName = 'OTP'; break;
        case 19: fieldType = FieldTypeEnum.EMAIL; fieldTypeName = 'Email'; break;
        case 20: fieldType = FieldTypeEnum.BOOLEAN; fieldTypeName = 'Boolean'; break;
        // Add more mappings as needed
        default:
          console.log(`Unknown field type ID: ${field.fieldTypeId}, using default TEXT type`);
          fieldType = FieldTypeEnum.TEXT;
          fieldTypeName = 'Text';
          break;
      }
    } else {
      console.log('Field has no fieldTypeId, using field.type:', field.type);
      // If no fieldTypeId, use the field.type directly
      fieldTypeName = getFieldTypeDisplay(field.type || 'TEXT');
    }

    // Get the appropriate icon for this field type
    const fieldIcon = getFieldIcon(fieldType);
    console.log('Using field icon for type:', fieldType);

    return (
      <div
        key={field.id}
        className={`grid grid-cols-12 gap-4 py-2 px-3 hover:bg-muted/50 ${isNested ? 'border-l-2 border-border ml-8' : ''} ${
          reorderMode ? 'cursor-move' : ''
        } ${
          dragOverFieldId === field.id ? 'border-2 border-primary' : ''
        }`}
        draggable={reorderMode}
        onDragStart={reorderMode ? handleDragStart(field) : undefined}
        onDragOver={reorderMode ? handleDragOver(field) : undefined}
        onDragEnd={reorderMode ? handleDragEnd : undefined}
        onDrop={reorderMode ? handleDrop(field) : undefined}
      >
        <div className="col-span-5 flex items-center">
          <div className="w-8 h-8 bg-primary/10 text-primary rounded flex items-center justify-center mr-3 border border-border">
            {fieldIcon}
          </div>
          <div className="flex flex-col">
            <span>{field.name}</span>
          </div>
        </div>
        <div className="col-span-5 flex items-center">
          <div className="w-6 h-6 rounded-md bg-primary/10 text-primary flex items-center justify-center mr-2">
            {fieldType.charAt(0).toUpperCase()}
          </div>
          <span>{fieldTypeName || field.type || 'Unknown'}</span>
        </div>
        <div className="col-span-2 flex items-center justify-end">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onEditField(field)}
            className="h-8 w-8 text-gray-500 hover:text-gray-700"
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onDeleteField(field.id!)}
            className="h-8 w-8 text-gray-500 hover:text-gray-700"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  };

  // Render a component section with its fields
  const renderComponentSection = (component: Field, _index: number) => {
    // Safety check - if component is undefined or null, return null
    if (!component) {
      console.error('Attempted to render undefined or null component');
      return null;
    }

    console.log('Rendering component section for component:', component);

    const componentName = component.name || 'Component';
    const componentId = component.id || `unknown_${_index}`;
    console.log(`Component ID: ${componentId}, Name: ${componentName}`);

    // Default to expanded if not set or if component ID is missing
    const isExpanded = componentId ? (componentFieldsCache[componentId]?.expanded !== false) : true;
    console.log(`Component expanded state: ${isExpanded}`);

    const actualComponentId = component.componentId || component.attributes?.componentId;
    console.log(`Actual component ID: ${actualComponentId}`);

    // If component ID is missing, show an error message
    if (!actualComponentId) {
      console.error('Component ID is missing for component:', component);
    }

    return (
      <div key={componentId} className="mb-4">
        {/* Component header */}
        <div className="flex items-center justify-between py-2 px-3 bg-primary/5 hover:bg-primary/10 border-b border-border">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => componentId && toggleComponentExpanded(componentId)}
              className="h-8 w-8 text-primary hover:text-primary/80 mr-1 p-1"
            >
              {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </Button>
            <div className="w-8 h-8 bg-primary/10 rounded flex items-center justify-center mr-3 border border-border">
              <Layers className="h-4 w-4 text-primary" />
            </div>
            <span className="font-medium">{componentName}</span>
            {!actualComponentId && (
              <span className="ml-2 text-xs text-destructive">Missing component ID</span>
            )}
          </div>
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => openComponentFieldPreferences(component)}
              className="h-8 w-8 text-muted-foreground hover:text-foreground"
              title="Field Display Preferences"
              disabled={!actualComponentId}
            >
              <ListOrdered className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onEditField(component)}
              className="h-8 w-8 text-muted-foreground hover:text-foreground"
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => componentId && onDeleteField(componentId)}
              className="h-8 w-8 text-muted-foreground hover:text-destructive"
              disabled={!componentId}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Component fields */}
        <div className="ml-4">
          {/* Debug logs */}
          {(() => {
            console.log('Rendering component fields for component ID:', component.id);
            console.log('Component fields cache state:', componentFieldsCache[component.id]);
            return null;
          })()}
          {componentFieldsCache[component.id]?.loading ? (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin h-5 w-5 border-2 border-primary rounded-full border-t-transparent mr-2"></div>
              <span className="text-muted-foreground">Loading fields...</span>
            </div>
          ) : componentFieldsCache[component.id]?.error ? (
            <div className="flex items-center justify-center py-4">
              <span className="text-destructive">Error loading fields</span>
            </div>
          ) : componentFieldsCache[component.id]?.fields?.length ? (
            <>
              {/* Debug logs */}
              {(() => {
                console.log('Rendering actual fields for component:', component.name);
                console.log('Fields:', componentFieldsCache[component.id].fields);
                return null;
              })()}
              {/* Render actual fields - sorted by display preference */}
              {componentFieldsCache[component.id].fields
                .slice() // Create a copy to avoid mutating the original array
                .sort((a, b) => {
                  // Sort by display preference
                  const prefA = a.displayPreference || 0;
                  const prefB = b.displayPreference || 0;
                  console.log(`Sorting fields: ${a.name} (${prefA}) vs ${b.name} (${prefB})`);
                  return prefA - prefB;
                })
                .map((field, i, arr) => (
                  renderFieldRow(
                    field,
                    true,
                    i === arr.length - 1 // is last item
                  )
                ))}
            </>
          ) : (
            <div className="flex items-center justify-center py-4">
              <span className="text-gray-500">No fields found for this component</span>
            </div>
          )}

          {/* Add field to component button */}
          <div className="flex items-center ml-8 mt-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onAddFieldToComponent(component.id!)}
              className="text-blue-600 hover:text-blue-800 hover:bg-blue-50"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add another field to this component
            </Button>
          </div>

          {/* Child Components Section */}
          {isExpanded && (
            <div className="mt-4">
              {componentFieldsCache[component.id!]?.childComponentsLoading ? (
                <div className="flex items-center justify-center py-4 ml-8">
                  <div className="animate-spin h-5 w-5 border-2 border-primary rounded-full border-t-transparent mr-2"></div>
                  <span className="text-muted-foreground">Loading child components...</span>
                </div>
              ) : componentFieldsCache[component.id!]?.childComponentsError ? (
                <div className="flex items-center justify-center py-4 ml-8">
                  <span className="text-destructive">Error loading child components</span>
                </div>
              ) : componentFieldsCache[component.id!]?.childComponents?.length ? (
                <div className="ml-8 border-l-2 border-border pl-4">
                  <div className="text-sm font-medium text-muted-foreground mb-2">Child Components:</div>
                  {componentFieldsCache[component.id!].childComponents!.map((childComponent) => {
                    const isChildExpanded = componentFieldsCache[component.id!]?.expandedChildComponents?.[childComponent.id] !== false;
                    const childComponentId = childComponent.childComponent.id.toString();
                    const isLoading = componentFieldsCache[component.id!]?.childComponentFieldsLoading?.[childComponentId];
                    const childFields = componentFieldsCache[component.id!]?.childComponentFields?.[childComponentId] || [];

                    return (
                      <div key={childComponent.id} className="mb-2 border-b border-border/30">
                        {/* Child component header */}
                        <div className="flex items-center justify-between py-2 px-3 hover:bg-muted/20">
                          <div className="flex items-center">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => toggleChildComponentExpanded(component.id!, childComponent.id, childComponentId)}
                              className="h-6 w-6 text-primary hover:text-primary/80 mr-1 p-1"
                            >
                              {isChildExpanded ? <ChevronDown className="h-3 w-3" /> : <ChevronRight className="h-3 w-3" />}
                            </Button>
                            <div className="w-6 h-6 bg-primary/10 rounded flex items-center justify-center mr-3 border border-border">
                              <Layers className="h-3 w-3 text-primary" />
                            </div>
                            <div>
                              <span>{childComponent.childComponent.componentDisplayName || childComponent.childComponent.componentName}</span>
                              {childComponent.isRepeatable && (
                                <span className="ml-2 text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full border border-border">Repeatable</span>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => openChildComponentFieldPreferences(childComponent)}
                              className="h-6 w-6 text-muted-foreground hover:text-foreground p-1"
                              title="Field Display Preferences"
                            >
                              <ListOrdered className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>

                        {/* Child component fields */}
                        {isChildExpanded && (
                          <div className="pl-10 pr-3 pb-2">
                            {isLoading ? (
                              <div className="flex items-center justify-center py-2">
                                <div className="animate-spin h-4 w-4 border-2 border-primary rounded-full border-t-transparent mr-2"></div>
                                <span className="text-muted-foreground text-sm">Loading fields...</span>
                              </div>
                            ) : childFields.length > 0 ? (
                              <div className="space-y-1">
                                {childFields
                                  .slice() // Create a copy to avoid mutating the original array
                                  .sort((a, b) => {
                                    // Sort by display preference
                                    const prefA = a.displayPreference || 0;
                                    const prefB = b.displayPreference || 0;

                                    // Get field names for logging
                                    let nameA = 'Unnamed';
                                    let nameB = 'Unnamed';
                                    try {
                                      if (a.additionalInformation) {
                                        const metadataA = JSON.parse(a.additionalInformation);
                                        nameA = metadataA.name || 'Unnamed';
                                      }
                                      if (b.additionalInformation) {
                                        const metadataB = JSON.parse(b.additionalInformation);
                                        nameB = metadataB.name || 'Unnamed';
                                      }
                                    } catch (e) {
                                      console.error('Error parsing additionalInformation:', e);
                                    }

                                    console.log(`Sorting child fields: ${nameA} (${prefA}) vs ${nameB} (${prefB})`);
                                    return prefA - prefB;
                                  })
                                  .map((field: any) => (
                                  <div key={field.id} className="grid grid-cols-12 gap-4 py-2 px-3 hover:bg-muted/50 rounded text-sm">
                                    <div className="col-span-5 flex items-center">
                                      <div className="w-8 h-8 bg-primary/10 text-primary rounded flex items-center justify-center mr-3 border border-border">
                                        {(() => {
                                          // Determine field type for icon
                                          let fieldType = field.fieldType?.fieldTypeName || 'TEXT';

                                          // Map field type to appropriate icon
                                          switch (fieldType.toUpperCase()) {
                                            case 'TEXT': return <span className="text-xs">Aa</span>;
                                            case 'NUMBER': return <span className="text-xs">123</span>;
                                            case 'EMAIL': return <span className="text-xs">@</span>;
                                            case 'DROPDOWN': return <span className="text-xs">▼</span>;
                                            default: return getFieldIcon(fieldType);
                                          }
                                        })()}
                                      </div>
                                      <div className="flex flex-col">
                                        <span>
                                          {(() => {
                                            // Try to get the field name from additionalInformation
                                            if (field.additionalInformation) {
                                              try {
                                                const metadata = JSON.parse(field.additionalInformation);
                                                if (metadata.name) {
                                                  return metadata.name;
                                                }
                                              } catch (e) {
                                                console.error('Error parsing additionalInformation:', e);
                                              }
                                            }
                                            // Fallback to other field name properties
                                            return field.fieldDisplayName || field.fieldName || field.name || 'Unnamed Field';
                                          })()}
                                        </span>
                                      </div>
                                    </div>
                                    <div className="col-span-5 flex items-center">
                                      <div className="w-6 h-6 rounded-md bg-primary/10 text-primary flex items-center justify-center mr-2">
                                        {(field.fieldType?.fieldTypeName || 'T').charAt(0).toUpperCase()}
                                      </div>
                                      <span>{field.fieldType?.displayName || field.fieldType?.fieldTypeName || 'Unknown Type'}</span>
                                    </div>
                                    <div className="col-span-2 flex items-center justify-end">
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => handleEditChildField(childComponentId, field.id)}
                                        className="h-8 w-8 text-muted-foreground hover:text-foreground"
                                      >
                                        <Edit className="h-4 w-4" />
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => {
                                          const fieldName = (() => {
                                            // Try to get the field name from additionalInformation
                                            if (field.additionalInformation) {
                                              try {
                                                const metadata = JSON.parse(field.additionalInformation);
                                                if (metadata.name) {
                                                  return metadata.name;
                                                }
                                              } catch (e) {
                                                console.error('Error parsing additionalInformation:', e);
                                              }
                                            }
                                            // Fallback to other field name properties
                                            return field.fieldDisplayName || field.fieldName || field.name || 'Unnamed Field';
                                          })();

                                          handleDeleteChildField(childComponentId, field.id, fieldName);
                                        }}
                                        className="h-8 w-8 text-muted-foreground hover:text-destructive"
                                      >
                                        <Trash2 className="h-4 w-4" />
                                      </Button>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <div className="text-sm text-muted-foreground py-1 px-2">
                                No fields defined for this component
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="flex items-center justify-center py-4 ml-8 text-sm text-muted-foreground">
                  No child components for this component
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  // Helper function to get field type display name
  const getFieldTypeDisplay = (type: string): string => {
    switch (type) {
      case FieldTypeEnum.TEXT:
        return 'Text';
      case FieldTypeEnum.NUMBER:
        return 'Number';
      case FieldTypeEnum.BOOLEAN:
        return 'Boolean';
      case FieldTypeEnum.DATE:
        return 'Date';
      case FieldTypeEnum.RICH_TEXT:
        return 'Rich Text';
      case FieldTypeEnum.MEDIA:
        return 'Media';
      case FieldTypeEnum.ENUM:
        return 'Enum';
      case FieldTypeEnum.RELATION:
        return 'Relation';
      case FieldTypeEnum.COMPONENT:
        return 'Component';
      default:
        return type;
    }
  };

  // Helper function to get field icon
  const getFieldIcon = (type: string) => {
    // Map field types to icons
    switch (type) {
      // Text-based fields
      case FieldTypeEnum.TEXT:
        return <span className="text-xs">Aa</span>;
      case FieldTypeEnum.TEXTAREA:
        return <span className="text-xs">¶</span>;
      case FieldTypeEnum.EMAIL:
        return <span className="text-xs">@</span>;
      case FieldTypeEnum.RICH_TEXT:
      case FieldTypeEnum.RICH_TEXT_BLOCKS:
      case FieldTypeEnum.RICH_TEXT_MARKDOWN:
        return <span className="text-xs">∷</span>;
      case FieldTypeEnum.EDITOR:
        return <span className="text-xs">≡</span>;
      case FieldTypeEnum.MASK:
      case FieldTypeEnum.INPUT_MASK:
        return <span className="text-xs">##</span>;
      case FieldTypeEnum.PASSWORD:
        return <span className="text-xs">***</span>;
      case FieldTypeEnum.OTP:
        return <span className="text-xs">⌨</span>;

      // Number-based fields
      case FieldTypeEnum.NUMBER:
        return <span className="text-xs">123</span>;

      // Date-based fields
      case FieldTypeEnum.DATE:
      case FieldTypeEnum.CALENDAR:
        return <span className="text-xs">📅</span>;

      // Boolean fields
      case FieldTypeEnum.BOOLEAN:
      case FieldTypeEnum.MULTI_STATE_CHECKBOX:
        return <span className="text-xs">✓</span>;

      // Media fields
      case FieldTypeEnum.MEDIA:
      case FieldTypeEnum.IMAGE:
      case FieldTypeEnum.FILE:
        return <span className="text-xs">🖼️</span>;

      // Selection fields
      case FieldTypeEnum.ENUM:
      case FieldTypeEnum.ENUMERATION:
      case FieldTypeEnum.DROPDOWN:
        return <span className="text-xs">▼</span>;
      case FieldTypeEnum.MULTI_SELECT:
        return <span className="text-xs">▼▼</span>;
      case FieldTypeEnum.AUTOCOMPLETE:
        return <span className="text-xs">⌨️</span>;
      case FieldTypeEnum.CASCADE_SELECT:
        return <span className="text-xs">📂</span>;

      // Relation fields
      case FieldTypeEnum.RELATION:
        return <span className="text-xs">🔗</span>;

      // Component fields
      case FieldTypeEnum.COMPONENT:
        return <span className="text-xs">⚙️</span>;

      // Other fields
      case FieldTypeEnum.JSON:
        return <span className="text-xs">{}</span>;
      case FieldTypeEnum.UID:
        return <span className="text-xs">#</span>;
      case FieldTypeEnum.MENTION:
        return <span className="text-xs">@</span>;

      // Default fallback
      default:
        console.log('Unknown field type:', type);
        return <span className="text-xs">Ab</span>;
    }
  };

  return (
    <div className="bg-card text-card-foreground rounded-md overflow-hidden border">
      {/* Reorder button */}
      {onReorderFields && (
        <div className="flex justify-end p-2 border-b border-border">
          <Button
            variant={reorderMode ? "default" : "outline"}
            size="sm"
            onClick={toggleReorderMode}
            className={`${reorderMode ? 'bg-primary text-primary-foreground' : 'text-primary'}`}
          >
            <MoveVertical className="h-4 w-4 mr-1" />
            {reorderMode ? 'Exit Reorder Mode' : 'Reorder Fields'}
          </Button>
        </div>
      )}

      <div className="divide-y divide-gray-200">
        {/* Render all fields in the sorted order */}
        {allSortedFields.map((field, index) => {
          if (field.type === FieldTypeEnum.COMPONENT) {
            // Render component field
            return renderComponentSection(field, index);
          } else {
            // Render regular field
            return renderFieldRow(field, false, index === allSortedFields.length - 1);
          }
        })}
      </div>

      {/* Component Field Preferences Dialog */}
      {selectedComponent && (
        <ComponentFieldPreferences
          isOpen={fieldPreferencesOpen}
          onClose={() => setFieldPreferencesOpen(false)}
          componentId={selectedComponent.id}
          componentName={selectedComponent.name}
          onSuccess={() => {
            // Refresh the component fields
            if (selectedComponent.id) {
              // Force refresh all components to ensure display preferences are updated
              console.log('Refreshing all components after display preference update');

              // Refresh top-level components
              fields.forEach(field => {
                if (field.type === FieldTypeEnum.COMPONENT && field.id) {
                  console.log(`Refreshing top-level component: ${field.name} (${field.id})`);
                  fetchComponentFields(field.id);
                }
              });

              // Also refresh the specific component that was updated
              const componentId = Object.keys(componentFieldsCache).find(key => {
                const actualComponentId = componentFieldsCache[key]?.fields?.[0]?.component?.id;
                return actualComponentId?.toString() === selectedComponent.id;
              });

              if (componentId) {
                console.log(`Refreshing specific component: ${componentId}`);
                fetchComponentFields(componentId);
              }

              toast({
                title: 'Success',
                description: 'Component field display preferences updated',
              });
            }
          }}
        />
      )}
    </div>
  );
}

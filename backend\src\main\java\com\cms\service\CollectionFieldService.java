package com.cms.service;

import com.cms.entity.CollectionField;

import java.util.List;

public interface CollectionFieldService {
    CollectionField getCollectionFieldById(Integer id);
    List<CollectionField> getAllCollectionFields();
    List<CollectionField> getCollectionFieldsByCollectionId(Integer collectionId);
    CollectionField createCollectionField(CollectionField collectionField);
    CollectionField updateCollectionField(Integer id, CollectionField collectionField);
    void deleteCollectionField(Integer id);
    Integer getNextAvailableId();
}

package com.cms.service.impl;

import com.cms.config.TenantContextHolder;
import com.cms.entity.Category;
import com.cms.entity.Client;
import com.cms.exception.ResourceNotFoundException;
import com.cms.repository.CategoryRepository;
import com.cms.repository.ClientRepository;
import com.cms.service.CategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of CategoryService
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CategoryServiceImpl implements CategoryService {

    private final CategoryRepository categoryRepository;
    private final ClientRepository clientRepository;

    @Override
    public List<Category> getAllCategories() {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Getting all categories for tenant: {}", currentTenant);

        // The multi-tenant connection provider will automatically set the correct schema
        // based on the tenant context, so we just need to call findAll()
        List<Category> categories = categoryRepository.findAll();
        log.info("Found {} categories for tenant: {}", categories.size(), currentTenant);

        return categories;
    }

    @Override
    public Optional<Category> getCategoryById(Integer id) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Getting category by ID: {} for tenant: {}", id, currentTenant);
        return categoryRepository.findById(id);
    }

    @Override
    public Optional<Category> getCategoryByName(String categoryName) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Getting category by name: {} for tenant: {}", categoryName, currentTenant);
        return categoryRepository.findByCategoryName(categoryName);
    }

//    @Override
//    public Category createCategory(Category category) {
//        String currentTenant = TenantContextHolder.getTenantId();
//        log.info("Creating category: {} for tenant: {}", category.getCategoryName(), currentTenant);
//
//        // Set audit fields if not already set
//        if (category.getCreatedBy() == null) {
//            category.setCreatedBy("system");
//        }
//        if (category.getCreatedAt() == null) {
//            category.setCreatedAt(LocalDateTime.now());
//        }
//        if (category.getModifiedBy() == null) {
//            category.setModifiedBy("system");
//        }
//        if (category.getModifiedAt() == null) {
//            category.setModifiedAt(LocalDateTime.now());
//        }
//
//        // If client is provided, validate it exists
//        if (category.getClient() != null && category.getClient().getId() != null) {
//            Integer clientId = category.getClient().getId();
//            Client client = clientRepository.findById(clientId)
//                    .orElseThrow(() -> new ResourceNotFoundException("Client not found with id: " + clientId));
//
//            // Set the full client entity
//            category.setClient(client);
//        }
//
//        return categoryRepository.save(category);
//    }


    @Override
    public Category createCategory(Category category) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Creating category: {} for tenant: {}", category.getCategoryName(), currentTenant);

        // Set audit fields if not already set
        if (category.getCreatedBy() == null) {
            category.setCreatedBy("system");
        }
        if (category.getCreatedAt() == null) {
            category.setCreatedAt(LocalDateTime.now());
        }
        if (category.getModifiedBy() == null) {
            category.setModifiedBy("system");
        }
        if (category.getModifiedAt() == null) {
            category.setModifiedAt(LocalDateTime.now());
        }

        // Validate and set the client
        if (category.getClient() != null && category.getClient().getId() != null) {
            Integer clientId = category.getClient().getId();
            Client client = clientRepository.findById(clientId)
                    .orElseThrow(() -> new ResourceNotFoundException("Client not found with id: " + clientId));
            category.setClient(client);
        }

        // Validate and set the parent category
        if (category.getParentCategory() != null && category.getParentCategory().getId() != null) {
            Integer parentId = category.getParentCategory().getId();
            Category parentCategory = categoryRepository.findById(parentId)
                    .orElseThrow(() -> new ResourceNotFoundException("Parent Category not found with id: " + parentId));
            category.setParentCategory(parentCategory);
        }

        // Log to ensure parent is set
        log.info("Parent category set: {}",
                category.getParentCategory() != null ? category.getParentCategory().getId() : "null");

        return categoryRepository.save(category);
    }



    @Override
    public Category updateCategory(Integer id, Category category) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Updating category with ID: {} for tenant: {}", id, currentTenant);

        Category existingCategory = categoryRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Category not found with id: " + id));

        existingCategory.setCategoryName(category.getCategoryName());

        // Update audit fields
        existingCategory.setModifiedBy(category.getModifiedBy() != null ? category.getModifiedBy() : "system");
        existingCategory.setModifiedAt(LocalDateTime.now());

        // If client is provided, validate it exists
        if (category.getClient() != null && category.getClient().getId() != null) {
            Integer clientId = category.getClient().getId();
            Client client = clientRepository.findById(clientId)
                    .orElseThrow(() -> new ResourceNotFoundException("Client not found with id: " + clientId));

            // Set the full client entity
            existingCategory.setClient(client);
        }

        return categoryRepository.save(existingCategory);
    }

    @Override
    public void deleteCategory(Integer id) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Deleting category with ID: {} for tenant: {}", id, currentTenant);

        Category category = categoryRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Category not found with id: " + id));
        categoryRepository.delete(category);
    }

    @Override
    public boolean existsByCategoryName(String categoryName) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Checking if category with name: {} exists for tenant: {}", categoryName, currentTenant);
        return categoryRepository.existsByCategoryName(categoryName);
    }

    @Override
    public Integer getNextAvailableId() {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Getting next available ID for tenant: {}", currentTenant);

        List<Category> categories = categoryRepository.findAll();
        log.info("Found {} existing categories for tenant: {}", categories.size(), currentTenant);

        if (categories.isEmpty()) {
            log.info("No existing categories for tenant: {}, returning ID 1", currentTenant);
            return 1;
        }

        Integer maxId = categories.stream()
                .map(Category::getId)
                .max(Integer::compareTo)
                .orElse(0);

        log.info("Current max ID: {}, returning next ID: {} for tenant: {}", maxId, (maxId + 1), currentTenant);
        return maxId + 1;
    }

    @Override
    public List<Category> getCategoriesByClientId(Integer clientId) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Getting categories for client ID: {} in tenant: {}", clientId, currentTenant);

        // Check if client exists
        if (!clientRepository.existsById(clientId)) {
            throw new ResourceNotFoundException("Client not found with id: " + clientId);
        }

        // Get categories by client ID using the native query method
        List<Category> categories = categoryRepository.findByClientId(clientId);

        log.info("Found {} categories for client ID: {} in tenant: {}",
                categories.size(), clientId, currentTenant);

        return categories;
    }

    @Override
    @Transactional
    public Category createCategoryForClient(Category category, Integer clientId) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Creating category: {} for client ID: {} in tenant: {}",
                category.getCategoryName(), clientId, currentTenant);

        // Get the client
        Client client = clientRepository.findById(clientId)
                .orElseThrow(() -> new ResourceNotFoundException("Client not found with id: " + clientId));

        // Set the client for the category
        category.setClient(client);

        // Save the category
        return categoryRepository.save(category);
    }

    @Override
    @Transactional
    public Category updateCategoryWithClient(Integer id, Category category, Integer clientId) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Updating category with ID: {} for client ID: {} in tenant: {}",
                id, clientId, currentTenant);

        // Get the existing category
        Category existingCategory = categoryRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Category not found with id: " + id));

        // Get the client
        Client client = clientRepository.findById(clientId)
                .orElseThrow(() -> new ResourceNotFoundException("Client not found with id: " + clientId));

        // Update the category
        existingCategory.setCategoryName(category.getCategoryName());
        existingCategory.setClient(client);

        // Save the updated category
        return categoryRepository.save(existingCategory);
    }

    @Override
    public List<Category> getCategoriesByParentId(Integer parentId) {
        return categoryRepository.findByParentCategoryId(parentId);
    }

    @Override
    public List<Category> getCategoriesByParentIdAndClientId(Integer parentId, Integer clientId) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Getting child categories for parent ID: {} and client ID: {} in tenant: {}", parentId, clientId, currentTenant);

        // Check if client exists
        if (!clientRepository.existsById(clientId)) {
            throw new ResourceNotFoundException("Client not found with id: " + clientId);
        }

        // Get child categories by parent ID and client ID
        List<Category> categories = categoryRepository.findByParentCategoryIdAndClientId(parentId, clientId);

        log.info("Found {} child categories for parent ID: {} and client ID: {} in tenant: {}",
                categories.size(), parentId, clientId, currentTenant);

        return categories;
    }

}

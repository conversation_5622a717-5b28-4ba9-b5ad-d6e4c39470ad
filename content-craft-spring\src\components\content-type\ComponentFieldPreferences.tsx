import React, { useState, useEffect } from 'react';
import { ArrowUp, ArrowDown, Save, X } from 'lucide-react';
import { Field, FieldTypeEnum } from '@/lib/store';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { componentFieldsApi } from '@/lib/api';

interface ComponentFieldPreferencesProps {
  isOpen: boolean;
  onClose: () => void;
  componentId: string;
  componentName: string;
  onSuccess?: () => void;
}

interface FieldWithDisplayPreference {
  id: string;
  name: string;
  type: string;
  fieldTypeId?: number;
  displayPreference: number;
  fieldTypeName?: string;
}

export default function ComponentFieldPreferences({
  isOpen,
  onClose,
  componentId,
  componentName,
  onSuccess
}: ComponentFieldPreferencesProps) {
  const { toast } = useToast();
  const [orderedFields, setOrderedFields] = useState<FieldWithDisplayPreference[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch component fields when the dialog opens
  useEffect(() => {
    if (isOpen && componentId) {
      fetchComponentFields();
    }
  }, [isOpen, componentId]);

  // Function to fetch component fields
  const fetchComponentFields = async () => {
    setIsLoading(true);
    try {
      const response = await componentFieldsApi.getByComponentId(componentId);
      console.log('Component fields:', response.data);

      if (response.data && Array.isArray(response.data)) {
        // Process fields to include display preference
        const fieldsWithPreference = response.data.map((field: any, index: number) => {
          // Parse additional information to get field name
          let fieldName = 'Unnamed Field';
          let fieldType = 'TEXT';

          try {
            if (field.additionalInformation) {
              const metadata = JSON.parse(field.additionalInformation);
              fieldName = metadata.name || 'Unnamed Field';
              fieldType = metadata.type || 'TEXT';
            }
          } catch (error) {
            console.error('Error parsing field metadata:', error);
          }

          // Get display preference or use index-based value
          const displayPref = field.displayPreference || (index + 1) * 10;

          return {
            id: field.id.toString(),
            name: fieldName,
            type: fieldType,
            fieldTypeId: field.fieldType?.id,
            fieldTypeName: field.fieldType?.fieldTypeName || 'text',
            displayPreference: displayPref
          };
        });

        // Sort by display preference
        const sorted = [...fieldsWithPreference].sort(
          (a, b) => a.displayPreference - b.displayPreference
        );

        setOrderedFields(sorted);
      } else {
        setOrderedFields([]);
      }
    } catch (error) {
      console.error('Error fetching component fields:', error);
      toast({
        title: 'Error',
        description: 'Failed to load component fields',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Function to move a field up in the order
  const moveFieldUp = (index: number) => {
    if (index === 0) return; // Already at the top
    const newFields = [...orderedFields];
    const temp = newFields[index];
    newFields[index] = newFields[index - 1];
    newFields[index - 1] = temp;
    setOrderedFields(newFields);
  };

  // Function to move a field down in the order
  const moveFieldDown = (index: number) => {
    if (index === orderedFields.length - 1) return; // Already at the bottom
    const newFields = [...orderedFields];
    const temp = newFields[index];
    newFields[index] = newFields[index + 1];
    newFields[index + 1] = temp;
    setOrderedFields(newFields);
  };

  // Function to update display preference directly
  const updateDisplayPreference = (index: number, value: string) => {
    const numValue = parseInt(value, 10);
    if (isNaN(numValue)) return;

    const newFields = [...orderedFields];
    newFields[index].displayPreference = numValue;
    setOrderedFields(newFields);
  };

  // Function to get field type display name
  const getFieldTypeDisplayName = (field: FieldWithDisplayPreference): string => {
    if (field.fieldTypeName) {
      return field.fieldTypeName.charAt(0).toUpperCase() + field.fieldTypeName.slice(1);
    }

    return field.type || 'Unknown';
  };

  // Function to save the updated display preferences
  const saveDisplayPreferences = async () => {
    setIsSaving(true);
    try {
      // Validate that all fields have valid display preferences
      const invalidFields = orderedFields.filter(field => {
        const pref = field.displayPreference;
        return isNaN(pref) || pref < 0;
      });

      if (invalidFields.length > 0) {
        throw new Error(`Invalid display preferences for fields: ${invalidFields.map(f => f.name).join(', ')}`);
      }

      // Sort by display preference
      const sortedFields = [...orderedFields].sort(
        (a, b) => a.displayPreference - b.displayPreference
      );

      // Update display preferences in sequential order (10, 20, 30, etc.)
      const normalizedFields = sortedFields.map((field, index) => ({
        ...field,
        displayPreference: (index + 1) * 10
      }));

      // Update each field with its new display preference
      const updatePromises = normalizedFields.map(async field => {
        console.log(`Updating field ${field.name} with display preference: ${field.displayPreference}`);

        try {
          // First, get the current field data to preserve all required fields
          const currentField = await componentFieldsApi.getById(field.id).then(res => res.data);
          console.log('Current field data:', currentField);

          // Prepare update payload with all required fields
          const updatePayload = {
            id: parseInt(field.id, 10),
            component: currentField.component || { id: parseInt(componentId, 10) },
            fieldType: currentField.fieldType || { id: field.fieldTypeId || 1 },
            displayPreference: field.displayPreference,
            // Preserve additional information
            additionalInformation: currentField.additionalInformation
          };

          console.log('Sending update with payload:', updatePayload);
          return componentFieldsApi.update(field.id, updatePayload);
        } catch (error) {
          console.error(`Error updating field ${field.id}:`, error);
          throw error;
        }
      });

      await Promise.all(updatePromises);

      toast({
        title: 'Success',
        description: 'Display preferences updated successfully',
      });

      // Update the local state with normalized values
      setOrderedFields(normalizedFields);

      // Call the success callback if provided
      if (onSuccess) {
        onSuccess();
      }

      // Close the dialog after a short delay to show the success message
      setTimeout(() => {
        onClose();
      }, 500);
    } catch (error) {
      console.error('Error updating display preferences:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update display preferences',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] bg-background border-border">
        <DialogHeader>
          <DialogTitle>Edit Component Field Order</DialogTitle>
          <DialogDescription>
            Change the order of fields in the "{componentName}" component by adjusting their display preferences.
            Lower numbers appear first.
          </DialogDescription>
        </DialogHeader>

        <div className="max-h-[400px] overflow-y-auto">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin h-6 w-6 border-2 border-primary rounded-full border-t-transparent mr-2"></div>
              <span className="text-muted-foreground">Loading fields...</span>
            </div>
          ) : orderedFields.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <span className="text-muted-foreground">No fields found for this component</span>
            </div>
          ) : (
            <table className="w-full border-collapse">
              <thead className="bg-muted/30 sticky top-0">
                <tr>
                  <th className="text-left p-2 text-sm font-medium text-primary">Field Name</th>
                  <th className="text-left p-2 text-sm font-medium text-primary">Type</th>
                  <th className="text-center p-2 text-sm font-medium text-primary w-[120px]">Display Order</th>
                  <th className="text-right p-2 text-sm font-medium text-primary w-[100px]">Actions</th>
                </tr>
              </thead>
              <tbody>
                {orderedFields.map((field, index) => (
                  <tr
                    key={field.id}
                    className="border-b border-border hover:bg-muted/20"
                  >
                    <td className="p-2">
                      <div className="flex items-center">
                        <span>{field.name}</span>
                      </div>
                    </td>
                    <td className="p-2">{getFieldTypeDisplayName(field)}</td>
                    <td className="p-2">
                      <Input
                        type="number"
                        value={field.displayPreference}
                        onChange={(e) => updateDisplayPreference(index, e.target.value)}
                        className="h-8 w-20 mx-auto text-center"
                        min="1"
                        placeholder={(index + 1) * 10 + ''}
                      />
                    </td>
                    <td className="p-2 text-right">
                      <div className="flex items-center justify-end space-x-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => moveFieldUp(index)}
                          disabled={index === 0}
                          className="h-8 w-8"
                        >
                          <ArrowUp className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => moveFieldDown(index)}
                          disabled={index === orderedFields.length - 1}
                          className="h-8 w-8"
                        >
                          <ArrowDown className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSaving} className="border-border">
            <X className="mr-2 h-4 w-4" />
            Cancel
          </Button>
          <Button onClick={saveDisplayPreferences} disabled={isSaving || orderedFields.length === 0} className="bg-primary hover:bg-primary/90">
            {isSaving ? (
              <>
                <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

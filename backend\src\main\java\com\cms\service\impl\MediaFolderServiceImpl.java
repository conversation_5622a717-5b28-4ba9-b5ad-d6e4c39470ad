package com.cms.service.impl;

import com.cms.dto.MediaFolderDTO;
import com.cms.entity.MediaFolder;
import com.cms.entity.User;
import com.cms.exception.NullConstraintViolationException;
import com.cms.exception.ResourceNotFoundException;
import com.cms.exception.UniqueConstraintViolationException;
import com.cms.repository.MediaFolderRepository;
import com.cms.repository.MediaRepository;
import com.cms.repository.UserRepository;
import com.cms.service.MediaFolderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class MediaFolderServiceImpl implements MediaFolderService {

    private final MediaFolderRepository folderRepository;
    private final MediaRepository mediaRepository;
    private final UserRepository userRepository;

    @Override
    @Transactional
    public MediaFolderDTO createFolder(String folderName, String description, Integer parentId) {
        // Get current user (may be null for anonymous users)
        User currentUser = getCurrentUser();

        // Validate folder name
        if (!StringUtils.hasText(folderName)) {
            throw new NullConstraintViolationException("folderName");
        }

        // Get parent folder if provided
        MediaFolder parent = null;
        if (parentId != null) {
            parent = folderRepository.findById(parentId)
                    .orElseThrow(() -> new ResourceNotFoundException("Parent folder not found with id: " + parentId));

            // Check if folder with same name already exists in the parent folder
            if (folderRepository.existsByFolderNameAndParent(folderName, parent)) {
                throw new UniqueConstraintViolationException("folderName", folderName);
            }
        } else {
            // Check if root folder with same name already exists
            List<MediaFolder> existingFolders = folderRepository.findRootFoldersByName(folderName);
            if (!existingFolders.isEmpty()) {
                throw new UniqueConstraintViolationException("folderName", folderName);
            }
        }

        // Create folder entity
        MediaFolder folder = new MediaFolder();
        folder.setFolderName(folderName);
        folder.setDescription(description);
        folder.setParent(parent);
        folder.setUser(currentUser);

        // Save folder entity
        MediaFolder savedFolder = folderRepository.save(folder);

        // Return DTO
        return mapToDTO(savedFolder, true);
    }

    @Override
    @Transactional(readOnly = true)
    public MediaFolderDTO getFolderById(Integer id) {
        MediaFolder folder = folderRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Folder not found with id: " + id));

        return mapToDTO(folder, true);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MediaFolderDTO> getRootFolders() {
        return folderRepository.findByParentIsNull().stream()
                .map(folder -> mapToDTO(folder, false))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<MediaFolderDTO> getSubfolders(Integer parentId) {
        MediaFolder parent = folderRepository.findById(parentId)
                .orElseThrow(() -> new ResourceNotFoundException("Parent folder not found with id: " + parentId));

        return folderRepository.findByParent(parent).stream()
                .map(folder -> mapToDTO(folder, false))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public MediaFolderDTO updateFolder(Integer id, String folderName, String description, Integer parentId) {
        MediaFolder folder = folderRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Folder not found with id: " + id));

        // Update fields if provided
        if (StringUtils.hasText(folderName) && !folderName.equals(folder.getFolderName())) {
            // Check if folder with same name already exists
            MediaFolder parent = folder.getParent();
            if (parent != null) {
                if (folderRepository.existsByFolderNameAndParent(folderName, parent)) {
                    throw new UniqueConstraintViolationException("folderName", folderName);
                }
            } else {
                // Check if root folder with same name already exists
                List<MediaFolder> existingFolders = folderRepository.findRootFoldersByNameExcludingId(folderName, id);
                if (!existingFolders.isEmpty()) {
                    throw new UniqueConstraintViolationException("folderName", folderName);
                }
            }
            folder.setFolderName(folderName);
        }

        if (description != null) {
            folder.setDescription(description);
        }

        if (parentId != null) {
            // Prevent circular references
            if (parentId.equals(id)) {
                throw new IllegalArgumentException("A folder cannot be its own parent");
            }

            MediaFolder parent = folderRepository.findById(parentId)
                    .orElseThrow(() -> new ResourceNotFoundException("Parent folder not found with id: " + parentId));

            // Check if the new parent is a descendant of this folder
            MediaFolder current = parent;
            while (current != null) {
                if (current.getId().equals(id)) {
                    throw new IllegalArgumentException("Cannot move a folder to its own descendant");
                }
                current = current.getParent();
            }

            folder.setParent(parent);
        }

        MediaFolder updatedFolder = folderRepository.save(folder);

        return mapToDTO(updatedFolder, true);
    }

    @Override
    @Transactional
    public void deleteFolder(Integer id) {
        MediaFolder folder = folderRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Folder not found with id: " + id));

        // Delete the folder and all its contents (cascading delete will handle subfolders and media)
        folderRepository.delete(folder);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MediaFolderDTO> searchFolders(String query) {
        return folderRepository.searchByName(query).stream()
                .map(folder -> mapToDTO(folder, false))
                .collect(Collectors.toList());
    }

    private MediaFolderDTO mapToDTO(MediaFolder folder, boolean includeSubfolders) {
        MediaFolderDTO dto = new MediaFolderDTO();
        dto.setId(folder.getId());
        dto.setFolderName(folder.getFolderName());
        dto.setDescription(folder.getDescription());

        if (folder.getParent() != null) {
            dto.setParentId(folder.getParent().getId());
            dto.setParentName(folder.getParent().getFolderName());
        }

        // Include subfolders if requested
        if (includeSubfolders && folder.getSubfolders() != null) {
            dto.setSubfolders(folder.getSubfolders().stream()
                    .map(subfolder -> mapToDTO(subfolder, false))
                    .collect(Collectors.toList()));
        }

        // Count media in this folder
        dto.setMediaCount(mediaRepository.countByFolder(folder));

        if (folder.getUser() != null) {
            dto.setCreatedByUsername(folder.getUser().getUsername());
        }

        dto.setCreatedAt(folder.getCreatedAt());
        dto.setModifiedAt(folder.getModifiedAt());

        return dto;
    }

    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        // Check if the user is anonymous
        if (authentication == null || "anonymousUser".equals(authentication.getName())) {
            // For anonymous users, try to find a default system user
            return userRepository.findByUsername("admin")
                    .orElse(null); // Return null if no admin user exists
        }

        String username = authentication.getName();

        return userRepository.findByUsername(username)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with username: " + username));
    }
}

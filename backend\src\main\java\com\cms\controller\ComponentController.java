package com.cms.controller;

import com.cms.dto.ComponentCreateDTO;
import com.cms.dto.ComponentWithChildrenDTO;
import com.cms.entity.ComponentListing;
import com.cms.exception.UniqueConstraintViolationException;
import com.cms.mapper.ComponentWithChildrenMapper;
import com.cms.service.ComponentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/component-management")
@RequiredArgsConstructor
@Tag(name = "Component Management", description = "Component Management API")
public class ComponentController {

    private final ComponentService componentService;
    private final ComponentWithChildrenMapper componentWithChildrenMapper;

    @GetMapping("/getAll")
    @Operation(summary = "Get all components", description = "Returns a list of all components with their child components")
    public ResponseEntity<List<ComponentWithChildrenDTO>> getAllComponents() {
        List<ComponentListing> components = componentService.getAllComponents();
        List<ComponentWithChildrenDTO> componentDTOs = componentWithChildrenMapper.toDTOList(components);
        return ResponseEntity.ok(componentDTOs);
    }

    @GetMapping("/getAllActive")
    @Operation(summary = "Get all active components", description = "Returns a list of all active components with their child components")
    public ResponseEntity<List<ComponentWithChildrenDTO>> getAllActiveComponents() {
        List<ComponentListing> components = componentService.getAllActiveComponents();
        List<ComponentWithChildrenDTO> componentDTOs = componentWithChildrenMapper.toDTOList(components);
        return ResponseEntity.ok(componentDTOs);
    }

    @GetMapping("/getById/{id}")
    @Operation(summary = "Get component by ID", description = "Returns a component by its ID with child components or 204 No Content if not found")
    public ResponseEntity<?> getComponentById(@PathVariable Integer id) {
        Optional<ComponentListing> componentOpt = componentService.getComponentById(id);
        if (componentOpt.isPresent()) {
            ComponentWithChildrenDTO componentDTO = componentWithChildrenMapper.toDTO(componentOpt.get());
            return ResponseEntity.ok(componentDTO);
        } else {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/getByIdWithDetails/{id}")
    @Operation(summary = "Get component details by ID", description = "Returns a component with all its fields, configurations, and child components or 204 No Content if not found")
    public ResponseEntity<?> getComponentDetailsById(@PathVariable Integer id) {
        Optional<ComponentListing> componentOpt = componentService.getComponentDetailsById(id);
        if (componentOpt.isPresent()) {
            ComponentWithChildrenDTO componentDTO = componentWithChildrenMapper.toDTO(componentOpt.get());
            return ResponseEntity.ok(componentDTO);
        } else {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/getByApiId/{apiId}")
    @Operation(summary = "Get component by API ID", description = "Returns a component by its API ID with child components or 204 No Content if not found")
    public ResponseEntity<?> getComponentByApiId(@PathVariable String apiId) {
        Optional<ComponentListing> componentOpt = componentService.getComponentByApiId(apiId);
        if (componentOpt.isPresent()) {
            ComponentWithChildrenDTO componentDTO = componentWithChildrenMapper.toDTO(componentOpt.get());
            return ResponseEntity.ok(componentDTO);
        } else {
            return ResponseEntity.noContent().build();
        }
    }

    @PostMapping("/create")
    @Operation(
        summary = "Create a new component",
        description = "Creates a new component without fields",
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "Component object to be created",
            content = @Content(
                mediaType = "application/json",
                examples = {
                    @ExampleObject(
                        name = "Standard Component",
                        summary = "Standard component creation example",
                        value = "{\"componentName\":\"Example Component\",\"componentDisplayName\":\"Example Component Display\",\"componentApiId\":\"example_component\",\"isActive\":true}"
                    )
                },
                schema = @Schema(implementation = ComponentCreateDTO.class)
            )
        ),
        responses = {
            @ApiResponse(
                responseCode = "201",
                description = "Component created successfully",
                content = @Content(mediaType = "application/json")
            ),
            @ApiResponse(
                responseCode = "400",
                description = "Invalid input or component name already exists",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<ComponentListing> createComponent(@Valid @RequestBody ComponentCreateDTO componentDTO) {
        // Check if component name already exists
        if (componentService.existsByComponentName(componentDTO.getComponentName())) {
            throw new UniqueConstraintViolationException("componentName", componentDTO.getComponentName());
        }

        // Convert DTO to entity and save
        ComponentListing component = componentDTO.toEntity();
        return new ResponseEntity<>(componentService.createComponent(component), HttpStatus.CREATED);
    }

    @PutMapping("/update/{id}")
    @Operation(summary = "Update a component", description = "Updates an existing component")
    public ResponseEntity<ComponentListing> updateComponent(@PathVariable Integer id, @Valid @RequestBody ComponentListing component) {
        return ResponseEntity.ok(componentService.updateComponent(id, component));
    }

    @DeleteMapping("/deleteById/{id}")
    @Operation(summary = "Delete a component", description = "Deletes a component by its ID")
    public ResponseEntity<Void> deleteComponent(@PathVariable Integer id) {
        componentService.deleteComponent(id);
        return ResponseEntity.noContent().build();
    }
}

package com.cms.dto;

import com.cms.entity.ComponentComponent;
import com.cms.entity.ComponentListing;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class ComponentComponentDTO {
    private Integer id;
    private ComponentListing parentComponent;
    private ComponentListing childComponent;
    private Boolean isRepeatable;
    private Integer displayPreference;
    private Boolean isActive;
    private String additionalInformation;

    // Additional fields not in the entity
    private String displayName;
    private String description;
    private Integer sortOrder;
    private Boolean isRequired;
    private Boolean isVisible;
    private Map<String, String> validationRules;

    public ComponentComponentDTO(ComponentComponent entity) {
        this.id = entity.getId();
        this.parentComponent = entity.getParentComponent();
        this.childComponent = entity.getChildComponent();
        this.isRepeatable = entity.getIsRepeatable();
        this.displayPreference = entity.getDisplayPreference();
        this.isActive = entity.getIsActive();
        this.additionalInformation = entity.getAdditionalInformation();

        // Set default values for additional fields
        this.displayName = entity.getChildComponent() != null ? entity.getChildComponent().getComponentDisplayName() : "";
        this.description = "";
        this.sortOrder = entity.getDisplayPreference();
        this.isRequired = false;
        this.isVisible = true;
        this.validationRules = new HashMap<>();
    }

    public ComponentComponent toEntity() {
        ComponentComponent entity = new ComponentComponent();
        entity.setId(this.id);
        entity.setParentComponent(this.parentComponent);
        entity.setChildComponent(this.childComponent);
        entity.setIsRepeatable(this.isRepeatable);
        entity.setDisplayPreference(this.displayPreference);
        entity.setIsActive(this.isActive);
        entity.setAdditionalInformation(this.additionalInformation);
        return entity;
    }
}

package com.cms.mapper;

import com.cms.dto.*;
import com.cms.entity.*;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

@Component
public class CollectionMapper {

    public CollectionDTO toDTO(CollectionListing entity) {
        if (entity == null) {
            return null;
        }

        CollectionDTO dto = new CollectionDTO();
        dto.setId(entity.getId());
        dto.setCollectionName(entity.getCollectionName());
        dto.setCollectionDesc(entity.getCollectionDesc());
        dto.setAdditionalInformation(entity.getAdditionalInformation());
        dto.setDisclaimerText(entity.getDisclaimerText());
        dto.setCollectionApiId(entity.getCollectionApiId());


        if (entity.getComponents() != null) {
            dto.setComponents(entity.getComponents().stream()
                    .map(this::toComponentDTO)
                    .collect(Collectors.toList()));
        }

        return dto;
    }

    public List<CollectionDTO> toDTOList(List<CollectionListing> entities) {
        if (entities == null) {
            return null;
        }

        return entities.stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }

    private ComponentDTO toComponentDTO(CollectionComponent entity) {
        if (entity == null) {
            return null;
        }

        ComponentDTO dto = new ComponentDTO();
        dto.setId(entity.getId());
        dto.setIsRepeatable(entity.getIsRepeatable());
        dto.setMinRepeatOccurrences(entity.getMinRepeatOccurrences());
        dto.setMaxRepeatOccurrences(entity.getMaxRepeatOccurrences());

        if (entity.getComponent() != null) {
            ComponentListing component = entity.getComponent();

            dto.setComponent(new ComponentDetailsDTO());
            dto.getComponent().setId(component.getId());
            dto.getComponent().setComponentName(component.getComponentName());
            dto.getComponent().setComponentDisplayName(component.getComponentDisplayName());
            dto.getComponent().setComponentApiId(component.getComponentApiId());
            dto.getComponent().setIsActive(component.getIsActive());
            dto.getComponent().setGetUrl(component.getGetUrl());
            dto.getComponent().setPostUrl(component.getPostUrl());
            dto.getComponent().setUpdateUrl(component.getUpdateUrl());



            if (component.getFields() != null) {
                dto.getComponent().setFields(component.getFields().stream()
                        .map(this::toFieldDTO)
                        .collect(Collectors.toList()));
            }
        }

        return dto;
    }

    private FieldDTO toFieldDTO(ComponentField field) {
        FieldDTO dto = new FieldDTO();
        dto.setId(field.getId());
        dto.setDisplayPreference(field.getDisplayPreference());

        FieldTypeDTO fieldTypeDTO = new FieldTypeDTO();
        fieldTypeDTO.setId(field.getFieldType().getId());
        fieldTypeDTO.setFieldTypeName(field.getFieldType().getFieldTypeName());
        fieldTypeDTO.setFieldTypeDesc(field.getFieldType().getFieldTypeDesc());
        fieldTypeDTO.setDisplayName(field.getFieldType().getDisplayName());
        fieldTypeDTO.setHelpText(field.getFieldType().getHelpText());
        dto.setFieldType(fieldTypeDTO);

        if (field.getConfigs() != null) {
            List<FieldConfigDTO> configDTOs = new ArrayList<>();

            field.getConfigs().forEach(config -> {
                FieldConfigDTO configDTO = new FieldConfigDTO();
                configDTO.setId(config.getId());
                configDTO.setFieldConfigValue(config.getFieldConfigValue());

                if (config.getFieldConfig() != null) {
                    configDTO.setConfigName(config.getFieldConfig().getConfigName());
                    configDTO.setValueType(config.getFieldConfig().getValueType());

                    if (config.getFieldConfig().getConfigType() != null) {
                        ConfigTypeDTO configTypeDTO = new ConfigTypeDTO();
                        configTypeDTO.setId(config.getFieldConfig().getConfigType().getId());
                        configTypeDTO.setConfigTypeName(config.getFieldConfig().getConfigType().getConfigTypeName());
                        configTypeDTO.setConfigTypeDesc(config.getFieldConfig().getConfigType().getConfigTypeDesc());
                        configTypeDTO.setDisplayName(config.getFieldConfig().getConfigType().getDisplayName());
                        configTypeDTO.setAdditionalInfo(config.getFieldConfig().getConfigType().getAdditionalInfo());
                        configTypeDTO.setDisclaimerText(config.getFieldConfig().getConfigType().getDisclaimerText());
                        configTypeDTO.setPlaceholderText(config.getFieldConfig().getConfigType().getPlaceholderText());
                        configDTO.setConfigType(configTypeDTO);
                    }
                }

                configDTOs.add(configDTO);
            });

            dto.setConfigs(configDTOs);
        }

        return dto;
    }
}


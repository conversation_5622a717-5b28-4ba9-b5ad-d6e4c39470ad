package com.cms.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

/**
 * Utility class for network-related operations
 */
@Component
public class NetworkUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(NetworkUtil.class);
    
    /**
     * Gets the IP address of the current machine.
     * Tries to find a non-loopback, IPv4 address.
     * 
     * @return The IP address as a string, or "localhost" if no suitable address is found
     */
    public String getHostIpAddress() {
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();
                // Skip loopback, inactive, or virtual interfaces
                if (networkInterface.isLoopback() || !networkInterface.isUp() || networkInterface.isVirtual()) {
                    continue;
                }
                
                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();
                    // Only consider IPv4 addresses that are not loopback
                    if (!address.isLoopbackAddress() && address.getHostAddress().indexOf(':') == -1) {
                        String ipAddress = address.getHostAddress();
                        logger.info("Detected system IP address: {}", ipAddress);
                        return ipAddress;
                    }
                }
            }
        } catch (SocketException e) {
            logger.error("Error getting host IP address", e);
        }
        
        logger.warn("Could not determine host IP address, using localhost");
        return "localhost";
    }
}

package com.cms.entity;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIdentityReference;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "collection_field_config")
@Getter
@Setter
@ToString(exclude = "collectionField")
@NoArgsConstructor
@AllArgsConstructor
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
public class CollectionFieldConfig extends Auditable {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "cms_collection_field_config_seq")
    @SequenceGenerator(name = "cms_collection_field_config_seq", sequenceName = "cms_collection_field_config_seq", initialValue = 100, allocationSize = 1)
    private Integer id;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "collection_field_id")
    @JsonIdentityReference(alwaysAsId = false)
    private CollectionField collectionField;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "field_config_id")
    private FieldConfig fieldConfig;

    @Column(name = "field_config_value")
    private String fieldConfigValue;
}

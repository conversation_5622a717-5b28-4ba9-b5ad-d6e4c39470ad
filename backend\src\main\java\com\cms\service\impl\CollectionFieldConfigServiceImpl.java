package com.cms.service.impl;

import com.cms.entity.CollectionFieldConfig;
import com.cms.exception.ResourceNotFoundException;
import com.cms.repository.CollectionFieldConfigRepository;
import com.cms.repository.CollectionFieldRepository;
import com.cms.repository.FieldConfigRepository;
import com.cms.service.CollectionFieldConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class CollectionFieldConfigServiceImpl implements CollectionFieldConfigService {

    private final CollectionFieldConfigRepository collectionFieldConfigRepository;

    @Override
    public List<CollectionFieldConfig> getAllCollectionFieldConfigs() {
        return collectionFieldConfigRepository.findAll();
    }

    @Override
    public List<CollectionFieldConfig> getCollectionFieldConfigsByCollectionFieldId(Integer collectionFieldId) {
        return collectionFieldConfigRepository.findByCollectionFieldId(collectionFieldId);
    }

    @Override
    public Optional<CollectionFieldConfig> getCollectionFieldConfigById(Integer id) {
        return collectionFieldConfigRepository.findById(id);
    }

    @Override
    public CollectionFieldConfig createCollectionFieldConfig(CollectionFieldConfig collectionFieldConfig) {
        log.info("Creating collection field config for field ID: {}, config ID: {}, value: {}",
            collectionFieldConfig.getCollectionField() != null ? collectionFieldConfig.getCollectionField().getId() : "NULL",
            collectionFieldConfig.getFieldConfig() != null ? collectionFieldConfig.getFieldConfig().getId() : "NULL",
            collectionFieldConfig.getFieldConfigValue());

        // Create a new config object to avoid transient entity issues
        CollectionFieldConfig newConfig = new CollectionFieldConfig();

        // Set the collection field by loading it from the repository
        if (collectionFieldConfig.getCollectionField() != null && collectionFieldConfig.getCollectionField().getId() != null) {
            collectionFieldRepository.findById(collectionFieldConfig.getCollectionField().getId())
                .ifPresentOrElse(
                    field -> {
                        newConfig.setCollectionField(field);
                        log.debug("Set collection field with ID: {}", field.getId());
                    },
                    () -> log.error("Collection field with ID {} not found", collectionFieldConfig.getCollectionField().getId())
                );
        }

        // Set the field config by loading it from the repository
        if (collectionFieldConfig.getFieldConfig() != null && collectionFieldConfig.getFieldConfig().getId() != null) {
            fieldConfigRepository.findById(collectionFieldConfig.getFieldConfig().getId())
                .ifPresentOrElse(
                    config -> {
                        newConfig.setFieldConfig(config);
                        log.debug("Set field config with ID: {}", config.getId());
                    },
                    () -> log.error("Field config with ID {} not found", collectionFieldConfig.getFieldConfig().getId())
                );
        }

        // Copy the value
        newConfig.setFieldConfigValue(collectionFieldConfig.getFieldConfigValue());

        // Save the new config
        CollectionFieldConfig savedConfig = collectionFieldConfigRepository.save(newConfig);
        log.info("Successfully saved collection field config with ID: {}", savedConfig.getId());
        return savedConfig;
    }

    @Autowired
    private FieldConfigRepository fieldConfigRepository;

    @Autowired
    private CollectionFieldRepository collectionFieldRepository;

    @Override
    public List<CollectionFieldConfig> createCollectionFieldConfigs(List<CollectionFieldConfig> collectionFieldConfigs) {
        log.info("Creating {} collection field configs", collectionFieldConfigs.size());
        List<CollectionFieldConfig> savedConfigs = new ArrayList<>();

        for (int i = 0; i < collectionFieldConfigs.size(); i++) {
            CollectionFieldConfig config = collectionFieldConfigs.get(i);
            log.info("Processing config {}: Collection Field ID = {}, Field Config ID = {}, Value = {}",
                i,
                config.getCollectionField() != null ? config.getCollectionField().getId() : "NULL",
                config.getFieldConfig() != null ? config.getFieldConfig().getId() : "NULL",
                config.getFieldConfigValue());

            try {
                // Create a new config object to avoid transient entity issues
                CollectionFieldConfig newConfig = new CollectionFieldConfig();

                // Set the collection field by loading it from the repository
                if (config.getCollectionField() != null && config.getCollectionField().getId() != null) {
                    collectionFieldRepository.findById(config.getCollectionField().getId())
                        .ifPresentOrElse(
                            field -> {
                                newConfig.setCollectionField(field);
                                log.debug("Set collection field with ID: {}", field.getId());
                            },
                            () -> {
                                log.error("Collection field with ID {} not found", config.getCollectionField().getId());
                                throw new RuntimeException("Collection field with ID " + config.getCollectionField().getId() + " not found");
                            }
                        );
                } else {
                    log.error("Collection field is null or has null ID for config {}", i);
                    throw new RuntimeException("Collection field is null or has null ID");
                }

                // Set the field config by loading it from the repository
                if (config.getFieldConfig() != null && config.getFieldConfig().getId() != null) {
                    fieldConfigRepository.findById(config.getFieldConfig().getId())
                        .ifPresentOrElse(
                            fieldConfig -> {
                                newConfig.setFieldConfig(fieldConfig);
                                log.debug("Set field config with ID: {}", fieldConfig.getId());
                            },
                            () -> {
                                log.error("Field config with ID {} not found", config.getFieldConfig().getId());
                                throw new RuntimeException("Field config with ID " + config.getFieldConfig().getId() + " not found");
                            }
                        );
                } else {
                    log.error("Field config is null or has null ID for config {}", i);
                    throw new RuntimeException("Field config is null or has null ID");
                }

                // Copy the value
                newConfig.setFieldConfigValue(config.getFieldConfigValue());

                // Save the new config
                CollectionFieldConfig savedConfig = collectionFieldConfigRepository.save(newConfig);
                savedConfigs.add(savedConfig);
                log.info("Successfully saved collection field config {} with ID: {}", i, savedConfig.getId());

            } catch (Exception e) {
                log.error("Error saving field config {}: {}", i, e.getMessage(), e);
                // Re-throw the exception to fail the entire operation
                throw new RuntimeException("Failed to save collection field config " + i + ": " + e.getMessage(), e);
            }
        }

        log.info("Successfully created {} collection field configs", savedConfigs.size());
        return savedConfigs;
    }

    @Override
    public CollectionFieldConfig updateCollectionFieldConfig(Integer id, CollectionFieldConfig collectionFieldConfig) {
        CollectionFieldConfig existingConfig = collectionFieldConfigRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("CollectionFieldConfig not found with id: " + id));

        existingConfig.setCollectionField(collectionFieldConfig.getCollectionField());
        existingConfig.setFieldConfig(collectionFieldConfig.getFieldConfig());
        existingConfig.setFieldConfigValue(collectionFieldConfig.getFieldConfigValue());

        return collectionFieldConfigRepository.save(existingConfig);
    }

    @Override
    public void deleteCollectionFieldConfig(Integer id) {
        CollectionFieldConfig config = collectionFieldConfigRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("CollectionFieldConfig not found with id: " + id));
        collectionFieldConfigRepository.delete(config);
    }
}

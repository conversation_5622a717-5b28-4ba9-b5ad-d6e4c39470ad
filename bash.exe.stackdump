Stack trace:
Frame         Function      Args
0007FFFF9DC0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8CC0) msys-2.0.dll+0x1FE8E
0007FFFF9DC0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA098) msys-2.0.dll+0x67F9
0007FFFF9DC0  000210046832 (000210286019, 0007FFFF9C78, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9DC0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9DC0  000210068E24 (0007FFFF9DD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA0A0  00021006A225 (0007FFFF9DD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE3A640000 ntdll.dll
7FFE38C30000 KERNEL32.DLL
7FFE37A30000 KERNELBASE.dll
7FFE38520000 USER32.dll
7FFE37950000 win32u.dll
000210040000 msys-2.0.dll
7FFE39A20000 GDI32.dll
7FFE37EA0000 gdi32full.dll
7FFE37980000 msvcp_win.dll
7FFE380A0000 ucrtbase.dll
7FFE3A420000 advapi32.dll
7FFE3A4E0000 msvcrt.dll
7FFE3A1C0000 sechost.dll
7FFE38400000 RPCRT4.dll
7FFE36EE0000 CRYPTBASE.DLL
7FFE37E00000 bcryptPrimitives.dll
7FFE3A270000 IMM32.DLL

package com.cms.service;

import com.cms.entity.Category;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for Category entity operations
 */
public interface CategoryService {
    /**
     * Get all categories
     * @return List of all categories
     */
    List<Category> getAllCategories();

    /**
     * Get all categories for a specific client
     * @param clientId Client ID
     * @return List of categories for the client
     */
    List<Category> getCategoriesByClientId(Integer clientId);

    /**
     * Get category by ID
     * @param id Category ID
     * @return Optional containing the category if found
     */
    Optional<Category> getCategoryById(Integer id);

    /**
     * Get category by name
     * @param categoryName Category name
     * @return Optional containing the category if found
     */
    Optional<Category> getCategoryByName(String categoryName);

    /**
     * Create a new category
     * @param category Category to create
     * @return Created category
     */
    Category createCategory(Category category);

    /**
     * Create a new category for a specific client
     * @param category Category to create
     * @param clientId Client ID
     * @return Created category
     */
    Category createCategoryForClient(Category category, Integer clientId);

    /**
     * Update an existing category
     * @param id Category ID
     * @param category Updated category data
     * @return Updated category
     */
    Category updateCategory(Integer id, Category category);

    /**
     * Update an existing category with client information
     * @param id Category ID
     * @param category Updated category data
     * @param clientId Client ID
     * @return Updated category
     */
    Category updateCategoryWithClient(Integer id, Category category, Integer clientId);

    /**
     * Delete a category
     * @param id Category ID
     */
    void deleteCategory(Integer id);

    /**
     * Check if a category with the given name exists
     * @param categoryName Category name
     * @return true if a category with the name exists, false otherwise
     */
    boolean existsByCategoryName(String categoryName);



    /**
     * Get the next available ID for a new category
     * @return Next available ID
     */
    Integer getNextAvailableId();

    List<Category> getCategoriesByParentId(Integer parentId);

    /**
     * Get child categories by parent category ID and client ID
     * @param parentId Parent category ID
     * @param clientId Client ID
     * @return List of child categories
     */
    List<Category> getCategoriesByParentIdAndClientId(Integer parentId, Integer clientId);

}

package com.cms.config;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class CustomCorsFilter implements Filter {

    @Value("${cors.allowed-origins:*}")
    private String allowedOrigins;

    @Value("${cors.allowed-methods:GET,POST,PUT,DELETE,OPTIONS,HEAD,PATCH}")
    private String allowedMethods;

    @Value("${cors.allowed-headers:*}")
    private String allowedHeaders;

    @Value("${cors.max-age:3600}")
    private String maxAge;

    @Value("${cors.allow-credentials:false}")
    private boolean allowCredentials;

    @Override
    public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain)
            throws IOEx<PERSON>, ServletException {
        HttpServletResponse response = (HttpServletResponse) res;
        HttpServletRequest request = (HttpServletRequest) req;

        // Skip media endpoints - they are handled by MediaUploadCorsFilter
        String requestURI = request.getRequestURI();
        if (requestURI.contains("/media/")) {
            // Let the MediaUploadCorsFilter handle this
            chain.doFilter(req, res);
            return;
        }

        // Get the origin from the request
        String origin = request.getHeader("Origin");
        System.out.println("Request from origin: " + origin);
        System.out.println("Allowed origins: " + allowedOrigins);

        // Set CORS headers based on configuration
        if (origin != null) {
            if ("*".equals(allowedOrigins)) {
                // For wildcard origins, we must set the specific origin
                // but we can't use credentials=true
                System.out.println("Using wildcard configuration, setting specific origin: " + origin);
                response.setHeader("Access-Control-Allow-Origin", origin);
                response.setHeader("Access-Control-Allow-Credentials", "false");
            } else {
                // Check if the origin is in our allowed origins list
                boolean originAllowed = false;
                String[] origins = allowedOrigins.split(",");
                for (String allowedOrigin : origins) {
                    if (allowedOrigin.trim().equals(origin)) {
                        originAllowed = true;
                        break;
                    }
                }

                if (originAllowed) {
                    System.out.println("Origin in allowed list, setting: " + origin);
                    response.setHeader("Access-Control-Allow-Origin", origin);
                } else {
                    // For development, allow any origin
                    System.out.println("Origin not in allowed list, but allowing for development: " + origin);
                    response.setHeader("Access-Control-Allow-Origin", origin);
                }
            }
        } else {
            System.out.println("No Origin header in request");
            // For preflight requests without origin
            response.setHeader("Access-Control-Allow-Origin", "*");
        }

        response.setHeader("Access-Control-Allow-Methods", allowedMethods);
        response.setHeader("Access-Control-Allow-Headers", allowedHeaders);
        response.setHeader("Access-Control-Max-Age", maxAge);
        response.setHeader("Access-Control-Allow-Credentials", String.valueOf(allowCredentials));

        System.out.println("CORS headers set:");
        System.out.println("Access-Control-Allow-Origin: " + response.getHeader("Access-Control-Allow-Origin"));
        System.out.println("Access-Control-Allow-Methods: " + allowedMethods);
        System.out.println("Access-Control-Allow-Headers: " + allowedHeaders);
        System.out.println("Access-Control-Allow-Credentials: " + allowCredentials);
        System.out.println("Access-Control-Max-Age: " + maxAge);

        // Handle preflight requests
        if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
            response.setStatus(HttpServletResponse.SC_OK);
        } else {
            chain.doFilter(req, res);
        }
    }

    @Override
    public void init(FilterConfig filterConfig) {
    }

    @Override
    public void destroy() {
    }
}

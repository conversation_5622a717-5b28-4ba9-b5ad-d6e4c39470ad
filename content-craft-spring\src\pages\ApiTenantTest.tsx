import React, { useState } from 'react';
import { useAuthStore } from '@/lib/store';
import { 
  authApi, 
  collectionsApi, 
  clientsApi, 
  categoriesApi, 
  componentsApi,
  contentEntriesApi,
  mediaApi
} from '@/lib/api';
import { getTenantFromToken, getUsernameFromToken } from '@/lib/jwt';

export default function ApiTenantTest() {
  const { user, token, isAuthenticated } = useAuthStore();
  const [testResults, setTestResults] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);

  const updateResult = (testName: string, result: any) => {
    setTestResults(prev => ({
      ...prev,
      [testName]: result
    }));
  };

  const testApi = async (testName: string, apiCall: () => Promise<any>) => {
    try {
      updateResult(testName, { status: 'loading', message: 'Testing...' });
      const response = await apiCall();
      updateResult(testName, { 
        status: 'success', 
        message: 'Success',
        data: response.data,
        dataCount: Array.isArray(response.data) ? response.data.length : 'N/A'
      });
    } catch (error: any) {
      updateResult(testName, { 
        status: 'error', 
        message: error.message,
        details: error.response?.data || error.response || 'No additional details'
      });
    }
  };

  const runAllTests = async () => {
    setIsLoading(true);
    setTestResults({});

    // Test all major API endpoints
    await testApi('Collections - Get All', () => collectionsApi.getAll());
    await testApi('Collections - Get Details', () => collectionsApi.getDetails());
    await testApi('Clients - Get All', () => clientsApi.getAll());
    await testApi('Categories - Get All', () => categoriesApi.getAll());
    await testApi('Components - Get All', () => componentsApi.getAll());
    await testApi('Components - Get Active', () => componentsApi.getActive());
    await testApi('Content Entries - Get All', () => contentEntriesApi.getAll());
    await testApi('Media Assets - Get All', () => mediaApi.getAllAssets());
    await testApi('Media Folders - Get All', () => mediaApi.getAllFolders());

    setIsLoading(false);
  };

  const clearResults = () => {
    setTestResults({});
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-50';
      case 'error': return 'text-red-600 bg-red-50';
      case 'loading': return 'text-blue-600 bg-blue-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const tokenInfo = token ? {
    username: getUsernameFromToken(token),
    tenant: getTenantFromToken(token),
    isValid: token.includes('.') && token.split('.').length === 3
  } : null;

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">API Tenant Test Dashboard</h1>
      
      {/* Auth Status */}
      <div className="bg-white border rounded-lg p-4 mb-6">
        <h2 className="text-lg font-semibold mb-3">Authentication Status</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
          <div>
            <strong>Authenticated:</strong> {isAuthenticated ? '✅ Yes' : '❌ No'}
          </div>
          <div>
            <strong>User:</strong> {user?.username || 'None'}
          </div>
          <div>
            <strong>User Tenant:</strong> {user?.tenant || 'None'}
          </div>
          <div>
            <strong>Token:</strong> {token ? '✅ Present' : '❌ Missing'}
          </div>
          {tokenInfo && (
            <>
              <div>
                <strong>Token Username:</strong> {tokenInfo.username || 'None'}
              </div>
              <div>
                <strong>Token Tenant:</strong> {tokenInfo.tenant || 'None'}
              </div>
              <div>
                <strong>Token Valid:</strong> {tokenInfo.isValid ? '✅ Yes' : '❌ No'}
              </div>
            </>
          )}
        </div>
      </div>

      {/* Test Controls */}
      <div className="bg-white border rounded-lg p-4 mb-6">
        <h2 className="text-lg font-semibold mb-3">Test Controls</h2>
        <div className="space-x-4">
          <button
            onClick={runAllTests}
            disabled={!isAuthenticated || isLoading}
            className="px-4 py-2 bg-blue-500 text-white rounded disabled:bg-gray-300"
          >
            {isLoading ? 'Running Tests...' : 'Run All API Tests'}
          </button>
          <button
            onClick={clearResults}
            className="px-4 py-2 bg-gray-500 text-white rounded"
          >
            Clear Results
          </button>
        </div>
        {!isAuthenticated && (
          <p className="mt-2 text-red-600 text-sm">
            Please login first to run API tests
          </p>
        )}
      </div>

      {/* Test Results */}
      {Object.keys(testResults).length > 0 && (
        <div className="bg-white border rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-3">Test Results</h2>
          <div className="space-y-3">
            {Object.entries(testResults).map(([testName, result]: [string, any]) => (
              <div key={testName} className="border rounded p-3">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium">{testName}</h3>
                  <span className={`px-2 py-1 rounded text-xs ${getStatusColor(result.status)}`}>
                    {result.status.toUpperCase()}
                  </span>
                </div>
                <div className="text-sm space-y-1">
                  <div><strong>Message:</strong> {result.message}</div>
                  {result.dataCount !== undefined && (
                    <div><strong>Data Count:</strong> {result.dataCount}</div>
                  )}
                  {result.status === 'error' && result.details && (
                    <div className="mt-2">
                      <strong>Error Details:</strong>
                      <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-auto">
                        {typeof result.details === 'object' 
                          ? JSON.stringify(result.details, null, 2)
                          : result.details
                        }
                      </pre>
                    </div>
                  )}
                  {result.status === 'success' && result.data && (
                    <details className="mt-2">
                      <summary className="cursor-pointer font-medium">View Response Data</summary>
                      <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-40">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 className="font-semibold mb-2">How to Use:</h3>
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>Make sure you're logged in with a valid token</li>
          <li>Click "Run All API Tests" to test all major endpoints</li>
          <li>Check the results to see which APIs are working correctly</li>
          <li>Look for tenant-related errors in the error details</li>
          <li>Successful tests should return data from your tenant</li>
        </ol>
        <p className="mt-2 text-sm text-gray-600">
          This tool helps identify tenant context issues by testing all major API endpoints
          and showing detailed error information.
        </p>
      </div>
    </div>
  );
}

package com.cms.repository;

import com.cms.entity.CollectionField;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CollectionFieldRepository extends JpaRepository<CollectionField, Integer> {
    List<CollectionField> findByCollectionId(Integer collectionId);

    List<CollectionField> findByCollectionIdOrderByDisplayPreference(Integer collectionId);

    @Query("SELECT MAX(cf.id) FROM CollectionField cf")
    Integer findMaxId();

    @Query("SELECT MAX(cf.displayPreference) FROM CollectionField cf WHERE cf.collection.id = :collectionId")
    Integer findMaxDisplayPreferenceByCollectionId(@Param("collectionId") Integer collectionId);

    @Query("SELECT cf FROM CollectionField cf WHERE cf.additionalInformation LIKE %:searchTerm%")
    List<CollectionField> findByAdditionalInformationContaining(@Param("searchTerm") String searchTerm);

    @Query("SELECT cf FROM CollectionField cf WHERE cf.collection.id = :collectionId AND cf.additionalInformation LIKE %:searchTerm%")
    List<CollectionField> findByCollectionIdAndAdditionalInformationContaining(
            @Param("collectionId") Integer collectionId,
            @Param("searchTerm") String searchTerm);
}

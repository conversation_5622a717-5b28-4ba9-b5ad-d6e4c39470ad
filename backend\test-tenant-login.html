<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tenant Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .card {
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .response {
            margin-top: 10px;
            min-height: 100px;
        }
    </style>
</head>
<body>
    <h1>Tenant Login Test</h1>
    
    <div class="container">
        <div class="card">
            <h2>Login</h2>
            <div class="form-group">
                <label for="login-username">Username (with tenant):</label>
                <input type="text" id="login-username" placeholder="username@tenant">
            </div>
            <div class="form-group">
                <label for="login-password">Password:</label>
                <input type="password" id="login-password">
            </div>
            <button onclick="login()">Login</button>
            <div class="response">
                <h3>Response:</h3>
                <pre id="login-response"></pre>
            </div>
        </div>

        <div class="card">
            <h2>Register</h2>
            <div class="form-group">
                <label for="register-username">Username (with tenant):</label>
                <input type="text" id="register-username" placeholder="username@tenant">
            </div>
            <div class="form-group">
                <label for="register-email">Email:</label>
                <input type="text" id="register-email">
            </div>
            <div class="form-group">
                <label for="register-password">Password:</label>
                <input type="password" id="register-password">
            </div>
            <button onclick="register()">Register</button>
            <div class="response">
                <h3>Response:</h3>
                <pre id="register-response"></pre>
            </div>
        </div>

        <div class="card">
            <h2>Test API Call with Token</h2>
            <div class="form-group">
                <label for="token">JWT Token:</label>
                <input type="text" id="token" placeholder="Paste your JWT token here">
            </div>
            <div class="form-group">
                <label for="tenant-header">X-TenantID Header (optional):</label>
                <input type="text" id="tenant-header" placeholder="Override tenant from token">
            </div>
            <button onclick="testApiCall()">Test API Call</button>
            <div class="response">
                <h3>Response:</h3>
                <pre id="api-response"></pre>
            </div>
        </div>
    </div>

    <script>
        // API base URL
        const API_URL = 'http://localhost:8071/api';
        let token = '';

        async function login() {
            const username = document.getElementById('login-username').value;
            const password = document.getElementById('login-password').value;

            try {
                const response = await fetch(`${API_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                document.getElementById('login-response').textContent = JSON.stringify(data, null, 2);
                console.log('Login response:', data);

                if (data.accessToken) {
                    token = data.accessToken;
                    console.log('Token saved:', token);
                    document.getElementById('token').value = token;
                }
            } catch (error) {
                document.getElementById('login-response').textContent = `Error: ${error.message}`;
                console.error('Login error:', error);
            }
        }

        async function register() {
            const username = document.getElementById('register-username').value;
            const email = document.getElementById('register-email').value;
            const password = document.getElementById('register-password').value;

            try {
                const response = await fetch(`${API_URL}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, email, password })
                });

                const data = await response.text();
                document.getElementById('register-response').textContent = data;
                console.log('Register response:', data);
            } catch (error) {
                document.getElementById('register-response').textContent = `Error: ${error.message}`;
                console.error('Register error:', error);
            }
        }

        async function testApiCall() {
            const token = document.getElementById('token').value;
            const tenantHeader = document.getElementById('tenant-header').value;

            try {
                const headers = {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                };

                if (tenantHeader) {
                    headers['X-TenantID'] = tenantHeader;
                }

                const response = await fetch(`${API_URL}/collections`, {
                    method: 'GET',
                    headers: headers
                });

                let data;
                try {
                    data = await response.json();
                } catch (e) {
                    data = await response.text();
                }

                document.getElementById('api-response').textContent = 
                    typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
                console.log('API response:', data);
            } catch (error) {
                document.getElementById('api-response').textContent = `Error: ${error.message}`;
                console.error('API call error:', error);
            }
        }
    </script>
</body>
</html>

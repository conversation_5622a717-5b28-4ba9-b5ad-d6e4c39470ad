-- Add category_id column to collection_listing table in all tenant schemas
-- This script will add the category_id column to the collection_listing table in all tenant schemas

-- Function to add category_id column to collection_listing table in all schemas
CREATE OR REPLACE FUNCTION add_category_id_to_all_schemas() R<PERSON><PERSON>NS void AS $$
DECLARE
    schema_name text;
BEGIN
    -- Loop through all schemas except system schemas
    FOR schema_name IN 
        SELECT nspname FROM pg_namespace 
        WHERE nspname NOT LIKE 'pg_%' AND nspname != 'information_schema'
    LOOP
        -- Check if the collection_listing table exists in this schema
        IF EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_schema = schema_name AND table_name = 'collection_listing'
        ) THEN
            -- Check if the category_id column already exists
            IF NOT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_schema = schema_name AND table_name = 'collection_listing' AND column_name = 'category_id'
            ) THEN
                -- Add the category_id column
                EXECUTE format('ALTER TABLE %I.collection_listing ADD COLUMN IF NOT EXISTS category_id INT', schema_name);
                
                -- Add foreign key constraint if the category table exists
                IF EXISTS (
                    SELECT 1 FROM information_schema.tables 
                    WHERE table_schema = schema_name AND table_name = 'category'
                ) THEN
                    BEGIN
                        EXECUTE format('ALTER TABLE %I.collection_listing ADD CONSTRAINT fk_collection_listing_category FOREIGN KEY (category_id) REFERENCES %I.category(id)', schema_name, schema_name);
                    EXCEPTION WHEN OTHERS THEN
                        RAISE NOTICE 'Error adding foreign key constraint in schema %: %', schema_name, SQLERRM;
                    END;
                    
                    -- Add index for better performance
                    BEGIN
                        EXECUTE format('CREATE INDEX IF NOT EXISTS idx_collection_listing_category_id ON %I.collection_listing(category_id)', schema_name);
                    EXCEPTION WHEN OTHERS THEN
                        RAISE NOTICE 'Error creating index in schema %: %', schema_name, SQLERRM;
                    END;
                END IF;
                
                RAISE NOTICE 'Added category_id column to collection_listing table in schema %', schema_name;
            ELSE
                RAISE NOTICE 'category_id column already exists in collection_listing table in schema %', schema_name;
            END IF;
        ELSE
            RAISE NOTICE 'collection_listing table does not exist in schema %', schema_name;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Execute the function
SELECT add_category_id_to_all_schemas();

-- Drop the function after execution
DROP FUNCTION IF EXISTS add_category_id_to_all_schemas();

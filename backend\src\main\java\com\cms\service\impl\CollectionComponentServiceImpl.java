package com.cms.service.impl;

import com.cms.config.TenantContextHolder;
import com.cms.entity.CollectionComponent;
import com.cms.entity.CollectionListing;
import com.cms.entity.ComponentListing;
import com.cms.exception.ForeignKeyViolationException;
import com.cms.exception.NoContentException;
import com.cms.exception.NullConstraintViolationException;
import com.cms.exception.ResourceNotFoundException;
import com.cms.repository.CollectionComponentRepository;
import com.cms.repository.CollectionListingRepository;
import com.cms.repository.ComponentListingRepository;
import com.cms.service.CollectionComponentService;
import com.cms.service.CollectionOrderingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class CollectionComponentServiceImpl implements CollectionComponentService {

    private final CollectionComponentRepository collectionComponentRepository;
    private final CollectionListingRepository collectionListingRepository;
    private final ComponentListingRepository componentListingRepository;
    private final CollectionOrderingService collectionOrderingService;

    @Override
    public CollectionComponent getCollectionComponentById(Integer id) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Getting collection component by ID: {} for tenant: {}", id, currentTenant);

        return collectionComponentRepository.findById(id)
                .orElseThrow(() -> {
                    log.error("CollectionComponent not found with id: {} in tenant: {}", id, currentTenant);
                    return new ResourceNotFoundException("CollectionComponent not found with id: " + id);
                });
    }

    @Override
    public List<CollectionComponent> getAllCollectionComponents() {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Getting all collection components for tenant: {}", currentTenant);

        List<CollectionComponent> components = collectionComponentRepository.findAll();
        log.info("Found {} collection components for tenant: {}", components.size(), currentTenant);

        if (components.isEmpty()) {
            log.warn("No collection components found for tenant: {}", currentTenant);
            throw new NoContentException("No collection components found");
        }
        return components;
    }

    @Override
    public List<CollectionComponent> getCollectionComponentsByCollectionId(Integer collectionId) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Getting collection components for collection ID: {} in tenant: {}", collectionId, currentTenant);

        // Verify collection exists
        if (!collectionListingRepository.existsById(collectionId)) {
            log.error("Collection not found with id: {} in tenant: {}", collectionId, currentTenant);
            throw new ResourceNotFoundException("Collection not found with id: " + collectionId);
        }

        List<CollectionComponent> components = collectionComponentRepository.findByCollectionId(collectionId);
        log.info("Retrieved {} components for collection ID: {} in tenant: {}",
                components.size(), collectionId, currentTenant);
        return components;
    }

    @Override
    @Transactional
    public CollectionComponent createCollectionComponent(CollectionComponent collectionComponent) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Creating collection component in tenant: {}, thread: {}",
                currentTenant, Thread.currentThread().getName());

        // Force the tenant context to ensure it's set correctly for this transaction
        TenantContextHolder.forceTenantContext(currentTenant);
        log.info("Forced tenant context to: {}, verified: {}",
                currentTenant, TenantContextHolder.getTenantId());

        try {
            // Validate collection
            if (collectionComponent.getCollection() == null || collectionComponent.getCollection().getId() == null) {
                log.error("Collection is null or has null ID in tenant: {}", currentTenant);
                throw new NullConstraintViolationException("collection");
            }

            Integer collectionId = collectionComponent.getCollection().getId();
            log.info("Verifying collection with ID: {} exists in tenant: {}", collectionId, currentTenant);

            // Verify the tenant context is still correct before checking collection existence
            String verifiedTenant = TenantContextHolder.getTenantId();
            if (!currentTenant.equals(verifiedTenant)) {
                log.error("CRITICAL: Tenant context changed during operation! Expected: {}, Actual: {}",
                        currentTenant, verifiedTenant);
                // Force it back to the correct tenant
                TenantContextHolder.forceTenantContext(currentTenant);
                log.info("Forced tenant context back to: {}", currentTenant);
            }

            // Explicitly check if the collection exists in the current tenant schema
            // This is critical for multi-tenant applications to prevent cross-tenant references
            log.info("Checking if collection ID: {} exists in tenant: {}", collectionId, currentTenant);
            boolean collectionExists = collectionListingRepository.existsById(collectionId);
            log.info("Collection ID: {} exists in tenant {}: {}", collectionId, currentTenant, collectionExists);

            if (!collectionExists) {
                // Double-check with direct SQL to bypass any Hibernate session/cache issues
                boolean existsInDb = verifyCollectionExistsInCurrentSchema(collectionId);

                if (existsInDb) {
                    log.warn("Collection exists in database but not found by repository! This suggests a tenant context issue.");
                    // Since it exists in the database, we can proceed
                    log.info("Proceeding with operation since collection exists in the database");
                } else {
                    // Check if the collection exists in other tenant schemas
                    List<String> schemasWithCollection = findSchemasContainingCollection(collectionId);

                    String errorMessage;
                    if (!schemasWithCollection.isEmpty()) {
                        // Collection exists in other tenant schemas
                        errorMessage = String.format(
                            "Collection with ID: %d does not exist in tenant: %s, but it exists in the following tenant schemas: %s. " +
                            "This is a cross-tenant reference issue. Please use a collection that belongs to the current tenant.",
                            collectionId, currentTenant, String.join(", ", schemasWithCollection));
                    } else {
                        // Collection doesn't exist in any tenant schema
                        errorMessage = String.format(
                            "Collection with ID: %d does not exist in tenant: %s or any other tenant schema. " +
                            "Please verify the collection ID is correct.",
                            collectionId, currentTenant);
                    }

                    log.error(errorMessage);
                    throw new ForeignKeyViolationException("CollectionListing", "id", collectionId, errorMessage);
                }
            }

            // Get the full collection entity with additional validation
            CollectionListing collection = collectionListingRepository.findById(collectionId)
                    .orElseThrow(() -> {
                        String errorMessage = String.format(
                            "Failed to retrieve collection with ID: %d in tenant: %s - collection does not exist in this tenant schema. " +
                            "Please verify that you're using a collection that belongs to the current tenant.",
                            collectionId, currentTenant);
                        log.error(errorMessage);
                        return new ForeignKeyViolationException("CollectionListing", "id", collectionId, errorMessage);
                    });

            log.info("Found collection: {} with ID: {} in tenant: {}",
                    collection.getCollectionName(), collectionId, currentTenant);
            collectionComponent.setCollection(collection);

            // Validate component
            if (collectionComponent.getComponent() == null || collectionComponent.getComponent().getId() == null) {
                log.error("Component is null or has null ID in tenant: {}", currentTenant);
                throw new NullConstraintViolationException("component");
            }

            Integer componentId = collectionComponent.getComponent().getId();
            log.info("Verifying component with ID: {} exists in tenant: {}", componentId, currentTenant);

            // Verify the tenant context is still correct before checking component existence
            String verifiedComponentTenant = TenantContextHolder.getTenantId();
            if (!currentTenant.equals(verifiedComponentTenant)) {
                log.error("CRITICAL: Tenant context changed during component check! Expected: {}, Actual: {}",
                        currentTenant, verifiedComponentTenant);
                // Force it back to the correct tenant
                TenantContextHolder.forceTenantContext(currentTenant);
                log.info("Forced tenant context back to: {}", currentTenant);
            }

            // Explicitly check if the component exists in the current tenant schema
            log.info("Checking if component ID: {} exists in tenant: {}", componentId, currentTenant);
            boolean componentExists = componentListingRepository.existsById(componentId);
            log.info("Component ID: {} exists in tenant {}: {}", componentId, currentTenant, componentExists);

            if (!componentExists) {
                // Double-check with direct SQL to bypass any Hibernate session/cache issues
                boolean existsInDb = verifyComponentExistsInCurrentSchema(componentId);

                if (existsInDb) {
                    log.warn("Component exists in database but not found by repository! This suggests a tenant context issue.");
                    // Since it exists in the database, we can proceed
                    log.info("Proceeding with operation since component exists in the database");
                } else {
                    // Check if the component exists in other tenant schemas
                    List<String> schemasWithComponent = findSchemasContainingComponent(componentId);

                    String errorMessage;
                    if (!schemasWithComponent.isEmpty()) {
                        // Component exists in other tenant schemas
                        errorMessage = String.format(
                            "Component with ID: %d does not exist in tenant: %s, but it exists in the following tenant schemas: %s. " +
                            "This is a cross-tenant reference issue. Please use a component that belongs to the current tenant.",
                            componentId, currentTenant, String.join(", ", schemasWithComponent));
                    } else {
                        // Component doesn't exist in any tenant schema
                        errorMessage = String.format(
                            "Component with ID: %d does not exist in tenant: %s or any other tenant schema. " +
                            "Please verify the component ID is correct.",
                            componentId, currentTenant);
                    }

                    log.error(errorMessage);
                    throw new ForeignKeyViolationException("ComponentListing", "id", componentId, errorMessage);
                }
            }

            // Get the full component entity with additional validation
            ComponentListing component = componentListingRepository.findById(componentId)
                    .orElseThrow(() -> {
                        String errorMessage = String.format(
                            "Failed to retrieve component with ID: %d in tenant: %s - component does not exist in this tenant schema. " +
                            "Please verify that you're using a component that belongs to the current tenant.",
                            componentId, currentTenant);
                        log.error(errorMessage);
                        return new ForeignKeyViolationException("ComponentListing", "id", componentId, errorMessage);
                    });

            log.info("Found component: {} with ID: {} in tenant: {}",
                    component.getComponentName(), componentId, currentTenant);
            collectionComponent.setComponent(component);

            // Set display_preference to be the max value for this collection + 10
            // This considers both components and fields
            Integer nextDisplayPreference = collectionOrderingService.getNextDisplayPreference(collectionId);
            log.info("Setting display preference to: {} for collection ID: {} in tenant: {}",
                    nextDisplayPreference, collectionId, currentTenant);
            collectionComponent.setDisplayPreference(nextDisplayPreference);

            // Set default values if not provided
            if (collectionComponent.getIsActive() == null) {
                collectionComponent.setIsActive(true);
            }

            // Ensure isRepeatable is properly set
            if (collectionComponent.getIsRepeatable() == null) {
                collectionComponent.setIsRepeatable(false);
            }

            // If component is repeatable, ensure min/max values are set
            if (Boolean.TRUE.equals(collectionComponent.getIsRepeatable())) {
                if (collectionComponent.getMinRepeatOccurrences() == null) {
                    collectionComponent.setMinRepeatOccurrences(0);
                }
                if (collectionComponent.getMaxRepeatOccurrences() == null) {
                    collectionComponent.setMaxRepeatOccurrences(10); // Default max value
                }
            }

            // Store additional information if provided
            if (collectionComponent.getAdditionalInformation() != null) {
                // Validate that it's valid JSON
                try {
                    // Just parse to validate, we don't need the result
                    com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
                    mapper.readTree(collectionComponent.getAdditionalInformation());
                } catch (Exception e) {
                    log.warn("Invalid JSON in additionalInformation in tenant {}: {}", currentTenant, e.getMessage());
                    // We don't throw an exception, just log a warning
                }
            }

            // Verify tenant context one last time before saving
            String finalTenantCheck = TenantContextHolder.getTenantId();
            if (!currentTenant.equals(finalTenantCheck)) {
                log.error("CRITICAL: Tenant context changed before saving! Expected: {}, Actual: {}",
                        currentTenant, finalTenantCheck);
                // Force it back to the correct tenant
                TenantContextHolder.forceTenantContext(currentTenant);
                log.info("Forced tenant context back to: {} before saving", currentTenant);
            }

            // Save the collection component
            log.info("Saving collection component for collection ID: {} and component ID: {} in tenant: {}",
                    collectionId, componentId, currentTenant);

            try {
                CollectionComponent savedComponent = collectionComponentRepository.save(collectionComponent);
                log.info("Created new collection component with ID: {} in tenant: {}",
                        savedComponent.getId(), currentTenant);
                return savedComponent;
            } catch (Exception e) {
                log.error("Error saving collection component: {}", e.getMessage(), e);

                // Try to diagnose the issue with direct SQL
                try {
                    log.info("Diagnosing issue with direct SQL queries");

                    // Check if collection exists in current schema
                    boolean collectionExistsInDb = verifyCollectionExistsInCurrentSchema(collectionId);

                    // Check if component exists in current schema
                    boolean componentExistsInDb = verifyComponentExistsInCurrentSchema(componentId);

                    // Check current schema
                    String schemaSql = "SELECT current_schema()";
                    String currentSchema = jdbcTemplate.queryForObject(schemaSql, String.class);
                    log.info("Current database schema: {}, expected tenant: {}", currentSchema, currentTenant);

                    // Check if the collection exists in other tenant schemas
                    List<String> schemasWithCollection = findSchemasContainingCollection(collectionId);
                    if (!schemasWithCollection.isEmpty()) {
                        log.error("CROSS-TENANT REFERENCE DETECTED: Collection ID {} exists in tenant schemas: {} but not in current tenant: {}",
                                collectionId, String.join(", ", schemasWithCollection), currentTenant);
                    }

                    // Check if the component exists in other tenant schemas
                    List<String> schemasWithComponent = findSchemasContainingComponent(componentId);
                    if (!schemasWithComponent.isEmpty()) {
                        log.error("CROSS-TENANT REFERENCE DETECTED: Component ID {} exists in tenant schemas: {} but not in current tenant: {}",
                                componentId, String.join(", ", schemasWithComponent), currentTenant);
                    }

                    // Check if the tenant context is correct
                    if (!currentTenant.equals(currentSchema)) {
                        log.error("CRITICAL: Tenant context mismatch! Expected: {}, Actual database schema: {}",
                                currentTenant, currentSchema);
                    }

                    // Provide a summary of the issue
                    if (!collectionExistsInDb && !schemasWithCollection.isEmpty()) {
                        log.error("DIAGNOSIS: This is a cross-tenant reference issue. The collection exists in another tenant schema but not in the current one.");
                    } else if (!componentExistsInDb && !schemasWithComponent.isEmpty()) {
                        log.error("DIAGNOSIS: This is a cross-tenant reference issue. The component exists in another tenant schema but not in the current one.");
                    } else if (!currentTenant.equals(currentSchema)) {
                        log.error("DIAGNOSIS: This is a tenant context issue. The database operation is being performed in the wrong schema.");
                    } else {
                        log.error("DIAGNOSIS: Unknown issue. Both collection and component exist in the current tenant schema, but the operation still failed.");
                    }

                } catch (Exception diagEx) {
                    log.warn("Error during diagnostic queries: {}", diagEx.getMessage());
                }

                // Re-throw the original exception
                throw e;
            }
        } catch (Exception e) {
            log.error("Error creating collection component in tenant {}: {}", currentTenant, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional
    public CollectionComponent updateCollectionComponent(Integer id, CollectionComponent collectionComponent) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Updating collection component with ID: {} in tenant: {}", id, currentTenant);

        try {
            // Verify the component exists
            CollectionComponent existingComponent = getCollectionComponentById(id);
            log.info("Found existing component with ID: {} in tenant: {}", id, currentTenant);

            // Update component reference if provided
            if (collectionComponent.getComponent() != null && collectionComponent.getComponent().getId() != null) {
                Integer componentId = collectionComponent.getComponent().getId();
                log.info("Updating component reference to ID: {} in tenant: {}", componentId, currentTenant);

                // Explicitly check if the component exists in the current tenant schema
                boolean componentExists = componentListingRepository.existsById(componentId);
                if (!componentExists) {
                    String errorMessage = String.format(
                        "Component with ID: %d does not exist in tenant: %s. This component may exist in another tenant schema.",
                        componentId, currentTenant);
                    log.error(errorMessage);
                    throw new ForeignKeyViolationException("ComponentListing", "id", componentId, errorMessage);
                }

                ComponentListing component = componentListingRepository.findById(componentId)
                        .orElseThrow(() -> {
                            String errorMessage = String.format(
                                "Failed to retrieve component with ID: %d in tenant: %s - component does not exist in this tenant schema. " +
                                "Please verify that you're using a component that belongs to the current tenant.",
                                componentId, currentTenant);
                            log.error(errorMessage);
                            return new ForeignKeyViolationException("ComponentListing", "id", componentId, errorMessage);
                        });

                log.info("Found component: {} with ID: {} in tenant: {}",
                        component.getComponentName(), componentId, currentTenant);
                existingComponent.setComponent(component);
            }

            // Update collection reference if provided
            if (collectionComponent.getCollection() != null && collectionComponent.getCollection().getId() != null) {
                Integer collectionId = collectionComponent.getCollection().getId();
                log.info("Updating collection reference to ID: {} in tenant: {}", collectionId, currentTenant);

                // Explicitly check if the collection exists in the current tenant schema
                // This is critical for multi-tenant applications to prevent cross-tenant references
                boolean collectionExists = collectionListingRepository.existsById(collectionId);
                if (!collectionExists) {
                    String errorMessage = String.format(
                        "Collection with ID: %d does not exist in tenant: %s. This collection may exist in another tenant schema.",
                        collectionId, currentTenant);
                    log.error(errorMessage);
                    throw new ForeignKeyViolationException("CollectionListing", "id", collectionId, errorMessage);
                }

                CollectionListing collection = collectionListingRepository.findById(collectionId)
                        .orElseThrow(() -> {
                            String errorMessage = String.format(
                                "Failed to retrieve collection with ID: %d in tenant: %s - collection does not exist in this tenant schema. " +
                                "Please verify that you're using a collection that belongs to the current tenant.",
                                collectionId, currentTenant);
                            log.error(errorMessage);
                            return new ForeignKeyViolationException("CollectionListing", "id", collectionId, errorMessage);
                        });

                log.info("Found collection: {} with ID: {} in tenant: {}",
                        collection.getCollectionName(), collectionId, currentTenant);
                existingComponent.setCollection(collection);
            }

            // Update display preference if provided
            if (collectionComponent.getDisplayPreference() != null) {
                log.info("Updating display preference to: {} in tenant: {}",
                        collectionComponent.getDisplayPreference(), currentTenant);
                existingComponent.setDisplayPreference(collectionComponent.getDisplayPreference());
            }

            // Update repeatable settings if provided
            if (collectionComponent.getIsRepeatable() != null) {
                log.info("Updating isRepeatable to: {} in tenant: {}",
                        collectionComponent.getIsRepeatable(), currentTenant);
                existingComponent.setIsRepeatable(collectionComponent.getIsRepeatable());
            }

            if (collectionComponent.getMinRepeatOccurrences() != null) {
                log.info("Updating minRepeatOccurrences to: {} in tenant: {}",
                        collectionComponent.getMinRepeatOccurrences(), currentTenant);
                existingComponent.setMinRepeatOccurrences(collectionComponent.getMinRepeatOccurrences());
            }

            if (collectionComponent.getMaxRepeatOccurrences() != null) {
                log.info("Updating maxRepeatOccurrences to: {} in tenant: {}",
                        collectionComponent.getMaxRepeatOccurrences(), currentTenant);
                existingComponent.setMaxRepeatOccurrences(collectionComponent.getMaxRepeatOccurrences());
            }

            // Update active status if provided
            if (collectionComponent.getIsActive() != null) {
                log.info("Updating isActive to: {} in tenant: {}",
                        collectionComponent.getIsActive(), currentTenant);
                existingComponent.setIsActive(collectionComponent.getIsActive());
            }

            // Update additional information if provided
            if (collectionComponent.getAdditionalInformation() != null) {
                log.info("Updating additionalInformation in tenant: {}", currentTenant);
                // Validate that it's valid JSON
                try {
                    // Just parse to validate, we don't need the result
                    com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
                    mapper.readTree(collectionComponent.getAdditionalInformation());
                    existingComponent.setAdditionalInformation(collectionComponent.getAdditionalInformation());
                } catch (Exception e) {
                    log.warn("Invalid JSON in additionalInformation in tenant {}: {}", currentTenant, e.getMessage());
                    // We don't throw an exception, just log a warning
                }
            }

            // Save the updated component
            log.info("Saving updated collection component with ID: {} in tenant: {}", id, currentTenant);
            CollectionComponent updatedComponent = collectionComponentRepository.save(existingComponent);
            log.info("Successfully updated collection component with ID: {} in tenant: {}",
                    updatedComponent.getId(), currentTenant);
            return updatedComponent;
        } catch (Exception e) {
            log.error("Error updating collection component with ID: {} in tenant {}: {}",
                    id, currentTenant, e.getMessage(), e);
            throw e;
        }
    }

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * Verify if a collection exists in the current schema using direct SQL
     * This bypasses any potential Hibernate session/cache issues
     *
     * @param collectionId The collection ID to check
     * @return true if the collection exists, false otherwise
     */
    private boolean verifyCollectionExistsInCurrentSchema(Integer collectionId) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Verifying collection ID: {} exists in current schema: {} using direct SQL",
                collectionId, currentTenant);

        try {
            // First check the current schema
            String sql = "SELECT EXISTS(SELECT 1 FROM collection_listing WHERE id = ?)";
            boolean exists = jdbcTemplate.queryForObject(sql, Boolean.class, collectionId);
            log.info("Collection ID: {} exists in current schema {}: {}",
                    collectionId, currentTenant, exists);
            return exists;
        } catch (Exception e) {
            log.error("Error verifying collection existence in schema {}: {}",
                    currentTenant, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Check if a collection exists in any other tenant schemas
     * This helps diagnose cross-tenant reference issues
     *
     * @param collectionId The collection ID to check
     * @return A list of tenant schemas where the collection exists
     */
    private List<String> findSchemasContainingCollection(Integer collectionId) {
        List<String> schemasWithCollection = new ArrayList<>();
        String currentTenant = TenantContextHolder.getTenantId();

        try {
            // Get all schemas except the current one
            String schemasSql = "SELECT schema_name FROM information_schema.schemata " +
                    "WHERE schema_name != 'public' AND schema_name != 'information_schema' " +
                    "AND schema_name != ? ORDER BY schema_name";
            List<String> schemas = jdbcTemplate.queryForList(schemasSql, String.class, currentTenant);

            // Check each schema for the collection
            for (String schema : schemas) {
                String checkSql = String.format(
                        "SELECT EXISTS(SELECT 1 FROM %s.collection_listing WHERE id = ?)",
                        schema);
                try {
                    Boolean exists = jdbcTemplate.queryForObject(checkSql, Boolean.class, collectionId);
                    if (Boolean.TRUE.equals(exists)) {
                        schemasWithCollection.add(schema);
                        log.info("Collection ID: {} exists in tenant schema: {}", collectionId, schema);
                    }
                } catch (Exception e) {
                    log.debug("Error checking schema {}: {}", schema, e.getMessage());
                    // Continue checking other schemas
                }
            }
        } catch (Exception e) {
            log.error("Error searching for collection across schemas: {}", e.getMessage(), e);
        }

        return schemasWithCollection;
    }

    /**
     * Verify if a component exists in the current schema using direct SQL
     * This bypasses any potential Hibernate session/cache issues
     *
     * @param componentId The component ID to check
     * @return true if the component exists, false otherwise
     */
    private boolean verifyComponentExistsInCurrentSchema(Integer componentId) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Verifying component ID: {} exists in current schema: {} using direct SQL",
                componentId, currentTenant);

        try {
            // First check the current schema
            String sql = "SELECT EXISTS(SELECT 1 FROM component_listing WHERE id = ?)";
            boolean exists = jdbcTemplate.queryForObject(sql, Boolean.class, componentId);
            log.info("Component ID: {} exists in current schema {}: {}",
                    componentId, currentTenant, exists);
            return exists;
        } catch (Exception e) {
            log.error("Error verifying component existence in schema {}: {}",
                    currentTenant, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Check if a component exists in any other tenant schemas
     * This helps diagnose cross-tenant reference issues
     *
     * @param componentId The component ID to check
     * @return A list of tenant schemas where the component exists
     */
    private List<String> findSchemasContainingComponent(Integer componentId) {
        List<String> schemasWithComponent = new ArrayList<>();
        String currentTenant = TenantContextHolder.getTenantId();

        try {
            // Get all schemas except the current one
            String schemasSql = "SELECT schema_name FROM information_schema.schemata " +
                    "WHERE schema_name != 'public' AND schema_name != 'information_schema' " +
                    "AND schema_name != ? ORDER BY schema_name";
            List<String> schemas = jdbcTemplate.queryForList(schemasSql, String.class, currentTenant);

            // Check each schema for the component
            for (String schema : schemas) {
                String checkSql = String.format(
                        "SELECT EXISTS(SELECT 1 FROM %s.component_listing WHERE id = ?)",
                        schema);
                try {
                    Boolean exists = jdbcTemplate.queryForObject(checkSql, Boolean.class, componentId);
                    if (Boolean.TRUE.equals(exists)) {
                        schemasWithComponent.add(schema);
                        log.info("Component ID: {} exists in tenant schema: {}", componentId, schema);
                    }
                } catch (Exception e) {
                    log.debug("Error checking schema {}: {}", schema, e.getMessage());
                    // Continue checking other schemas
                }
            }
        } catch (Exception e) {
            log.error("Error searching for component across schemas: {}", e.getMessage(), e);
        }

        return schemasWithComponent;
    }

    @Override
    @Transactional
    public void deleteCollectionComponent(Integer id) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Deleting collection component with ID: {} in tenant: {}", id, currentTenant);

        try {
            // Verify the component exists (will throw ResourceNotFoundException if not found)
            getCollectionComponentById(id);
            log.info("Verified component with ID: {} exists in tenant: {}", id, currentTenant);

            // Use direct JDBC to delete the component
            String sql = "DELETE FROM collection_components WHERE id = ?";
            log.info("Executing SQL: {} with ID: {} in tenant: {}", sql, id, currentTenant);
            int rowsAffected = jdbcTemplate.update(sql, id);

            if (rowsAffected == 0) {
                log.error("No rows affected when deleting collection component with ID: {} in tenant: {}",
                        id, currentTenant);
                throw new RuntimeException("Failed to delete collection component with ID: " + id);
            }

            log.info("Successfully deleted collection component with ID: {}, rows affected: {} in tenant: {}",
                    id, rowsAffected, currentTenant);
        } catch (Exception e) {
            log.error("Error deleting collection component with ID: {} in tenant {}: {}",
                    id, currentTenant, e.getMessage(), e);
            throw new RuntimeException("Failed to delete collection component with ID: " + id, e);
        }
    }

    @Override
    public Integer getNextAvailableId() {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Getting next available ID for collection component in tenant: {}", currentTenant);

        // Get the maximum ID currently in use
        Integer maxId = collectionComponentRepository.findMaxId();

        // If no records exist, start with ID 1, otherwise use max + 1
        Integer nextId = maxId != null ? maxId + 1 : 1;
        log.info("Next available ID for collection component in tenant {}: {}", currentTenant, nextId);

        return nextId;
    }
}

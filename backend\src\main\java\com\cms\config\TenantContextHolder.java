package com.cms.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Thread-local storage for tenant identifier.
 * This class provides methods to set, get, and clear the tenant identifier for the current thread.
 */
public class TenantContextHolder {
    private static final Logger log = LoggerFactory.getLogger(TenantContextHolder.class);
    private static final ThreadLocal<String> CONTEXT = new ThreadLocal<>();
    private static final String DEFAULT_TENANT = "public";

    /**
     * Set the tenant identifier for the current thread.
     *
     * @param tenantId The tenant identifier to set
     */
    public static void setTenantId(String tenantId) {
        if (tenantId == null || tenantId.isEmpty()) {
            log.warn("Attempt to set null or empty tenant ID, using default tenant");
            CONTEXT.set(DEFAULT_TENANT);
        } else {
            log.info("Setting tenant context to: {}, thread: {}", tenantId, Thread.currentThread().getName());
            CONTEXT.set(tenantId);

            // Verify the tenant was set correctly
            String actualTenant = CONTEXT.get();
            if (!tenantId.equals(actualTenant)) {
                log.error("CRITICAL: Failed to set tenant context! Expected: {}, Actual: {}",
                        tenantId, actualTenant);
            }
        }
    }

    /**
     * Get the tenant identifier for the current thread.
     * If no tenant identifier is set, returns the default tenant.
     *
     * @return The tenant identifier
     */
    public static String getTenantId() {
        String tenantId = CONTEXT.get();
        if (tenantId == null || tenantId.isEmpty()) {
            log.info("No tenant context found, using default tenant, thread: {}", Thread.currentThread().getName());
            return DEFAULT_TENANT;
        }
        log.info("Getting tenant context: {}, thread: {}", tenantId, Thread.currentThread().getName());
        return tenantId;
    }

    /**
     * Clear the tenant identifier for the current thread.
     */
    public static void clear() {
        String currentTenant = CONTEXT.get();
        log.info("Clearing tenant context, current value: {}, thread: {}",
                currentTenant, Thread.currentThread().getName());
        CONTEXT.remove();
    }

    /**
     * Force the tenant identifier for the current thread.
     * This method is used when we need to ensure the tenant context is set correctly.
     *
     * @param tenantId The tenant identifier to force
     */
    public static void forceTenantContext(String tenantId) {
        if (tenantId == null || tenantId.isEmpty()) {
            log.error("Attempt to force null or empty tenant ID, using default tenant");
            CONTEXT.set(DEFAULT_TENANT);
            return;
        }

        log.info("Forcing tenant context to: {}, thread: {}",
                tenantId, Thread.currentThread().getName());
        CONTEXT.set(tenantId);

        // Verify the tenant was set correctly
        String actualTenant = CONTEXT.get();
        if (!tenantId.equals(actualTenant)) {
            log.error("CRITICAL: Failed to force tenant context! Expected: {}, Actual: {}",
                    tenantId, actualTenant);
            throw new RuntimeException("Failed to set tenant context to: " + tenantId);
        }
    }

    /**
     * Get the default tenant identifier.
     *
     * @return The default tenant identifier
     */
    public static String getDefaultTenant() {
        return DEFAULT_TENANT;
    }
}

# Multi-Tenant System Implementation

This directory contains the implementation of a multi-tenant system for the CMS application using the schema-based approach with PostgreSQL.

## Overview

The multi-tenant system allows the CMS application to serve multiple clients (tenants) from a single instance of the application. Each tenant's data is isolated in its own PostgreSQL schema, providing data separation while sharing the same application code and database server.

## Components

1. **Tenant Entity**: Represents a tenant in the system with properties like name, schema name, and active status.

2. **TenantContextHolder**: Utility class that holds the current tenant identifier in a ThreadLocal variable.

3. **CurrentTenantIdentifierResolverImpl**: Implements Hibernate's CurrentTenantIdentifierResolver interface to resolve the current tenant identifier.

4. **MultiTenantConnectionProviderImpl**: Implements Hibernate's MultiTenantConnectionProvider interface to set the schema of the database connection dynamically.

5. **TenantFilter**: Servlet filter that extracts the tenant ID from HTTP requests and sets it in the TenantContextHolder.

6. **TenantService**: Service class for managing tenants, including creating and deleting tenant schemas.

7. **TenantController**: REST controller for tenant management operations.

## How It Works

1. When a request comes in, the TenantFilter extracts the tenant ID from the X-TenantID header.
2. The tenant ID is stored in the TenantContextHolder.
3. When Hibernate needs to execute a query, it uses the CurrentTenantIdentifierResolverImpl to get the current tenant ID.
4. The MultiTenantConnectionProviderImpl sets the schema of the database connection based on the tenant ID.
5. All database operations are performed in the context of the tenant's schema.
6. After the request is processed, the TenantContextHolder is cleared.

## Usage

To use the multi-tenant system:

1. Include the X-TenantID header in your HTTP requests with the schema name of the tenant.
2. If no X-TenantID header is provided, the default "public" schema is used.
3. Use the TenantController to create and manage tenants.

## Example

```
# Create a new tenant
POST /api/tenants
{
    "name": "Tenant 1",
    "schemaName": "tenant1",
    "description": "First tenant"
}

# Access tenant-specific data
GET /api/collections
X-TenantID: tenant1
```

## Notes

- The default tenant is "public" and is created automatically when the application starts.
- Each tenant has its own schema with the same table structure.
- When a tenant is created, the schema is initialized with the same tables and initial data as the public schema.
- Tenant management operations (create, delete) should be restricted to admin users in production.

package com.cms.mapper;

import com.cms.dto.ComponentWithChildrenDTO;
import com.cms.entity.ComponentListing;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class ComponentWithChildrenMapper {

    public ComponentWithChildrenDTO toDTO(ComponentListing component) {
        return new ComponentWithChildrenDTO(component);
    }

    public List<ComponentWithChildrenDTO> toDTOList(List<ComponentListing> components) {
        return components.stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }
}

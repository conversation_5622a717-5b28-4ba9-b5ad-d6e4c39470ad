# Content Management System

A Spring Boot application for managing content components and collections.

## Features

- RESTful API for managing content components and collections
- PostgreSQL database integration
- JWT authentication
- AOP-based logging
- Global exception handling
- Swagger API documentation

## Prerequisites

- Java 17 or higher
- Maven
- PostgreSQL

## Database Setup

1. Create a PostgreSQL database named `cms`:

```sql
CREATE DATABASE cms;
```

2. Update the database connection details in `src/main/resources/application.properties` if needed.

## Running the Application

1. Clone the repository
2. Navigate to the project directory
3. Build the project:

```bash
mvn clean install
```

4. Run the application:

```bash
mvn spring-boot:run
```

The application will start on port 8080 with context path `/api`.

## API Documentation

Swagger UI is available at: http://localhost:8080/api/swagger-ui.html

## Authentication

### Register a new user

```
POST /api/auth/signup
{
  "username": "user",
  "email": "<EMAIL>",
  "password": "password"
}
```

### Login

```
POST /api/auth/login
{
  "username": "user",
  "password": "password"
}
```

The response will contain a JWT token that should be included in the Authorization header for subsequent requests:

```
Authorization: Bearer <token>
```

## Project Structure

- `com.cms.entity`: JPA entity classes
- `com.cms.repository`: Spring Data JPA repositories
- `com.cms.service`: Service interfaces and implementations
- `com.cms.controller`: REST controllers
- `com.cms.security`: JWT authentication
- `com.cms.exception`: Exception handling
- `com.cms.aop`: AOP-based logging
- `com.cms.config`: Configuration classes

## License

This project is licensed under the MIT License.

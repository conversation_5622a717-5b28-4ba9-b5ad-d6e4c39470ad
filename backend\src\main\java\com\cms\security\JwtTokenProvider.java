package com.cms.security;

import com.cms.config.TenantContextHolder;
import com.cms.util.TenantUtils;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import jakarta.annotation.PostConstruct;
import java.security.Key;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
@Slf4j
public class JwtTokenProvider {

    private Key key;

    private final TenantUtils tenantUtils;

    @Value("${jwt.expiration}")
    private long jwtExpirationInMs;

    @PostConstruct
    public void init() {
        // Generate a secure key for HS512 algorithm
        this.key = Keys.secretKeyFor(SignatureAlgorithm.HS512);
    }

    public String generateToken(Authentication authentication) {
        UserDetails userDetails = (UserDetails) authentication.getPrincipal();
        return generateToken(userDetails);
    }

    public String generateToken(UserDetails userDetails) {
        Map<String, Object> claims = new HashMap<>();

        // Get the current tenant from context
        String currentTenant = TenantContextHolder.getTenantId();
        log.debug("Generating token for user: {} with tenant: {}", userDetails.getUsername(), currentTenant);

        // Always add tenant to claims
        claims.put("tenant", currentTenant);
        log.info("Added tenant to JWT claims: {}", currentTenant);

        // If the username doesn't already have the tenant, add it
        String username = userDetails.getUsername();
        if (!username.contains("@") && !currentTenant.equals("public")) {
            username = username + "@" + currentTenant;
            log.debug("Modified username to include tenant: {}", username);
        }

        return createToken(claims, username);
    }

    private String createToken(Map<String, Object> claims, String subject) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + jwtExpirationInMs);

        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(this.key, SignatureAlgorithm.HS512)
                .compact();
    }

    public String getUsernameFromToken(String token) {
        return getClaimFromToken(token, Claims::getSubject);
    }

    public Date getExpirationDateFromToken(String token) {
        return getClaimFromToken(token, Claims::getExpiration);
    }

    public <T> T getClaimFromToken(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = getAllClaimsFromToken(token);
        return claimsResolver.apply(claims);
    }

    private Claims getAllClaimsFromToken(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(this.key)
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    public boolean validateToken(String token, UserDetails userDetails) {
        final String usernameFromToken = getUsernameFromToken(token);
        final String usernameFromUserDetails = userDetails.getUsername();

        log.debug("Validating token - Token username: {}, UserDetails username: {}",
                usernameFromToken, usernameFromUserDetails);

        // Extract username parts for comparison
        String tokenUsername = tenantUtils.extractUsername(usernameFromToken);
        String userDetailsUsername = tenantUtils.extractUsername(usernameFromUserDetails);

        // Check if usernames match (ignoring tenant part) and token is not expired
        boolean isValid = (tokenUsername.equals(userDetailsUsername) && !isTokenExpired(token));

        log.debug("Token validation result: {}", isValid);
        return isValid;
    }

    private boolean isTokenExpired(String token) {
        final Date expiration = getExpirationDateFromToken(token);
        return expiration.before(new Date());
    }

    /**
     * Extract tenant information from JWT token
     *
     * @param token JWT token
     * @return tenant ID or default tenant if not found
     */
    public String getTenantFromToken(String token) {
        try {
            Claims claims = getAllClaimsFromToken(token);
            String tenant = claims.get("tenant", String.class);

            if (tenant != null) {
                log.debug("Extracted tenant from token: {}", tenant);
                return tenant;
            } else {
                // Try to extract from subject (username@tenant)
                String subject = claims.getSubject();
                String extractedTenant = tenantUtils.extractTenantSchemaName(subject);

                if (extractedTenant != null) {
                    log.debug("Extracted tenant from token subject: {}", extractedTenant);
                    return extractedTenant;
                }
            }

            log.debug("No tenant found in token, using default");
            return TenantContextHolder.getDefaultTenant();
        } catch (Exception e) {
            log.warn("Error extracting tenant from token: {}", e.getMessage());
            return TenantContextHolder.getDefaultTenant();
        }
    }
}

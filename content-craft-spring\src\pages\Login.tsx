
import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Link, useNavigate } from 'react-router-dom';
import { Database, Eye, EyeOff, Info } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { toast as sonnerToast } from 'sonner';
import { authApi, getBaseUrl } from '@/lib/api';
import { useAuthStore } from '@/lib/store';
import { getTenantFromToken, getUsernameFromToken } from '@/lib/jwt';
import { useTenant } from '@/components/tenant/TenantProvider';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

// Validation schema
const loginSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
});

type LoginFormValues = z.infer<typeof loginSchema>;

export default function Login() {
  const { login } = useAuthStore();
  const { setCurrentTenant } = useTenant();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = React.useState(false);
  const [showPassword, setShowPassword] = React.useState(false);

  // Set up form with react-hook-form and zod validation
  const formMethods = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  });

  // Check if already authenticated on mount
  // React.useEffect(() => {
  //   const token = localStorage.getItem('cms_token');
  //   if (token) {
  //     console.log('Already have token, redirecting to dashboard');
  //     navigate('/dashboard');
  //   }
  // }, [navigate]);

  // Handle form submission
  const onSubmit = async (data: LoginFormValues) => {
    setIsLoading(true);
    console.log('Attempting login with:', data.username);

    try {
      console.log('Making login API call to backend...');
      console.log('API base URL:', getBaseUrl()); // Add this line to log the base URL
      console.log('Login endpoint:', '/auth/login');
      console.log('Login payload:', { username: data.username, password: data.password });
      const response = await authApi.login(data.username, data.password);
      console.log('Login API response:', response.data);

      // The backend returns { accessToken: "token_value" }
      if (!response.data || !response.data.accessToken) {
        throw new Error('Invalid response: Missing token');
      }

      const token = response.data.accessToken;

      // Extract username and tenant from token
      const username = getUsernameFromToken(token) || data.username;
      const tenant = getTenantFromToken(token);

      console.log('Extracted from token - Username:', username, 'Tenant:', tenant);

      // Create a user object with tenant information
      const user = {
        id: username,
        username: username,
        email: '', // We don't have this from the login response
        tenant: tenant
      };

      // Save token and user info
      console.log('Saving token to auth store and localStorage');
      login(token, user);

      // Set tenant in context
      if (tenant) {
        console.log('Setting tenant context to:', tenant);
        setCurrentTenant(tenant);
      }

      // Also save to localStorage directly for debugging
      localStorage.setItem('cms_token', token);

      // Verify token was stored correctly
      const storedToken = localStorage.getItem('cms_token');
      console.log('Login successful, token stored:', storedToken ? 'Yes' : 'No');
      console.log('Token format valid:', storedToken?.includes('.') && storedToken?.split('.').length === 3 ? 'Yes' : 'No');

      // Verify auth store state
      console.log('Auth store state after login:', { token: !!token, user: !!user });

      toast({
        title: 'Login successful',
        description: 'Welcome back to the CMS',
      });

      sonnerToast.success('Login successful', {
        description: 'Welcome back to the CMS'
      });

      // Longer delay to ensure all state updates are complete
      setTimeout(() => {
        console.log('Navigating to dashboard...');
        navigate('/dashboard');
      }, 500);
    } catch (error: any) {
      console.error('Login error:', error);
      console.error('Error response:', error.response);

      // More detailed error logging
      if (error.response) {
        console.error('Status:', error.response.status);
        console.error('Headers:', error.response.headers);
        console.error('Data:', error.response.data);
      }

      // Try to extract a meaningful error message
      let errorMessage = 'Unable to connect to the server';

      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (error.response.data.error) {
          errorMessage = error.response.data.error;
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      console.log('Login failed:', errorMessage);

      toast({
        title: 'Login failed',
        description: errorMessage,
        variant: 'destructive',
      });

      sonnerToast.error('Login failed', {
        description: errorMessage
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-slate-50 p-4">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="space-y-2">
          <div className="flex justify-center mb-6">
            <div className="flex items-center rounded-lg bg-white p-2 ">
              <img src="/images/cloud-logo.png" alt="CMS Logo" className="h-12 w-36" />
            </div>
          </div>
         <CardDescription className="text-center">
            Enter your credentials to access your account
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Email Domain Login Info */}
          <Alert className="mb-4">
            <Info className="h-4 w-4" />
            <AlertDescription>
              <strong>New:</strong> Login format is now <code>username@tenant_schema</code>.
              For users registered with email domains (e.g., <code><EMAIL></code>),
              use <code>username@acme_com</code> to login.
            </AlertDescription>
          </Alert>

          <Form {...formMethods}>
            <form onSubmit={formMethods.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={formMethods.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Username</FormLabel>
                    <FormControl>
                      <Input placeholder="username@tenant_schema (e.g., admin@acme_com)" {...field} />
                    </FormControl>
                    <FormMessage />
                    <p className="text-xs text-muted-foreground mt-1">
                      Use format: <code>username@tenant_schema</code>.
                      If you registered with <code><EMAIL></code>, login as <code>user@acme_com</code>
                    </p>
                  </FormItem>
                )}
              />

              <FormField
                control={formMethods.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showPassword ? "text" : "password"}
                          placeholder="••••••••"
                          {...field}
                        />
                        <button
                          type="button"
                          className="absolute right-2 top-2.5 text-muted-foreground hover:text-foreground"
                          onClick={() => setShowPassword(!showPassword)}
                          tabIndex={-1}
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? 'Logging in...' : 'Login'}
              </Button>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <div className="text-center text-sm">
            Don't have an account?{' '}
            <Link to="/register" className="text-primary hover:underline">
              Register
            </Link>
          </div>
          <div className="text-center text-sm">
            <Link to="/forgot-password" className="text-primary hover:underline">
              Forgot your password?
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}

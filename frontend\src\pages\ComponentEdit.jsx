import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Card, 
  Tabs, 
  Button, 
  Form, 
  Input, 
  Switch, 
  message, 
  Spin, 
  Typography,
  Space,
  Breadcrumb
} from 'antd';
import { ArrowLeftOutlined, SaveOutlined } from '@ant-design/icons';
import axios from 'axios';
import ChildComponentsList from '../components/ChildComponentsList';
import '../styles/ChildComponentsList.css';

const { TabPane } = Tabs;
const { Title, Text } = Typography;

const ComponentEdit = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [component, setComponent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('details');

  useEffect(() => {
    fetchComponent();
  }, [id]);

  const fetchComponent = async () => {
    setLoading(true);
    try {
      const response = await axios.get(`/api/components/getByIdWithDetails/${id}`);
      setComponent(response.data);
      
      // Set form values
      form.setFieldsValue({
        componentName: response.data.componentName,
        componentDisplayName: response.data.componentDisplayName,
        componentApiId: response.data.componentApiId,
        isActive: response.data.isActive,
        getUrl: response.data.getUrl,
        postUrl: response.data.postUrl,
        updateUrl: response.data.updateUrl
      });
    } catch (error) {
      console.error('Error fetching component:', error);
      message.error('Failed to load component details');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (values) => {
    setSaving(true);
    try {
      const updatedComponent = {
        ...component,
        ...values
      };
      
      await axios.put(`/api/components/update/${id}`, updatedComponent);
      message.success('Component updated successfully');
      fetchComponent(); // Refresh data
    } catch (error) {
      console.error('Error updating component:', error);
      message.error('Failed to update component');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    navigate('/components');
  };

  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  const handleComponentUpdate = () => {
    fetchComponent(); // Refresh component data when child components change
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: '20px' }}>Loading component...</div>
      </div>
    );
  }

  return (
    <div className="component-edit-container">
      <Breadcrumb style={{ marginBottom: '16px' }}>
        <Breadcrumb.Item>
          <a onClick={() => navigate('/components')}>Components</a>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{component?.componentDisplayName || 'Edit Component'}</Breadcrumb.Item>
      </Breadcrumb>

      <div className="page-header" style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Space>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={handleCancel}
            style={{ marginRight: '16px' }}
          />
          <Title level={2} style={{ margin: 0 }}>
            {component?.componentDisplayName || 'Edit Component'}
          </Title>
        </Space>
        <Button 
          type="primary" 
          icon={<SaveOutlined />} 
          onClick={() => form.submit()}
          loading={saving}
        >
          Save Changes
        </Button>
      </div>

      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        <TabPane tab="Component Details" key="details">
          <Card>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSave}
              initialValues={{
                componentName: component?.componentName,
                componentDisplayName: component?.componentDisplayName,
                componentApiId: component?.componentApiId,
                isActive: component?.isActive,
                getUrl: component?.getUrl,
                postUrl: component?.postUrl,
                updateUrl: component?.updateUrl
              }}
            >
              <Form.Item
                name="componentName"
                label="Component Name"
                rules={[{ required: true, message: 'Please enter component name' }]}
              >
                <Input placeholder="Enter component name" />
              </Form.Item>

              <Form.Item
                name="componentDisplayName"
                label="Display Name"
                rules={[{ required: true, message: 'Please enter display name' }]}
              >
                <Input placeholder="Enter display name" />
              </Form.Item>

              <Form.Item
                name="componentApiId"
                label="API ID"
                rules={[{ required: true, message: 'Please enter API ID' }]}
              >
                <Input placeholder="Enter API ID" />
              </Form.Item>

              <Form.Item
                name="getUrl"
                label="GET URL"
              >
                <Input placeholder="Enter GET URL" />
              </Form.Item>

              <Form.Item
                name="postUrl"
                label="POST URL"
              >
                <Input placeholder="Enter POST URL" />
              </Form.Item>

              <Form.Item
                name="updateUrl"
                label="UPDATE URL"
              >
                <Input placeholder="Enter UPDATE URL" />
              </Form.Item>

              <Form.Item
                name="isActive"
                label="Active"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" loading={saving}>
                    Save Changes
                  </Button>
                  <Button onClick={handleCancel}>
                    Cancel
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>
        
        <TabPane tab="Fields" key="fields">
          <Card title="Component Fields">
            <Text>Fields functionality will be implemented here</Text>
          </Card>
        </TabPane>
        
        <TabPane tab="Child Components" key="childComponents">
          <Card>
            {component && (
              <ChildComponentsList 
                parentComponent={component} 
                onUpdate={handleComponentUpdate}
              />
            )}
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default ComponentEdit;

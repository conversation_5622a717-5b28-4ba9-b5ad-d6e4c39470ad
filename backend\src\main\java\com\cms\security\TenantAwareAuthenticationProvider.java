package com.cms.security;

import com.cms.util.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

/**
 * Custom authentication provider that is aware of tenant context.
 * This provider extends the standard DaoAuthenticationProvider to add
 * tenant-specific authentication logic.
 */
@Component
@Slf4j
public class TenantAwareAuthenticationProvider extends DaoAuthenticationProvider {

    private TenantUtils tenantUtils;

    /**
     * Default constructor required by Spring
     */
    public TenantAwareAuthenticationProvider() {
        super();
    }

    /**
     * Constructor that sets the required dependencies.
     *
     * @param userDetailsService The user details service
     * @param passwordEncoder The password encoder
     * @param tenantUtils The tenant utilities
     */
    @Autowired
    public TenantAwareAuthenticationProvider(
            UserDetailsService userDetailsService,
            PasswordEncoder passwordEncoder,
            TenantUtils tenantUtils) {
        super();
        this.tenantUtils = tenantUtils;
        setUserDetailsService(userDetailsService);
        setPasswordEncoder(passwordEncoder);
    }

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        try {
            String username = authentication.getName();
            log.info("TenantAwareAuthenticationProvider authenticating: {}", username);

            // Extract tenant from username if present
            if (tenantUtils != null) {
                String tenantId = tenantUtils.extractTenantSchemaName(username);
                if (tenantId != null) {
                    log.info("Tenant extracted from username: {}", tenantId);
                } else {
                    log.info("No tenant in username, using default tenant");
                }
            } else {
                log.warn("TenantUtils is null, cannot extract tenant from username");
            }

            // Let the parent class handle the actual authentication
            Authentication result = super.authenticate(authentication);
            log.info("Authentication successful for user: {}", username);
            return result;
        } catch (BadCredentialsException e) {
            log.error("Bad credentials for user: {}", authentication.getName());
            throw e;
        } catch (Exception e) {
            log.error("Authentication error for user: {}", authentication.getName(), e);
            throw e;
        }
        // Note: Don't clear tenant context here as it's needed for subsequent operations
    }

    @Override
    protected void additionalAuthenticationChecks(UserDetails userDetails,
                                                UsernamePasswordAuthenticationToken authentication)
                                                throws AuthenticationException {
        log.info("Performing additional authentication checks for user: {}", userDetails.getUsername());
        try {
            super.additionalAuthenticationChecks(userDetails, authentication);
            log.info("Password verification successful for user: {}", userDetails.getUsername());
        } catch (BadCredentialsException e) {
            log.error("Password verification failed for user: {}", userDetails.getUsername());
            throw e;
        }
    }
}

package com.cms.service;

import com.cms.entity.CollectionComponent;
import com.cms.entity.CollectionField;

import java.util.List;
import java.util.Map;

/**
 * Service for managing the ordering of collection components and fields
 */
public interface CollectionOrderingService {
    
    /**
     * Get the maximum display preference value for a collection
     * considering both components and fields
     * 
     * @param collectionId The collection ID
     * @return The maximum display preference value
     */
    Integer getMaxDisplayPreference(Integer collectionId);
    
    /**
     * Get all components and fields for a collection in display order
     * 
     * @param collectionId The collection ID
     * @return Map containing ordered components and fields
     */
    Map<String, Object> getOrderedCollectionItems(Integer collectionId);
    
    /**
     * Reorder components and fields for a collection
     * 
     * @param collectionId The collection ID
     * @param componentIds List of component IDs in display order
     * @param fieldIds List of field IDs in display order
     * @return Map containing updated components and fields
     */
    Map<String, Object> reorderCollectionItems(Integer collectionId, List<Integer> componentIds, List<Integer> fieldIds);
    
    /**
     * Get the next display preference value for a collection
     * 
     * @param collectionId The collection ID
     * @return The next display preference value
     */
    Integer getNextDisplayPreference(Integer collectionId);
    
    /**
     * Get all components for a collection in display order
     * 
     * @param collectionId The collection ID
     * @return List of components in display order
     */
    List<CollectionComponent> getOrderedComponents(Integer collectionId);
    
    /**
     * Get all fields for a collection in display order
     * 
     * @param collectionId The collection ID
     * @return List of fields in display order
     */
    List<CollectionField> getOrderedFields(Integer collectionId);
}

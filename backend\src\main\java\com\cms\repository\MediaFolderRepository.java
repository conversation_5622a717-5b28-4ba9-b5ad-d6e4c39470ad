package com.cms.repository;

import com.cms.entity.MediaFolder;
import com.cms.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface MediaFolderRepository extends JpaRepository<MediaFolder, Integer> {

    List<MediaFolder> findByParentIsNull();

    List<MediaFolder> findByParent(MediaFolder parent);

    List<MediaFolder> findByCreatedBy(User user);

    // This method might return multiple results if there are duplicate folder names
    // Use with caution and prefer the methods below for uniqueness checks
    Optional<MediaFolder> findByFolderName(String folderName);

    @Query("SELECT f FROM MediaFolder f WHERE f.folderName = :folderName AND f.parent IS NULL")
    List<MediaFolder> findRootFoldersByName(@Param("folderName") String folderName);

    @Query("SELECT f FROM MediaFolder f WHERE f.folderName = :folderName AND f.parent IS NULL AND f.id != :excludeId")
    List<MediaFolder> findRootFoldersByNameExcludingId(@Param("folderName") String folderName, @Param("excludeId") Integer excludeId);

    @Query("SELECT f FROM MediaFolder f WHERE f.folderName LIKE %:name%")
    List<MediaFolder> searchByName(@Param("name") String name);

    @Query("SELECT COUNT(f) FROM MediaFolder f WHERE f.parent = :parent")
    Long countByParent(@Param("parent") MediaFolder parent);

    boolean existsByFolderNameAndParent(String folderName, MediaFolder parent);
}

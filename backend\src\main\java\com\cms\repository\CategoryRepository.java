package com.cms.repository;

import com.cms.entity.Category;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Category entity operations
 */
@Repository
public interface CategoryRepository extends JpaRepository<Category, Integer> {
    /**
     * Find a category by name
     * @param categoryName Category name
     * @return Optional containing the category if found
     */
    Optional<Category> findByCategoryName(String categoryName);

    /**
     * Check if a category with the given name exists
     * @param categoryName Category name
     * @return true if a category with the name exists, false otherwise
     */
    boolean existsByCategoryName(String categoryName);

    /**
     * Find categories by client ID using native SQL query
     * @param clientId Client ID
     * @return List of categories for the client
     */
    @Query(value = "SELECT * FROM category WHERE client_id = :clientId", nativeQuery = true)
    List<Category> findByClientId(@Param("clientId") Integer clientId);

    List<Category> findByParentCategoryId(Integer parentCategoryId);

    /**
     * Find child categories by parent category ID and client ID
     * @param parentCategoryId Parent category ID
     * @param clientId Client ID
     * @return List of child categories for the specified parent and client
     */
    @Query("SELECT c FROM Category c WHERE c.parentCategory.id = :parentCategoryId AND c.client.id = :clientId")
    List<Category> findByParentCategoryIdAndClientId(@Param("parentCategoryId") Integer parentCategoryId, @Param("clientId") Integer clientId);


}

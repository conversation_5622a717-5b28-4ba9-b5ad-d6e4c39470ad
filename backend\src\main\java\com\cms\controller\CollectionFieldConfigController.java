package com.cms.controller;

import com.cms.entity.CollectionFieldConfig;
import com.cms.service.CollectionFieldConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/collection-field-configs")
@RequiredArgsConstructor
@Tag(name = "Collection Field Config", description = "Collection Field Config API")
@Slf4j
public class CollectionFieldConfigController {

    private final CollectionFieldConfigService collectionFieldConfigService;

    @GetMapping("/getAll")
    @Operation(summary = "Get all collection field configs", description = "Returns a list of all collection field configs or 204 if none found")
    public ResponseEntity<?> getAllCollectionFieldConfigs() {
        List<CollectionFieldConfig> configs = collectionFieldConfigService.getAllCollectionFieldConfigs();
        if (configs.isEmpty()) {
            return ResponseEntity.status(HttpStatus.NO_CONTENT)
                    .body(Map.of("message", "No data in the table"));
        }
        return ResponseEntity.ok(configs);
    }

    @GetMapping("/getByFieldId/{collectionFieldId}")
    @Operation(summary = "Get collection field configs by collection field ID",
               description = "Returns a list of collection field configs for a specific collection field or 204 if none found")
    public ResponseEntity<?> getCollectionFieldConfigsByCollectionFieldId(
            @PathVariable Integer collectionFieldId) {
        try {
            List<CollectionFieldConfig> configs = collectionFieldConfigService.getCollectionFieldConfigsByCollectionFieldId(collectionFieldId);
            if (configs.isEmpty()) {
                return ResponseEntity.status(HttpStatus.NO_CONTENT)
                        .body(Map.of("message", "No configs found for collection field with ID: " + collectionFieldId));
            }
            return ResponseEntity.ok(configs);
        } catch (Exception e) {
            // Handle case where collection field doesn't exist
            return ResponseEntity.status(HttpStatus.NO_CONTENT)
                    .body(Map.of("message", "No configs found for collection field with ID: " + collectionFieldId));
        }
    }

    @GetMapping("/getById/{id}")
    @Operation(summary = "Get collection field config by ID", description = "Returns a collection field config by its ID or 204 if not found")
    public ResponseEntity<?> getCollectionFieldConfigById(@PathVariable Integer id) {
        var config = collectionFieldConfigService.getCollectionFieldConfigById(id);
        if (config.isPresent()) {
            return ResponseEntity.ok(config.get());
        } else {
            return ResponseEntity.status(HttpStatus.NO_CONTENT)
                    .body(Map.of("message", "No content found for collection field config with id: " + id));
        }
    }

    @PostMapping("/create")
    @Operation(summary = "Create a new collection field config", description = "Creates a new collection field config")
    public ResponseEntity<CollectionFieldConfig> createCollectionFieldConfig(
            @Valid @RequestBody CollectionFieldConfig collectionFieldConfig) {
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(collectionFieldConfigService.createCollectionFieldConfig(collectionFieldConfig));
    }

    @PostMapping("/createBulk")
    @Operation(summary = "Create multiple collection field configs",
               description = "Creates multiple collection field configs in a single request")
    public ResponseEntity<List<CollectionFieldConfig>> createCollectionFieldConfigs(
            @Valid @RequestBody List<CollectionFieldConfig> collectionFieldConfigs) {
        log.info("Received createBulk request for {} collection field configs", collectionFieldConfigs.size());

        for (int i = 0; i < collectionFieldConfigs.size(); i++) {
            CollectionFieldConfig config = collectionFieldConfigs.get(i);
            log.info("Config {}: Collection Field ID = {}, Field Config ID = {}, Value = {}",
                i,
                config.getCollectionField() != null ? config.getCollectionField().getId() : "NULL",
                config.getFieldConfig() != null ? config.getFieldConfig().getId() : "NULL",
                config.getFieldConfigValue());
        }

        try {
            List<CollectionFieldConfig> savedConfigs = collectionFieldConfigService.createCollectionFieldConfigs(collectionFieldConfigs);
            log.info("Successfully created {} collection field configs", savedConfigs.size());
            return ResponseEntity.status(HttpStatus.CREATED).body(savedConfigs);
        } catch (Exception e) {
            log.error("Error creating collection field configs: {}", e.getMessage(), e);
            throw e;
        }
    }

    @PutMapping("/update/{id}")
    @Operation(summary = "Update a collection field config", description = "Updates an existing collection field config")
    public ResponseEntity<CollectionFieldConfig> updateCollectionFieldConfig(
            @PathVariable Integer id, @Valid @RequestBody CollectionFieldConfig collectionFieldConfig) {
        return ResponseEntity.ok(collectionFieldConfigService.updateCollectionFieldConfig(id, collectionFieldConfig));
    }

    @DeleteMapping("/deleteById/{id}")
    @Operation(summary = "Delete a collection field config", description = "Deletes a collection field config or returns 204 if it doesn't exist")
    public ResponseEntity<Map<String, String>> deleteCollectionFieldConfig(@PathVariable Integer id) {
        try {
            // Check if the config exists
            var config = collectionFieldConfigService.getCollectionFieldConfigById(id);
            if (config.isEmpty()) {
                // Return 204 with no content message
                return ResponseEntity.status(HttpStatus.NO_CONTENT)
                        .body(Map.of("message", "No content found for collection field config with id: " + id));
            }

            // Delete the config
            collectionFieldConfigService.deleteCollectionFieldConfig(id);

            // Return 204 with success message
            return ResponseEntity.status(HttpStatus.NO_CONTENT)
                    .body(Map.of("message", "Successfully deleted collection field config with id: " + id));
        } catch (Exception e) {
            // If an unexpected error occurs, return 500 with error message
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to process delete request for collection field config with id: " + id + ". Error: " + e.getMessage()));
        }
    }
}

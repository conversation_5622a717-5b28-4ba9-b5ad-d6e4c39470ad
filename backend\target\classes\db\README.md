# Database Indexing

This directory contains SQL scripts for database indexing to improve query performance.

## indexes.sql

This file contains the creation of database indexes for all tables in the CMS system. The indexes are designed to improve performance for:

1. **Foreign Key Lookups**: Indexes on all foreign key columns to speed up join operations.
2. **Common Query Filters**: Indexes on columns frequently used in WHERE clauses (e.g., `is_active`).
3. **Unique Identifiers**: Indexes on columns used for lookups by name or API ID.
4. **JSONB Data**: A GIN index on the `data_json` column in the `content_entries` table to enable efficient querying of JSON data.

## Benefits

- Faster query execution for common operations
- Improved API response times
- Better scalability with larger datasets
- Efficient text search capabilities

## Notes

- Indexes are created with `IF NOT EXISTS` to prevent errors on repeated application startups.
- The `DatabaseIndexingConfig` class ensures these indexes are created after the main schema initialization.
- While indexes improve read performance, they can slightly impact write performance due to the overhead of maintaining the indexes.

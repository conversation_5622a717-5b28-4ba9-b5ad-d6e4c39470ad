package com.cms.service.impl;

import com.cms.entity.CollectionComponent;
import com.cms.entity.CollectionField;
import com.cms.entity.CollectionListing;
import com.cms.exception.ResourceNotFoundException;
import com.cms.repository.CollectionComponentRepository;
import com.cms.repository.CollectionFieldRepository;
import com.cms.repository.CollectionListingRepository;
import com.cms.service.CollectionOrderingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class CollectionOrderingServiceImpl implements CollectionOrderingService {

    private final CollectionComponentRepository collectionComponentRepository;
    private final CollectionFieldRepository collectionFieldRepository;
    private final CollectionListingRepository collectionListingRepository;

    @Override
    public Integer getMaxDisplayPreference(Integer collectionId) {
        // Get max display preference from components
        Integer maxComponentPreference = collectionComponentRepository.findMaxDisplayPreferenceByCollectionId(collectionId);

        // Get max display preference from fields
        Integer maxFieldPreference = collectionFieldRepository.findMaxDisplayPreferenceByCollectionId(collectionId);

        // Return the maximum of the two, or 0 if both are null
        if (maxComponentPreference == null && maxFieldPreference == null) {
            return 0;
        } else if (maxComponentPreference == null) {
            return maxFieldPreference;
        } else if (maxFieldPreference == null) {
            return maxComponentPreference;
        } else {
            return Math.max(maxComponentPreference, maxFieldPreference);
        }
    }

    @Override
    public Integer getNextDisplayPreference(Integer collectionId) {
        // Get the current max display preference and add 10
        return getMaxDisplayPreference(collectionId) + 10;
    }

    @Override
    public Map<String, Object> getOrderedCollectionItems(Integer collectionId) {
        // Verify collection exists
        if (!collectionListingRepository.existsById(collectionId)) {
            throw new ResourceNotFoundException("Collection not found with id: " + collectionId);
        }

        // Get components and fields
        List<CollectionComponent> components = getOrderedComponents(collectionId);
        List<CollectionField> fields = getOrderedFields(collectionId);

        // Create result map
        Map<String, Object> result = new HashMap<>();
        result.put("components", components);
        result.put("fields", fields);

        return result;
    }

    @Override
    public List<CollectionComponent> getOrderedComponents(Integer collectionId) {
        // Get collection
        CollectionListing collection = collectionListingRepository.findById(collectionId)
                .orElseThrow(() -> new ResourceNotFoundException("Collection not found with id: " + collectionId));

        // Get components and sort by display preference
        List<CollectionComponent> components = collectionComponentRepository.findByCollectionAndIsActiveTrue(collection);

        // Sort by display preference, handling null values
        components.sort(Comparator.comparing(
            component -> component.getDisplayPreference() != null ? component.getDisplayPreference() : Integer.MAX_VALUE
        ));

        // Normalize display preferences to 10, 20, 30 format
        for (int i = 0; i < components.size(); i++) {
            components.get(i).setDisplayPreference((i + 1) * 10);
        }

        return components;
    }

    @Override
    public List<CollectionField> getOrderedFields(Integer collectionId) {
        // Get fields and sort by display preference
        List<CollectionField> fields = collectionFieldRepository.findByCollectionId(collectionId);

        // Sort by display preference, handling null values
        fields.sort(Comparator.comparing(
            field -> field.getDisplayPreference() != null ? field.getDisplayPreference() : Integer.MAX_VALUE
        ));

        // Normalize display preferences to 10, 20, 30 format
        for (int i = 0; i < fields.size(); i++) {
            fields.get(i).setDisplayPreference((i + 1) * 10);
        }

        return fields;
    }

    @Override
    @Transactional
    public Map<String, Object> reorderCollectionItems(Integer collectionId, List<Integer> componentIds, List<Integer> fieldIds) {
        // Verify collection exists
        if (!collectionListingRepository.existsById(collectionId)) {
            throw new ResourceNotFoundException("Collection not found with id: " + collectionId);
        }

        // Get all components for this collection
        CollectionListing collection = collectionListingRepository.findById(collectionId)
                .orElseThrow(() -> new ResourceNotFoundException("Collection not found with id: " + collectionId));

        List<CollectionComponent> components = collectionComponentRepository.findByCollectionAndIsActiveTrue(collection);
        List<CollectionField> fields = collectionFieldRepository.findByCollectionId(collectionId);

        log.info("Reordering collection items for collection {}: {} components and {} fields",
                collectionId, componentIds.size(), fieldIds.size());
        log.info("Component IDs: {}", componentIds);
        log.info("Field IDs: {}", fieldIds);

        // Create maps for quick lookup
        Map<Integer, CollectionComponent> componentMap = components.stream()
                .collect(Collectors.toMap(CollectionComponent::getId, component -> component));

        Map<Integer, CollectionField> fieldMap = fields.stream()
                .collect(Collectors.toMap(CollectionField::getId, field -> field));

        // Update display preferences for components
        List<CollectionComponent> updatedComponents = new ArrayList<>();
        for (int i = 0; i < componentIds.size(); i++) {
            Integer componentId = componentIds.get(i);
            CollectionComponent component = componentMap.get(componentId);

            if (component != null) {
                // Set display preference based on position (multiply by 10 to leave room for insertion)
                int displayPreference = (i + 1) * 10;
                log.info("Setting component {} display preference to {}", componentId, displayPreference);
                component.setDisplayPreference(displayPreference);
                updatedComponents.add(component);
            } else {
                log.warn("Component with ID {} not found for collection {}", componentId, collectionId);
            }
        }

        // Update display preferences for fields
        List<CollectionField> updatedFields = new ArrayList<>();
        for (int i = 0; i < fieldIds.size(); i++) {
            Integer fieldId = fieldIds.get(i);
            CollectionField field = fieldMap.get(fieldId);

            if (field != null) {
                // Set display preference based on position (multiply by 10 to leave room for insertion)
                // Start after the components to maintain proper ordering
                int displayPreference = (componentIds.size() + i + 1) * 10;
                log.info("Setting field {} display preference to {}", fieldId, displayPreference);
                field.setDisplayPreference(displayPreference);
                updatedFields.add(field);
            } else {
                log.warn("Field with ID {} not found for collection {}", fieldId, collectionId);
            }
        }

        // Save all updates
        List<CollectionComponent> savedComponents = collectionComponentRepository.saveAll(updatedComponents);
        List<CollectionField> savedFields = collectionFieldRepository.saveAll(updatedFields);

        log.info("Saved {} components and {} fields with updated display preferences",
                savedComponents.size(), savedFields.size());

        // Log the saved display preferences
        savedComponents.forEach(component ->
            log.info("Component {} saved with display preference {}", component.getId(), component.getDisplayPreference()));

        savedFields.forEach(field ->
            log.info("Field {} saved with display preference {}", field.getId(), field.getDisplayPreference()));

        // Create result map
        Map<String, Object> result = new HashMap<>();
        result.put("components", savedComponents);
        result.put("fields", savedFields);

        return result;
    }
}

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { apiTokensApi } from '@/lib/api';
import { useToast } from '@/components/ui/use-toast';
import { format } from 'date-fns';
import {
  Key,
  Plus,
  Copy,
  Check,
  AlertTriangle,
  Clock,
  Calendar,
  Info,
  X
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

// Define token schema
const tokenSchema = z.object({
  name: z.string().min(3, 'Name must be at least 3 characters').max(50, 'Name cannot exceed 50 characters'),
  description: z.string().max(200, 'Description cannot exceed 200 characters').optional(),
  expirationDays: z.number().min(1, 'Expiration must be at least 1 day').max(365, 'Expiration cannot exceed 365 days'),
});

type TokenFormValues = z.infer<typeof tokenSchema>;

// Define token type
interface ApiToken {
  id: number;
  name: string;
  description?: string;
  tokenValue: string;
  expiresAt: string;
  lastUsedAt?: string;
  isActive: boolean;
  createdAt: string;
}

export default function ApiTokenSettings() {
  // Set document title
  React.useEffect(() => {
    document.title = 'Settings | R-CMS';
  }, []);
  const [tokens, setTokens] = useState<ApiToken[]>([]);
  const [loading, setLoading] = useState(true);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [newToken, setNewToken] = useState<ApiToken | null>(null);
  const [copied, setCopied] = useState(false);
  const [revokeDialogOpen, setRevokeDialogOpen] = useState(false);
  const [tokenToRevoke, setTokenToRevoke] = useState<ApiToken | null>(null);
  const { toast } = useToast();

  // Set up form with react-hook-form and zod validation
  const form = useForm<TokenFormValues>({
    resolver: zodResolver(tokenSchema),
    defaultValues: {
      name: '',
      description: '',
      expirationDays: 30,
    },
  });

  // Load tokens on mount
  useEffect(() => {
    loadTokens();
  }, []);

  // Load tokens from API
  const loadTokens = async () => {
    setLoading(true);
    try {
      const response = await apiTokensApi.getAll();
      setTokens(response.data);
    } catch (error) {
      console.error('Error loading tokens:', error);
      toast({
        title: 'Error',
        description: 'Failed to load API tokens',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission
  const onSubmit = async (data: TokenFormValues) => {
    try {
      const response = await apiTokensApi.create(
        data.name,
        data.description || '',
        data.expirationDays
      );

      setNewToken(response.data);
      form.reset();
      loadTokens();

      toast({
        title: 'Success',
        description: 'API token created successfully',
      });
    } catch (error) {
      console.error('Error creating token:', error);
      toast({
        title: 'Error',
        description: 'Failed to create API token',
        variant: 'destructive',
      });
    }
  };

  // Handle token revocation
  const handleRevoke = async () => {
    if (!tokenToRevoke) return;

    try {
      await apiTokensApi.revoke(tokenToRevoke.id);
      setRevokeDialogOpen(false);
      setTokenToRevoke(null);
      loadTokens();

      toast({
        title: 'Success',
        description: 'API token revoked successfully',
      });
    } catch (error) {
      console.error('Error revoking token:', error);
      toast({
        title: 'Error',
        description: 'Failed to revoke API token',
        variant: 'destructive',
      });
    }
  };

  // Copy token to clipboard
  const copyToken = (token: string) => {
    navigator.clipboard.writeText(token);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);

    toast({
      title: 'Copied',
      description: 'API token copied to clipboard',
    });
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy h:mm a');
    } catch (error) {
      return 'Invalid date';
    }
  };

  // Check if token is expired
  const isExpired = (expiresAt: string) => {
    return new Date(expiresAt) < new Date();
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Settings</h1>
          <p className="text-muted-foreground">Manage your CMS settings and API tokens</p>
        </div>
        <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create Token
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Create API Token</DialogTitle>
              <DialogDescription>
                Create a new API token to allow external applications to access your CMS data.
              </DialogDescription>
            </DialogHeader>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Token Name</FormLabel>
                      <FormControl>
                        <Input placeholder="My Application" {...field} />
                      </FormControl>
                      <FormDescription>
                        A descriptive name to identify this token
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description (Optional)</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Used for..." {...field} />
                      </FormControl>
                      <FormDescription>
                        A brief description of what this token is used for
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="expirationDays"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Expiration (Days): {field.value}</FormLabel>
                      <FormControl>
                        <Slider
                          min={1}
                          max={365}
                          step={1}
                          value={[field.value]}
                          onValueChange={(value) => field.onChange(value[0])}
                        />
                      </FormControl>
                      <FormDescription>
                        Number of days until this token expires
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <DialogFooter>
                  <Button type="button" variant="outline" onClick={() => setCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">Create Token</Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      {/* New Token Alert */}
      {newToken && (
        <Alert className="bg-green-50 border-green-200">
          <Key className="h-4 w-4 text-green-600" />
          <AlertTitle className="text-green-800">New API Token Created</AlertTitle>
          <AlertDescription className="text-green-700">
            <p className="mb-2">
              Your new API token has been created. Make sure to copy it now as you won't be able to see it again.
            </p>
            <div className="flex items-center gap-2 p-2 bg-green-100 rounded border border-green-300 mb-2">
              <code className="text-sm font-mono break-all">{newToken.tokenValue}</code>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-green-700"
                onClick={() => copyToken(newToken.tokenValue)}
              >
                {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
              </Button>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="text-green-700 border-green-300 hover:bg-green-100"
              onClick={() => setNewToken(null)}
            >
              I've copied my token
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* API Token Usage Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            How to Use API Tokens
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p>
              API tokens allow external applications to access your CMS data. Include the token in the request header:
            </p>
            <div className="bg-muted p-3 rounded-md">
              <code className="text-sm font-mono">X-API-Key: your_token_here</code>
            </div>
            <p>
              For security reasons:
            </p>
            <ul className="list-disc list-inside space-y-1">
              <li>Tokens are only shown once when created</li>
              <li>Set appropriate expiration dates</li>
              <li>Revoke tokens when they are no longer needed</li>
              <li>Don't share tokens in public repositories or insecure channels</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Tokens Table */}
      <Card>
        <CardHeader>
          <CardTitle>API Tokens</CardTitle>
          <CardDescription>
            Manage API tokens for external applications
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-4">Loading tokens...</div>
          ) : tokens.length === 0 ? (
            <div className="text-center py-4 text-muted-foreground">
              No API tokens found. Create your first token to get started.
            </div>
          ) : (
            <Table>
              <TableCaption>A list of your API tokens</TableCaption>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Token</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Expiration</TableHead>
                  <TableHead>Last Used</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tokens.map((token) => (
                  <TableRow key={token.id}>
                    <TableCell className="font-medium">
                      <div className="flex flex-col">
                        <span>{token.name}</span>
                        {token.description && (
                          <span className="text-xs text-muted-foreground">{token.description}</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="font-mono text-xs">{token.tokenValue}</TableCell>
                    <TableCell>
                      {!token.isActive ? (
                        <Badge variant="outline" className="bg-gray-100 text-gray-500">
                          Revoked
                        </Badge>
                      ) : isExpired(token.expiresAt) ? (
                        <Badge variant="outline" className="bg-amber-100 text-amber-700">
                          Expired
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="bg-green-100 text-green-700">
                          Active
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger className="flex items-center gap-1 text-sm">
                            <Calendar className="h-3.5 w-3.5" />
                            {formatDate(token.expiresAt)}
                          </TooltipTrigger>
                          <TooltipContent>
                            {isExpired(token.expiresAt)
                              ? 'This token has expired'
                              : `Expires in ${Math.ceil((new Date(token.expiresAt).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} days`
                            }
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell>
                      {token.lastUsedAt ? (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger className="flex items-center gap-1 text-sm">
                              <Clock className="h-3.5 w-3.5" />
                              {formatDate(token.lastUsedAt)}
                            </TooltipTrigger>
                            <TooltipContent>
                              Last used {formatDate(token.lastUsedAt)}
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      ) : (
                        <span className="text-muted-foreground text-sm">Never</span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      {token.isActive && !isExpired(token.expiresAt) && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 text-destructive hover:text-destructive hover:bg-destructive/10"
                          onClick={() => {
                            setTokenToRevoke(token);
                            setRevokeDialogOpen(true);
                          }}
                        >
                          Revoke
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Revoke Confirmation Dialog */}
      <Dialog open={revokeDialogOpen} onOpenChange={setRevokeDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              Revoke API Token
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to revoke the token "{tokenToRevoke?.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setRevokeDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleRevoke}>
              Revoke Token
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

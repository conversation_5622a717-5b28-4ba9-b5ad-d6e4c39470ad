package com.cms.service.impl;

import com.cms.entity.ComponentListing;
import com.cms.exception.ResourceNotFoundException;
import com.cms.repository.ComponentListingRepository;
import com.cms.service.ComponentListingService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class ComponentListingServiceImpl implements ComponentListingService {

    private final ComponentListingRepository componentListingRepository;

    @Override
    public List<ComponentListing> getAllComponents() {
        return componentListingRepository.findAll();
    }

    @Override
    public List<ComponentListing> getActiveComponents() {
        return componentListingRepository.findByIsActiveTrue();
    }

    @Override
    public Optional<ComponentListing> getComponentById(Integer id) {
        return componentListingRepository.findById(id);
    }

    @Override
    public Optional<ComponentListing> getComponentByApiId(String apiId) {
        return componentListingRepository.findByComponentApiId(apiId);
    }

    @Override
    public ComponentListing createComponent(ComponentListing component) {
        return componentListingRepository.save(component);
    }

    @Override
    public ComponentListing updateComponent(Integer id, ComponentListing component) {
        ComponentListing existingComponent = componentListingRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Component not found with id: " + id));

        existingComponent.setComponentName(component.getComponentName());
        existingComponent.setComponentDisplayName(component.getComponentDisplayName());
        existingComponent.setComponentApiId(component.getComponentApiId());
        existingComponent.setIsActive(component.getIsActive());
        existingComponent.setGetUrl(component.getGetUrl());
        existingComponent.setPostUrl(component.getPostUrl());
        existingComponent.setUpdateUrl(component.getUpdateUrl());

        return componentListingRepository.save(existingComponent);
    }

    @Override
    public void deleteComponent(Integer id) {
        ComponentListing component = componentListingRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Component not found with id: " + id));
        component.setIsActive(false);
        componentListingRepository.save(component);
    }



    @Override
    public boolean existsByComponentName(String componentName) {
        return componentListingRepository.existsByComponentName(componentName);
    }
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Domain-Based Tenant Management Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .response {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .instructions {
            background-color: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin-bottom: 20px;
        }
        .new-feature {
            background-color: #d1ecf1;
            border-left: 4px solid #17a2b8;
            padding: 15px;
            margin-bottom: 20px;
        }
        .users-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .users-table th, .users-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .users-table th {
            background-color: #f2f2f2;
        }
        code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
        .domain-preview {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🚀 Email Domain-Based Tenant Management Test</h1>

    <div class="new-feature">
        <h3>✨ New Feature: Email Domain-Based Tenant Assignment</h3>
        <p><strong>Automatic tenant creation from email domains!</strong></p>
        <ul>
            <li>📧 <code><EMAIL></code> → <code>acme_com</code> tenant schema</li>
            <li>🏢 <code><EMAIL></code> → <code>company_org</code> tenant schema</li>
            <li>👥 Multiple users from same domain share the same tenant</li>
            <li>⚙️ Override with custom tenant schema if needed</li>
        </ul>
    </div>

    <div class="instructions">
        <h3>📋 Testing Workflow</h3>
        <ol>
            <li><strong>🆕 Register with email domain:</strong> Use <code><EMAIL></code> → Creates <code>acme_com</code> tenant automatically</li>
            <li><strong>🆕 Add users by email domain:</strong> Use <code><EMAIL></code> → Auto-added to <code>acme_com</code> tenant</li>
            <li><strong>📋 List users:</strong> Use tenant schema <code>acme_com</code> to see all users</li>
            <li><strong>🔐 Login test:</strong> Users login with <code>username@schema</code> format</li>
            <li><strong>⚙️ Override:</strong> You can still specify custom tenant schemas if needed</li>
        </ol>
    </div>

    <div class="grid">
        <!-- Register (Create Tenant from Email Domain) -->
        <div class="container">
            <h2>👤 Register (Email Domain → Tenant)</h2>
            <div class="form-group">
                <label for="reg-username">Username:</label>
                <input type="text" id="reg-username" placeholder="admin" value="admin">
            </div>
            <div class="form-group">
                <label for="reg-email">Email (determines tenant):</label>
                <input type="email" id="reg-email" placeholder="<EMAIL>" value="<EMAIL>" oninput="updateDomainPreview('reg')">
                <div id="reg-domain-preview" class="domain-preview"></div>
            </div>
            <div class="form-group">
                <label for="reg-password">Password:</label>
                <input type="password" id="reg-password" placeholder="password123" value="password123">
            </div>
            <button onclick="register()" id="register-btn">Create Tenant from Email Domain</button>
            <div id="register-response" class="response"></div>
        </div>

        <!-- Add User (Auto-derive from Email) -->
        <div class="container">
            <h2>➕ Add User (Auto-derive Tenant)</h2>
            <div class="form-group">
                <label for="add-username">Username:</label>
                <input type="text" id="add-username" placeholder="john_doe" value="john_doe">
            </div>
            <div class="form-group">
                <label for="add-email">Email (auto-determines tenant):</label>
                <input type="email" id="add-email" placeholder="<EMAIL>" value="<EMAIL>" oninput="updateDomainPreview('add')">
                <div id="add-domain-preview" class="domain-preview"></div>
            </div>
            <div class="form-group">
                <label for="add-password">Password:</label>
                <input type="password" id="add-password" placeholder="password123" value="password123">
            </div>
            <div class="form-group">
                <label for="add-tenant">Custom Tenant (Optional):</label>
                <input type="text" id="add-tenant" placeholder="Leave empty to auto-derive" value="">
                <small style="color: #666;">Leave empty to auto-derive from email domain</small>
            </div>
            <button onclick="addUserToTenant()" id="add-user-btn">Add User (Auto-derive Tenant)</button>
            <div id="add-user-response" class="response"></div>
        </div>

        <!-- List Users in Tenant -->
        <div class="container">
            <h2>📋 List Users in Tenant</h2>
            <div class="form-group">
                <label for="list-tenant">Tenant Schema Name:</label>
                <input type="text" id="list-tenant" placeholder="acme_com" value="acme_com">
            </div>
            <button onclick="listUsersInTenant()" id="list-users-btn">List Users</button>
            <div id="list-users-response" class="response"></div>
            <div id="users-table-container"></div>
        </div>

        <!-- Login Test -->
        <div class="container">
            <h2>🔐 Login Test</h2>
            <div class="form-group">
                <label for="login-username">Username@Schema:</label>
                <input type="text" id="login-username" placeholder="admin@acme_com" value="admin@acme_com">
                <small style="color: #666;">Use the username you registered with + @ + tenant schema</small>
            </div>
            <div class="form-group">
                <label for="login-password">Password:</label>
                <input type="password" id="login-password" placeholder="password123" value="password123">
            </div>
            <button onclick="login()" id="login-btn">Login</button>
            <div id="login-response" class="response"></div>
        </div>
    </div>

    <!-- Examples Section -->
    <div class="container">
        <h2>📝 Example Scenarios</h2>
        <div class="grid">
            <div>
                <h4>🏢 Company A (acme.com)</h4>
                <ul style="font-size: 14px;">
                    <li>Register: <code><EMAIL></code> → <code>acme_com</code> tenant</li>
                    <li>Add: <code><EMAIL></code> → <code>acme_com</code> tenant</li>
                    <li>Add: <code><EMAIL></code> → <code>acme_com</code> tenant</li>
                    <li>Login: <code>john@acme_com</code></li>
                </ul>
            </div>
            <div>
                <h4>🏭 Company B (company.org)</h4>
                <ul style="font-size: 14px;">
                    <li>Register: <code><EMAIL></code> → <code>company_org</code> tenant</li>
                    <li>Add: <code><EMAIL></code> → <code>company_org</code> tenant</li>
                    <li>Add: <code><EMAIL></code> → <code>company_org</code> tenant</li>
                    <li>Login: <code>bob@company_org</code></li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:8071/api';
        let token = '';

        // Helper function to extract domain and convert to schema name
        function emailToSchemaName(email) {
            if (!email.includes('@')) return '';
            const domain = email.split('@')[1];
            return domain.toLowerCase().replace(/\./g, '_').replace(/[^a-z0-9_]/g, '_');
        }

        // Update domain preview
        function updateDomainPreview(prefix) {
            const emailInput = document.getElementById(`${prefix}-email`);
            const previewDiv = document.getElementById(`${prefix}-domain-preview`);

            if (emailInput.value && emailInput.value.includes('@')) {
                const schemaName = emailToSchemaName(emailInput.value);
                previewDiv.innerHTML = `🎯 Will use tenant schema: <strong>${schemaName}</strong>`;
                previewDiv.style.display = 'block';
            } else {
                previewDiv.style.display = 'none';
            }
        }

        // Initialize domain previews
        document.addEventListener('DOMContentLoaded', function() {
            updateDomainPreview('reg');
            updateDomainPreview('add');
        });

        // Helper function to update response display
        function updateResponse(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
            element.className = `response ${isSuccess ? 'success' : 'error'}`;
        }

        // Helper function to disable/enable button
        function setButtonState(buttonId, disabled, text) {
            const button = document.getElementById(buttonId);
            button.disabled = disabled;
            button.textContent = text;
        }

        // Register (Create Tenant from Email Domain)
        async function register() {
            const username = document.getElementById('reg-username').value;
            const email = document.getElementById('reg-email').value;
            const password = document.getElementById('reg-password').value;

            setButtonState('register-btn', true, 'Creating...');

            try {
                const response = await fetch(`${API_URL}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, email, password })
                });

                const data = await response.text();
                const schemaName = emailToSchemaName(email);
                updateResponse('register-response',
                    `${data}\n\n🎯 Created tenant schema: ${schemaName}\n📧 Email domain: ${email.split('@')[1]}`,
                    response.ok);
                console.log('Register response:', data);
            } catch (error) {
                updateResponse('register-response', `Error: ${error.message}`, false);
                console.error('Register error:', error);
            } finally {
                setButtonState('register-btn', false, 'Create Tenant from Email Domain');
            }
        }

        // Add User to Tenant (Auto-derive from Email)
        async function addUserToTenant() {
            const username = document.getElementById('add-username').value;
            const email = document.getElementById('add-email').value;
            const password = document.getElementById('add-password').value;
            const customTenant = document.getElementById('add-tenant').value.trim();

            setButtonState('add-user-btn', true, 'Adding User...');

            try {
                const requestBody = {
                    username,
                    email,
                    password
                };

                // Only include tenantSchemaName if custom tenant is specified
                if (customTenant) {
                    requestBody.tenantSchemaName = customTenant;
                }

                const response = await fetch(`${API_URL}/auth/add-user-to-tenant`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });

                const data = await response.text();
                const derivedSchema = emailToSchemaName(email);
                const usedSchema = customTenant || derivedSchema;

                updateResponse('add-user-response',
                    `${data}\n\n🎯 Used tenant schema: ${usedSchema}\n📧 Email domain: ${email.split('@')[1]}\n${customTenant ? '⚙️ Custom tenant specified' : '🤖 Auto-derived from email'}`,
                    response.ok);
                console.log('Add user response:', data);
            } catch (error) {
                updateResponse('add-user-response', `Error: ${error.message}`, false);
                console.error('Add user error:', error);
            } finally {
                setButtonState('add-user-btn', false, 'Add User (Auto-derive Tenant)');
            }
        }

        // List Users in Tenant
        async function listUsersInTenant() {
            const tenantSchemaName = document.getElementById('list-tenant').value;

            setButtonState('list-users-btn', true, 'Loading Users...');

            try {
                const response = await fetch(`${API_URL}/auth/list-users-in-tenant`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ tenantSchemaName })
                });

                const data = await response.json();
                updateResponse('list-users-response', `Found ${data.length} users in tenant: ${tenantSchemaName}`, response.ok);

                // Create users table
                if (response.ok && Array.isArray(data)) {
                    createUsersTable(data);
                }

                console.log('List users response:', data);
            } catch (error) {
                updateResponse('list-users-response', `Error: ${error.message}`, false);
                console.error('List users error:', error);
            } finally {
                setButtonState('list-users-btn', false, 'List Users');
            }
        }

        // Create users table
        function createUsersTable(users) {
            const container = document.getElementById('users-table-container');

            if (users.length === 0) {
                container.innerHTML = '<p>No users found in this tenant.</p>';
                return;
            }

            let tableHTML = `
                <table class="users-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Username</th>
                            <th>Email</th>
                            <th>Email Domain</th>
                            <th>Active</th>
                            <th>Created At</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            users.forEach(user => {
                const emailDomain = user.email.includes('@') ? user.email.split('@')[1] : 'N/A';
                tableHTML += `
                    <tr>
                        <td>${user.id}</td>
                        <td>${user.username}</td>
                        <td>${user.email}</td>
                        <td><code>${emailDomain}</code></td>
                        <td>${user.isActive ? '✅ Active' : '❌ Inactive'}</td>
                        <td>${new Date(user.createdAt).toLocaleString()}</td>
                    </tr>
                `;
            });

            tableHTML += `
                    </tbody>
                </table>
            `;

            container.innerHTML = tableHTML;
        }

        // Login
        async function login() {
            const username = document.getElementById('login-username').value;
            const password = document.getElementById('login-password').value;

            setButtonState('login-btn', true, 'Logging in...');

            try {
                const response = await fetch(`${API_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (response.ok) {
                    token = data.accessToken;
                    updateResponse('login-response', `✅ Login successful!\n🎫 Token: ${token.substring(0, 50)}...\n👤 User: ${username}`, true);
                } else {
                    updateResponse('login-response', data, false);
                }

                console.log('Login response:', data);
            } catch (error) {
                updateResponse('login-response', `Error: ${error.message}`, false);
                console.error('Login error:', error);
            } finally {
                setButtonState('login-btn', false, 'Login');
            }
        }

        // Initialize page
        console.log('Email Domain-Based Tenant Management Test Interface loaded');
        console.log('API URL:', API_URL);
    </script>
</body>
</html>

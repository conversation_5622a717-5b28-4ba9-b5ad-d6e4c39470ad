import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Components from './pages/Components';
import ComponentEdit from './pages/ComponentEdit';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/components" element={<Components />} />
        <Route path="/components/edit/:id" element={<ComponentEdit />} />
        <Route path="/" element={<Components />} />
      </Routes>
    </Router>
  );
}

export default App;

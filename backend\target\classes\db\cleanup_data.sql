-- Cleanup script to remove all auto-generated data from tables
-- This script preserves table structures but removes all data

-- Disable foreign key constraints temporarily
SET session_replication_role = 'replica';

-- Clean up collection_field_config table first (child table)
TRUNCATE TABLE collection_field_config CASCADE;

-- Clean up component_field_config table (child table)
TRUNCATE TABLE component_field_config CASCADE;

-- Clean up content_entries table (child table)
TRUNCATE TABLE content_entries CASCADE;

-- Clean up collection_fields table
TRUNCATE TABLE collection_fields CASCADE;

-- Clean up collection_components table
TRUNCATE TABLE collection_components CASCADE;

-- Clean up component_fields table
TRUNCATE TABLE component_fields CASCADE;

-- Clean up collection_listing table
TRUNCATE TABLE collection_listing CASCADE;

-- Clean up component_listing table
TRUNCATE TABLE component_listing CASCADE;

-- Clean up category table
TRUNCATE TABLE category CASCADE;

-- Re-enable foreign key constraints
SET session_replication_role = 'origin';

-- Reset sequences to start from initial values
ALTER SEQUENCE cms_category_seq RESTART WITH 100;
ALTER SEQUENCE cms_collection_listing_seq RESTART WITH 100;
ALTER SEQUENCE cms_collection_component_seq RESTART WITH 100;
ALTER SEQUENCE cms_colleciton_field_seq RESTART WITH 100;
ALTER SEQUENCE cms_collection_field_config_seq RESTART WITH 100;
ALTER SEQUENCE cms_component_listing_seq RESTART WITH 100;
ALTER SEQUENCE cms_component_field_seq RESTART WITH 100;
ALTER SEQUENCE cms_component_field_config_seq RESTART WITH 100;

-- Confirm completion
SELECT 'Database tables cleaned successfully' AS result;

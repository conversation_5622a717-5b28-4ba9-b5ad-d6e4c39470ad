import React, { useState, useEffect } from 'react';
import { Table, But<PERSON>, Card, Badge, Tooltip, Pagination, Input, Space, message } from 'antd';
import { EditOutlined, DeleteOutlined, PlusOutlined, SearchOutlined, LinkOutlined } from '@ant-design/icons';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import AddChildComponentModal from './AddChildComponentModal';
import '../styles/ComponentRelationships.css';

const { Search } = Input;

const ComponentsWithRelationships = () => {
  const [components, setComponents] = useState([]);
  const [relationships, setRelationships] = useState([]);
  const [relationshipsByParent, setRelationshipsByParent] = useState({});
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [addChildModalVisible, setAddChildModalVisible] = useState(false);
  const [selectedParentComponent, setSelectedParentComponent] = useState(null);

  const navigate = useNavigate();

  useEffect(() => {
    fetchComponents();
  }, []);

  const fetchComponents = async () => {
    setLoading(true);
    try {
      // Fetch all components
      const componentsResponse = await axios.get('/api/components/getAllWithRelationships');
      setComponents(componentsResponse.data);

      // Fetch component relationships
      const relationshipsResponse = await axios.get('/api/component-components/getAllWithRelationships');
      setRelationships(relationshipsResponse.data.relationships);
      setRelationshipsByParent(relationshipsResponse.data.relationshipsByParent);

      setPagination({
        ...pagination,
        total: componentsResponse.data.length,
      });
    } catch (error) {
      console.error('Error fetching components:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (value) => {
    setSearchText(value);
  };

  const handleTableChange = (pagination) => {
    setPagination(pagination);
  };

  const handleCreateComponent = () => {
    navigate('/components/create');
  };

  const handleEditComponent = (id) => {
    navigate(`/components/edit/${id}`);
  };

  const handleDeleteComponent = async (id) => {
    try {
      await axios.delete(`/api/components/delete/${id}`);
      fetchComponents();
      message.success('Component deleted successfully');
    } catch (error) {
      console.error('Error deleting component:', error);
      message.error('Failed to delete component');
    }
  };

  const handleAddChildComponent = (component) => {
    setSelectedParentComponent(component);
    setAddChildModalVisible(true);
  };

  const handleAddChildSuccess = () => {
    setAddChildModalVisible(false);
    fetchComponents();
    message.success('Child component added successfully');
  };

  const handleAddChildCancel = () => {
    setAddChildModalVisible(false);
  };

  const getChildComponents = (component) => {
    // If the component has childComponents property directly from the API
    if (component.childComponents && Array.isArray(component.childComponents)) {
      return component.childComponents;
    }

    // Fallback to the old way using relationshipsByParent
    if (!relationshipsByParent[component.id]) {
      return [];
    }
    return relationshipsByParent[component.id];
  };

  const filteredComponents = components.filter((component) => {
    return (
      component.componentName.toLowerCase().includes(searchText.toLowerCase()) ||
      component.componentDisplayName.toLowerCase().includes(searchText.toLowerCase()) ||
      component.componentApiId.toLowerCase().includes(searchText.toLowerCase())
    );
  });

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: 'Component Name',
      dataIndex: 'componentName',
      key: 'componentName',
      render: (text, record) => (
        <div>
          <div>{text}</div>
          <div className="text-muted small">API ID: {record.componentApiId}</div>
        </div>
      ),
    },
    {
      title: 'Display Name',
      dataIndex: 'componentDisplayName',
      key: 'componentDisplayName',
    },
    {
      title: 'Child Components',
      key: 'childComponents',
      render: (_, record) => {
        const childComponents = getChildComponents(record);
        return (
          <div className="component-relationship-container">
            {childComponents && childComponents.length > 0 ? (
              childComponents.map((rel) => {
                // Handle both formats (direct API response and relationships from separate endpoint)
                const childComponent = rel.childComponent || rel;
                const isRepeatable = rel.isRepeatable || false;
                const minRepeatOccurrences = rel.minRepeatOccurrences || 0;
                const maxRepeatOccurrences = rel.maxRepeatOccurrences || 'unlimited';
                const displayName = childComponent.componentDisplayName ||
                                   (childComponent.childComponent && childComponent.childComponent.componentDisplayName) ||
                                   'Unknown';

                return (
                  <span
                    key={rel.id}
                    className={`component-badge ${isRepeatable ? 'repeatable' : ''}`}
                    title={isRepeatable ? `Repeatable (${minRepeatOccurrences}-${maxRepeatOccurrences})` : 'Not repeatable'}
                  >
                    {displayName}
                    {isRepeatable && ' (R)'}
                  </span>
                );
              })
            ) : (
              <span className="text-muted">No child components</span>
            )}
          </div>
        );
      },
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 100,
      render: (isActive) => (
        <Badge
          status={isActive ? 'success' : 'error'}
          text={isActive ? 'Active' : 'Inactive'}
        />
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space>
          <Tooltip title="Add Child Component">
            <Button
              icon={<LinkOutlined />}
              size="small"
              type="primary"
              ghost
              onClick={() => handleAddChildComponent(record)}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEditComponent(record.id)}
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Button
              icon={<DeleteOutlined />}
              size="small"
              danger
              onClick={() => handleDeleteComponent(record.id)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Card title="Components" extra={
      <Space>
        <Search
          placeholder="Search components..."
          allowClear
          onSearch={handleSearch}
          onChange={(e) => handleSearch(e.target.value)}
          style={{ width: 250 }}
        />
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleCreateComponent}
        >
          Create new component
        </Button>
      </Space>
    }>
      <Table
        columns={columns}
        dataSource={filteredComponents}
        rowKey="id"
        pagination={pagination}
        onChange={handleTableChange}
        loading={loading}
      />

      {selectedParentComponent && (
        <AddChildComponentModal
          visible={addChildModalVisible}
          parentComponent={selectedParentComponent}
          onCancel={handleAddChildCancel}
          onSuccess={handleAddChildSuccess}
        />
      )}
    </Card>
  );
};

export default ComponentsWithRelationships;

# Email Domain-Based Tenant Management

## 🚀 Overview

The CMS application now supports **automatic tenant schema creation and assignment based on email domains**. This revolutionary feature simplifies multi-user onboarding and makes tenant management more intuitive and scalable.

## ✨ Key Features

### 1. **Automatic Tenant Schema Derivation**
- Email domains automatically determine tenant schemas
- `<EMAIL>` → `acme_com` schema
- `<EMAIL>` → `company_org` schema
- Multiple users from the same domain share the same tenant

### 2. **Intelligent Schema Naming**
- Dots (`.`) are replaced with underscores (`_`)
- Hyphens (`-`) are replaced with underscores (`_`)
- Special characters are sanitized
- Schema names are validated for database compatibility

### 3. **Backward Compatibility**
- Original `username@schema` format still works
- Explicit tenant specification takes precedence over email domain
- Existing functionality remains unchanged

### 4. **Flexible Override**
- Can still specify custom tenant schemas when needed
- Email domain derivation is used as fallback

## 🔧 Implementation Details

### Backend Changes

#### 1. **TenantUtils Enhancements**
```java
// New methods added:
public String extractTenantSchemaFromEmail(String email)
public String sanitizeDomainForSchema(String domain)
public String determineTenantSchemaForRegistration(String username, String email)
```

#### 2. **AuthController Updates**
- Registration now uses email domain for tenant determination
- Add-user-to-tenant supports optional tenant schema (auto-derives if not provided)
- Enhanced logging and error handling

#### 3. **Request Payload Changes**
- `AddUserToTenantRequest.tenantSchemaName` is now optional
- Backend auto-derives from email if not provided

### Frontend Changes

#### 1. **TenantUserManagement Component**
- Real-time email domain preview
- Optional tenant schema field
- Enhanced user experience with auto-suggestions
- Comprehensive usage instructions

#### 2. **Form Validation**
- Updated Zod schemas to make tenant schema optional
- Email validation with domain extraction
- User-friendly error messages

## 📋 Usage Examples

### Example 1: Company Registration Flow

```bash
# 1. Register first user (creates tenant)
POST /api/auth/register
{
  "username": "admin",
  "email": "<EMAIL>",
  "password": "admin123"
}
# Creates tenant schema: acme_com

# 2. Add more users (auto-assigned to same tenant)
POST /api/auth/add-user-to-tenant
{
  "username": "john_doe",
  "email": "<EMAIL>",
  "password": "password123"
  // tenantSchemaName omitted - auto-derived as acme_com
}

# 3. Add another user
POST /api/auth/add-user-to-tenant
{
  "username": "jane_smith",
  "email": "<EMAIL>",
  "password": "password456"
  // tenantSchemaName omitted - auto-derived as acme_com
}

# 4. List all users in tenant
POST /api/auth/list-users-in-tenant
{
  "tenantSchemaName": "acme_com"
}

# 5. Users login with username@schema format
POST /api/auth/login
{
  "username": "john_doe@acme_com",
  "password": "password123"
}
```

### Example 2: Multiple Organizations

```bash
# Organization A: acme.com
<EMAIL> → acme_com tenant
<EMAIL> → acme_com tenant
<EMAIL> → acme_com tenant

# Organization B: company.org
<EMAIL> → company_org tenant
<EMAIL> → company_org tenant
<EMAIL> → company_org tenant

# Organization C: startup.io
<EMAIL> → startup_io tenant
<EMAIL> → startup_io tenant
```

## 🎯 Schema Naming Rules

### Domain Sanitization Process
1. Convert to lowercase
2. Replace dots (`.`) with underscores (`_`)
3. Replace hyphens (`-`) with underscores (`_`)
4. Remove/replace other special characters
5. Remove consecutive underscores
6. Ensure starts with letter or underscore
7. Limit to 60 characters (PostgreSQL limit)

### Examples
```
acme.com → acme_com
company.org → company_org
my-startup.io → my_startup_io
test-site.co.uk → test_site_co_uk
123domain.com → tenant_123domain_com (prefixed for valid schema name)
```

## 🔄 Migration Guide

### For Existing Users
- No changes required for existing tenants
- Existing users continue to work as before
- New users can be added using either method

### For New Implementations
1. Use email-based registration for new tenants
2. Add users by email domain (omit tenantSchemaName)
3. Override with custom tenant only when necessary

## 🧪 Testing

### Test Files
- `backend/test-email-domain-tenants.html` - Interactive test interface
- `backend/test-tenant-user-management.html` - Original test interface

### Test Scenarios
1. **Email Domain Registration**: Register users with different email domains
2. **Auto-Assignment**: Add users without specifying tenant schema
3. **Custom Override**: Add users with explicit tenant schema
4. **Multi-Domain**: Test multiple organizations simultaneously
5. **Edge Cases**: Test domain sanitization with special characters

## 🚨 Important Notes

### Security Considerations
- Email domains are sanitized for database safety
- Tenant isolation is maintained
- No cross-tenant data leakage

### Performance Impact
- Minimal overhead for domain extraction
- Database schema creation only when needed
- Efficient tenant context switching

### Limitations
- Email domain must be valid
- Schema names limited to 60 characters
- Cannot change tenant assignment after creation

## 🔍 Troubleshooting

### Common Issues

#### 1. Invalid Email Domain
**Problem**: Email domain cannot be converted to valid schema name
**Solution**: Provide explicit tenantSchemaName in request

#### 2. Schema Name Too Long
**Problem**: Domain results in schema name > 60 characters
**Solution**: Use custom tenant schema name

#### 3. Special Characters in Domain
**Problem**: Domain contains unsupported characters
**Solution**: Automatic sanitization handles most cases

### Debug Information
- Check application logs for tenant determination logic
- Verify email domain extraction in browser console
- Use test interface for interactive debugging

## 📈 Benefits

### For Developers
- Simplified tenant management
- Reduced configuration overhead
- Intuitive user onboarding

### For Organizations
- Natural tenant grouping by email domain
- Easier user management
- Scalable multi-tenant architecture

### For End Users
- Seamless registration process
- Logical tenant assignment
- Consistent user experience

## 🔮 Future Enhancements

### Planned Features
- Bulk user import by email domain
- Tenant domain verification
- Custom domain mapping
- Advanced tenant analytics

### Potential Improvements
- Email domain whitelist/blacklist
- Tenant capacity limits
- Domain-based access controls
- Automated tenant provisioning

---

**Note**: This feature maintains full backward compatibility while providing a more intuitive and scalable approach to multi-tenant user management.

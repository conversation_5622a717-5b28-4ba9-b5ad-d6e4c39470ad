package com.cms.dto;

import com.cms.entity.ComponentListing;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;

/**
 * DTO for creating a new component without fields
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Data transfer object for creating a new component")
public class ComponentCreateDTO {

    @Schema(description = "Component name", example = "Blog Post", required = true)
    @NotBlank(message = "Component name is required")
    private String componentName;

    @Schema(description = "Component display name", example = "Blog Post Component")
    private String componentDisplayName;

    @Schema(description = "Component API ID", example = "blog_post")
    private String componentApiId;

    @Schema(description = "Whether the component is active", example = "true")
    private Boolean isActive = true;

    @Schema(description = "GET URL for the component", example = "/api/blog-posts")
    private String getUrl;

    @Schema(description = "POST URL for the component", example = "/api/blog-posts/create")
    private String postUrl;

    @Schema(description = "UPDATE URL for the component", example = "/api/blog-posts/update")
    private String updateUrl;

    /**
     * Convert this DTO to a ComponentListing entity
     * @return ComponentListing entity
     */
    public ComponentListing toEntity() {
        ComponentListing entity = new ComponentListing();
        entity.setComponentName(this.componentName);
        entity.setComponentDisplayName(this.componentDisplayName);
        entity.setComponentApiId(this.componentApiId);
        entity.setIsActive(this.isActive);
        entity.setGetUrl(this.getUrl);
        entity.setPostUrl(this.postUrl);
        entity.setUpdateUrl(this.updateUrl);
        return entity;
    }
}

package com.cms.controller;

import com.cms.util.DatabaseCleanupUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Controller for database cleanup operations
 * This should only be used in development environments
 */
@RestController
@RequestMapping("/admin/database")
@RequiredArgsConstructor
@Tag(name = "Database Admin", description = "Database administration operations")
public class DatabaseCleanupController {

    private final DatabaseCleanupUtil databaseCleanupUtil;

    @PostMapping("/cleanup")
    @Operation(summary = "Clean up database tables", 
               description = "Removes all data from category, collection, and component tables")
    public ResponseEntity<Map<String, String>> cleanupDatabase() {
        databaseCleanupUtil.cleanupDatabase();
        
        Map<String, String> response = new HashMap<>();
        response.put("status", "success");
        response.put("message", "Database tables cleaned successfully");
        
        return ResponseEntity.ok(response);
    }
}

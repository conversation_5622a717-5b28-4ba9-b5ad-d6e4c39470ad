package com.cms.controller;

import com.cms.payload.ReorderItemsRequest;
import com.cms.service.CollectionOrderingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/collections/{collectionId}/ordering")
@RequiredArgsConstructor
@Tag(name = "Collection Ordering", description = "API for managing the order of collection components and fields")
public class CollectionOrderingController {

    private final CollectionOrderingService collectionOrderingService;

    @GetMapping
    @Operation(summary = "Get ordered collection items", description = "Get all components and fields for a collection in display order")
    public ResponseEntity<Map<String, Object>> getOrderedCollectionItems(@PathVariable Integer collectionId) {
        Map<String, Object> result = collectionOrderingService.getOrderedCollectionItems(collectionId);
        return ResponseEntity.ok(result);
    }

    @PutMapping
    @Operation(summary = "Reorder collection items", description = "Reorder components and fields for a collection")
    public ResponseEntity<Map<String, Object>> reorderCollectionItems(
            @PathVariable Integer collectionId,
            @RequestBody ReorderItemsRequest request) {
        
        Map<String, Object> result = collectionOrderingService.reorderCollectionItems(
                collectionId,
                request.getComponentIds(),
                request.getFieldIds()
        );
        
        return ResponseEntity.ok(result);
    }
}

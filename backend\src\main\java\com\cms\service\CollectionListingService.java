package com.cms.service;

import com.cms.entity.CollectionListing;

import java.util.List;
import java.util.Optional;

public interface CollectionListingService {
    List<CollectionListing> getAllCollections();
    List<CollectionListing> getAllCollectionsWithDetails();
    Optional<CollectionListing> getCollectionById(Integer id);
    Optional<CollectionListing> getCollectionByIdWithDetails(Integer id);
    Optional<CollectionListing> getCollectionByApiId(String apiId);
    Optional<CollectionListing> getCollectionByApiIdWithDetails(String apiId);
    CollectionListing createCollection(CollectionListing collection);
    CollectionListing updateCollection(Integer id, CollectionListing collection);
    void deleteCollection(Integer id);
    boolean existsByCollectionName(String collectionName);

    /**
     * Check if a collection with the given name exists in the specified category
     *
     * @param collectionName the name of the collection
     * @param categoryId the ID of the category
     * @return true if a collection with the name exists in the category, false otherwise
     */
    boolean existsByCollectionNameAndCategoryId(String collectionName, Integer categoryId);

    /**
     * Get all collections that belong to categories associated with a specific client
     *
     * @param clientId the ID of the client
     * @return list of collections
     */
    List<CollectionListing> getCollectionsByClientId(Integer clientId);

    /**
     * Get all collections that belong to categories associated with a specific client, with details
     *
     * @param clientId the ID of the client
     * @return list of collections with details
     */
    List<CollectionListing> getCollectionsByClientIdWithDetails(Integer clientId);

    /**
     * Get all collections that belong to a specific category
     *
     * @param categoryId the ID of the category
     * @return list of collections
     */
    List<CollectionListing> getCollectionsByCategoryId(Integer categoryId);

    /**
     * Get all collections that belong to a specific client and category, with details
     *
     * @param clientId   the ID of the client (mandatory)
     * @param categoryId the ID of the category (optional, but used here)
     * @return list of collections with details
     */
    List<CollectionListing> getCollectionsByClientIdAndCategoryIdWithDetails(Integer clientId, Integer categoryId);

    /**
     * Get all collections that belong to a specific client and category (by name), with details
     *
     * @param clientId     the ID of the client (mandatory)
     * @param categoryName the name of the category (mandatory)
     * @return list of collections with details
     */
    List<CollectionListing> getCollectionsByClientIdAndCategoryNameWithDetails(Integer clientId, String categoryName);
    List<CollectionListing> getCollectionsByClientNameAndCategoryNameWithDetails(String clientName, String categoryName);
    List<CollectionListing> getCollectionsByClientNameAndCategoryNameHierarchical(String clientName, String categoryName);
}

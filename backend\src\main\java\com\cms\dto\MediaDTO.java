package com.cms.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MediaDTO {
    private Integer id;
    private String fileName;
    private String originalFileName;
    private String fileType;
    private Long fileSize;
    private Integer width;
    private Integer height;
    private Integer duration;
    private String altText;
    private String description;
    private String publicUrl;
    private Boolean isPublic;
    private Integer folderId;
    private String folderName;
    private String uploadedByUsername;
    private LocalDateTime createdAt;
    private LocalDateTime modifiedAt;
    private String shareUrl;
}

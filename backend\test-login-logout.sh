#!/bin/bash

# Test script for login/logout functionality
# This script tests the login/logout cycle to identify the "bad credentials" issue

BASE_URL="http://localhost:8071"
USERNAME="admin@public"
PASSWORD="password"

echo "=== Testing Login/Logout Cycle ==="
echo "Base URL: $BASE_URL"
echo "Username: $USERNAME"
echo ""

# Function to make login request
login() {
    echo "--- Attempting Login ---"
    RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$USERNAME\",\"password\":\"$PASSWORD\"}" \
        "$BASE_URL/auth/login")
    
    HTTP_CODE=$(echo "$RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
    BODY=$(echo "$RESPONSE" | sed '/HTTP_CODE:/d')
    
    echo "HTTP Code: $HTTP_CODE"
    echo "Response: $BODY"
    
    if [ "$HTTP_CODE" = "200" ]; then
        TOKEN=$(echo "$BODY" | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)
        echo "Login successful! Token: ${TOKEN:0:50}..."
        return 0
    else
        echo "Login failed!"
        return 1
    fi
}

# Function to make logout request
logout() {
    echo "--- Attempting Logout ---"
    RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        "$BASE_URL/auth/logout")
    
    HTTP_CODE=$(echo "$RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
    BODY=$(echo "$RESPONSE" | sed '/HTTP_CODE:/d')
    
    echo "HTTP Code: $HTTP_CODE"
    echo "Response: $BODY"
    
    if [ "$HTTP_CODE" = "200" ]; then
        echo "Logout successful!"
        return 0
    else
        echo "Logout failed!"
        return 1
    fi
}

# Function to check logged-in users
check_logged_in_users() {
    echo "--- Checking Logged-in Users ---"
    RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X GET \
        -H "Authorization: Bearer $TOKEN" \
        "$BASE_URL/auth/logged-in-users")
    
    HTTP_CODE=$(echo "$RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
    BODY=$(echo "$RESPONSE" | sed '/HTTP_CODE:/d')
    
    echo "HTTP Code: $HTTP_CODE"
    echo "Response: $BODY"
}

# Test cycle
echo "=== Test 1: Initial Login ==="
if login; then
    echo ""
    check_logged_in_users
    echo ""
    
    echo "=== Test 2: Logout ==="
    logout
    echo ""
    
    echo "=== Test 3: Re-login (This should work but might fail) ==="
    if login; then
        echo "SUCCESS: Re-login worked!"
        echo ""
        check_logged_in_users
    else
        echo "FAILURE: Re-login failed with bad credentials!"
        echo ""
        echo "=== Debugging Information ==="
        echo "This suggests there might be an issue with:"
        echo "1. Tenant context not being cleared properly during logout"
        echo "2. Database connection issues"
        echo "3. User lookup in wrong tenant schema"
        echo ""
        echo "Check the application logs for more details."
    fi
else
    echo "FAILURE: Initial login failed!"
fi

echo ""
echo "=== Test Complete ==="

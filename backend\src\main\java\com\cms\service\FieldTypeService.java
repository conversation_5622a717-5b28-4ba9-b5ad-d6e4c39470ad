package com.cms.service;

import com.cms.entity.FieldType;

import java.util.List;
import java.util.Optional;

public interface FieldTypeService {
    List<FieldType> getAllFieldTypes();
    List<FieldType> getActiveFieldTypes();
    Optional<FieldType> getFieldTypeById(Integer id);
    FieldType createFieldType(FieldType fieldType);
    FieldType updateFieldType(Integer id, FieldType fieldType);
    void deleteFieldType(Integer id);
    boolean existsByFieldTypeName(String fieldTypeName);
    boolean existsById(Integer id);
}

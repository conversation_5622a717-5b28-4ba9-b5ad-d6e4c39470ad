package com.cms.util;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.FileCopyUtils;

import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;

/**
 * Utility class to execute database migrations on application startup.
 * This will run the SQL migration script to remove unique constraints from all tenant schemas.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DatabaseMigrationExecutor implements CommandLineRunner {

    private final JdbcTemplate jdbcTemplate;

    @Override
    public void run(String... args) {
        try {
            log.info("Executing SQL migration script to remove unique constraints...");
            
            // Load the SQL script from the classpath
            ClassPathResource resource = new ClassPathResource("db/migration/V1__remove_unique_constraints.sql");
            String sql = readResourceAsString(resource);
            
            // Execute the SQL script
            jdbcTemplate.execute(sql);
            
            log.info("Migration completed successfully.");
        } catch (Exception e) {
            log.error("Error executing migration script: {}", e.getMessage(), e);
        }
    }
    
    private String readResourceAsString(ClassPathResource resource) throws IOException {
        try (Reader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8)) {
            return FileCopyUtils.copyToString(reader);
        }
    }
}

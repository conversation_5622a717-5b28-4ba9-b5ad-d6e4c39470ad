package com.cms.repository;

import com.cms.entity.FieldConfig;
import com.cms.entity.FieldType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

@Repository
public interface FieldConfigRepository extends JpaRepository<FieldConfig, Integer> {
    List<FieldConfig> findByFieldTypeAndIsActiveTrue(FieldType fieldType);
    List<FieldConfig> findByIsActiveTrue();
    boolean existsByConfigName(String configName);
    @Query("SELECT fc FROM FieldConfig fc WHERE fc.id = :id")
    Optional<FieldConfig> findByConfigId(@Param("id") Integer id);

    @Query("SELECT fc FROM FieldConfig fc WHERE fc.fieldType.id = :fieldTypeId AND fc.isActive = true")
    List<FieldConfig> findByFieldTypeIdAndIsActiveTrue(@Param("fieldTypeId") Integer fieldTypeId);

    List<FieldConfig> findByConfigName(String configName);
}

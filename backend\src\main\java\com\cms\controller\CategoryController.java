package com.cms.controller;

import com.cms.config.TenantContextHolder;
import com.cms.dto.CategoryDTO;
import com.cms.entity.Category;
import com.cms.exception.NullConstraintViolationException;
import com.cms.exception.ResourceNotFoundException;
import com.cms.exception.UniqueConstraintViolationException;
import com.cms.mapper.CategoryMapper;
import com.cms.service.CategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/categories")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Category", description = "Category API")
public class CategoryController {

    private final CategoryService categoryService;
    private final CategoryMapper categoryMapper;

    @GetMapping("/getAll")
    @Operation(summary = "Get all categories", description = "Returns a list of all categories for the current tenant")
    public ResponseEntity<List<CategoryDTO>> getAllCategories() {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("CategoryController.getAllCategories() called for tenant: {}", currentTenant);

        List<Category> categories = categoryService.getAllCategories();
        List<CategoryDTO> categoryDTOs = categoryMapper.toDTOList(categories);

        log.info("CategoryController.getAllCategories() returning {} categories for tenant: {}",
                categories.size(), currentTenant);

        return ResponseEntity.ok(categoryDTOs);
    }

    @GetMapping("/getById/{id}")
    @Operation(summary = "Get category by ID", description = "Returns a category by its ID or 204 No Content if not found")
    public ResponseEntity<CategoryDTO> getCategoryById(@PathVariable Integer id) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("CategoryController.getCategoryById() called with ID: {} for tenant: {}", id, currentTenant);

        return categoryService.getCategoryById(id)
                .map(category -> {
                    log.info("Category found with ID: {} for tenant: {}", id, currentTenant);
                    CategoryDTO categoryDTO = categoryMapper.toDTO(category);
                    return ResponseEntity.ok(categoryDTO);
                })
                .orElse(ResponseEntity.noContent().build()); // Return 204 No Content instead of 404 Not Found
    }

    @GetMapping("/getByName/{name}")
    @Operation(summary = "Get category by name", description = "Returns a category by its name or 204 No Content if not found")
    public ResponseEntity<CategoryDTO> getCategoryByName(@PathVariable String name) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("CategoryController.getCategoryByName() called with name: {} for tenant: {}", name, currentTenant);

        return categoryService.getCategoryByName(name)
                .map(category -> {
                    log.info("Category found with name: {} for tenant: {}", name, currentTenant);
                    CategoryDTO categoryDTO = categoryMapper.toDTO(category);
                    return ResponseEntity.ok(categoryDTO);
                })
                .orElse(ResponseEntity.noContent().build()); // Return 204 No Content instead of 404 Not Found
    }

    @PostMapping("/create")
    @Operation(summary = "Create a new category", description = "Creates a new category in the current tenant")
    public ResponseEntity<CategoryDTO> createCategory(@Valid @RequestBody CategoryDTO categoryDTO) {
        String currentTenant = TenantContextHolder.getTenantId();
        try {
            // Log the incoming request
            log.info("Received request to create category: {} for tenant: {}", categoryDTO, currentTenant);

            // Check if category is null
            if (categoryDTO == null) {
                log.warn("Category is null for tenant: {}", currentTenant);
                // Throw NullConstraintViolationException to trigger 700 status code
                throw new NullConstraintViolationException("category");
            }

            // Check if category name is null
            if (categoryDTO.getCategoryName() == null) {
                log.warn("Category name is null for tenant: {}", currentTenant);
                // Throw NullConstraintViolationException to trigger 700 status code
                throw new NullConstraintViolationException("categoryName");
            }

            // Note: Removed unique category name validation to allow duplicate names across different clients

            // Convert DTO to entity
            Category category = categoryMapper.toEntity(categoryDTO);

            // Set the next available ID if not provided
            if (category.getId() == null) {
                Integer nextId = categoryService.getNextAvailableId();
                log.info("Assigning next available ID: {} for tenant: {}", nextId, currentTenant);
                category.setId(nextId);
            }

            // If client is provided in the request, validate and use it
            if (categoryDTO.getClient() != null && categoryDTO.getClient().getId() != null) {
                log.info("Client information provided in request: {}", categoryDTO.getClient());
                // The client entity will be properly associated in the service layer
            }

            Category createdCategory = categoryService.createCategory(category);
            CategoryDTO createdCategoryDTO = categoryMapper.toDTO(createdCategory);

            log.info("Category created successfully: {} for tenant: {}", createdCategory, currentTenant);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdCategoryDTO);
        } catch (Exception e) {
            log.error("Error creating category for tenant {}: {}", currentTenant, e.getMessage(), e);
            throw e;
        }
    }

    @PutMapping("/update/{id}")
    @Operation(summary = "Update a category", description = "Updates an existing category in the current tenant")
    public ResponseEntity<CategoryDTO> updateCategory(@PathVariable Integer id, @Valid @RequestBody CategoryDTO categoryDTO) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Updating category with ID: {} for tenant: {}", id, currentTenant);

        // Check if category is null
        if (categoryDTO == null) {
            log.warn("Category is null for tenant: {}", currentTenant);
            // Throw NullConstraintViolationException to trigger 700 status code
            throw new NullConstraintViolationException("category");
        }

        // Check if category name is null
        if (categoryDTO.getCategoryName() == null) {
            log.warn("Category name is null for tenant: {}", currentTenant);
            // Throw NullConstraintViolationException to trigger 700 status code
            throw new NullConstraintViolationException("categoryName");
        }

        // Note: Removed unique category name validation to allow duplicate names across different clients

        // Convert DTO to entity
        Category category = categoryMapper.toEntity(categoryDTO);

        // If client is provided in the request, validate and use it
        if (categoryDTO.getClient() != null && categoryDTO.getClient().getId() != null) {
            log.info("Client information provided in update request: {}", categoryDTO.getClient());
            // The client entity will be properly associated in the service layer
        }

        Category updatedCategory = categoryService.updateCategory(id, category);
        CategoryDTO updatedCategoryDTO = categoryMapper.toDTO(updatedCategory);

        log.info("Category updated successfully: {} for tenant: {}", updatedCategory, currentTenant);
        return ResponseEntity.ok(updatedCategoryDTO);
    }

    @DeleteMapping("/deleteById/{id}")
    @Operation(summary = "Delete a category", description = "Deletes a category from the current tenant and returns 204 with a success or no content message")
    public ResponseEntity<Map<String, String>> deleteCategory(@PathVariable Integer id) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Deleting category with ID: {} for tenant: {}", id, currentTenant);

        // Check if the category exists
        boolean exists = categoryService.getCategoryById(id).isPresent();

        if (exists) {
            try {
                // Delete the category
                categoryService.deleteCategory(id);
                // Return 204 with success message
                log.info("Successfully deleted category with ID: {} for tenant: {}", id, currentTenant);
                return ResponseEntity.status(HttpStatus.NO_CONTENT)
                        .body(Map.of("message", "Successfully deleted category with id: " + id));
            } catch (Exception e) {
                // If deletion fails, return an error message
                log.error("Failed to delete category with ID: {} for tenant: {}: {}",
                        id, currentTenant, e.getMessage(), e);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(Map.of("error", "Failed to delete category with id: " + id + ". Error: " + e.getMessage()));
            }
        } else {
            // Return 204 with no content message
            log.info("No category found with ID: {} for tenant: {}", id, currentTenant);
            return ResponseEntity.status(HttpStatus.NO_CONTENT)
                    .body(Map.of("message", "No category found with id: " + id));
        }
    }

    // Client-related endpoints

    @GetMapping("/client/{clientId}")
    @Operation(summary = "Get categories by client ID", description = "Returns a list of categories for a specific client")
    public ResponseEntity<List<CategoryDTO>> getCategoriesByClientId(@PathVariable Integer clientId) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Getting categories for client ID: {} in tenant: {}", clientId, currentTenant);

        List<Category> categories = categoryService.getCategoriesByClientId(clientId);
        List<CategoryDTO> categoryDTOs = categoryMapper.toDTOList(categories);

        return ResponseEntity.ok(categoryDTOs);
    }

    @PostMapping("/client/{clientId}/create")
    @Operation(summary = "Create a category for a client", description = "Creates a new category for a specific client")
    public ResponseEntity<CategoryDTO> createCategoryForClient(
            @PathVariable Integer clientId,
            @Valid @RequestBody CategoryDTO categoryDTO) {

        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Creating category for client ID: {} in tenant: {}", clientId, currentTenant);

        // Validate category
        if (categoryDTO == null) {
            throw new NullConstraintViolationException("category");
        }

        if (categoryDTO.getCategoryName() == null) {
            throw new NullConstraintViolationException("categoryName");
        }

        // Check if category with the same name already exists
        if (categoryService.existsByCategoryName(categoryDTO.getCategoryName())) {
            throw new UniqueConstraintViolationException("categoryName", categoryDTO.getCategoryName());
        }

        // Convert DTO to entity
        Category category = categoryMapper.toEntity(categoryDTO);

        // Set the next available ID if not provided
        if (category.getId() == null) {
            Integer nextId = categoryService.getNextAvailableId();
            category.setId(nextId);
        }

        // Create the category for the client
        Category createdCategory = categoryService.createCategoryForClient(category, clientId);
        CategoryDTO createdCategoryDTO = categoryMapper.toDTO(createdCategory);

        return ResponseEntity.status(HttpStatus.CREATED).body(createdCategoryDTO);
    }

    @PutMapping("/client/{clientId}/update/{id}")
    @Operation(summary = "Update a category for a client", description = "Updates an existing category for a specific client")
    public ResponseEntity<CategoryDTO> updateCategoryForClient(
            @PathVariable Integer clientId,
            @PathVariable Integer id,
            @Valid @RequestBody CategoryDTO categoryDTO) {

        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Updating category ID: {} for client ID: {} in tenant: {}", id, clientId, currentTenant);

        // Validate category
        if (categoryDTO == null) {
            throw new NullConstraintViolationException("category");
        }

        if (categoryDTO.getCategoryName() == null) {
            throw new NullConstraintViolationException("categoryName");
        }

        // Check if another category with the same name already exists (excluding the current one)
        if (categoryService.existsByCategoryName(categoryDTO.getCategoryName()) &&
                !categoryService.getCategoryById(id).get().getCategoryName().equals(categoryDTO.getCategoryName())) {
            throw new UniqueConstraintViolationException("categoryName", categoryDTO.getCategoryName());
        }

        // Convert DTO to entity
        Category category = categoryMapper.toEntity(categoryDTO);

        // Update the category for the client
        Category updatedCategory = categoryService.updateCategoryWithClient(id, category, clientId);
        CategoryDTO updatedCategoryDTO = categoryMapper.toDTO(updatedCategory);

        return ResponseEntity.ok(updatedCategoryDTO);
    }
    @GetMapping("/getByParentId/{parentId}")
    @Operation(summary = "Get categories by parent category ID", description = "Returns a list of categories that have the specified parent category ID")
    public ResponseEntity<List<CategoryDTO>> getCategoriesByParentId(@PathVariable Integer parentId) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Getting categories with parent category ID: {} in tenant: {}", parentId, currentTenant);

        List<Category> categories = categoryService.getCategoriesByParentId(parentId);
        List<CategoryDTO> categoryDTOs = categoryMapper.toDTOList(categories);

        return ResponseEntity.ok(categoryDTOs);
    }

    @GetMapping("/getByParentId/{parentId}/client/{clientId}")
    @Operation(summary = "Get child categories by parent category ID and client ID", description = "Returns a list of child categories that belong to the specified parent category and client")
    public ResponseEntity<List<CategoryDTO>> getChildCategoriesByParentAndClient(
            @PathVariable Integer parentId,
            @PathVariable Integer clientId) {
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Getting child categories with parent category ID: {} and client ID: {} in tenant: {}", parentId, clientId, currentTenant);

        List<Category> categories = categoryService.getCategoriesByParentIdAndClientId(parentId, clientId);
        List<CategoryDTO> categoryDTOs = categoryMapper.toDTOList(categories);

        return ResponseEntity.ok(categoryDTOs);
    }

}

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import axios from 'axios';

export default function MediaRenameTest() {
  const [assetId, setAssetId] = useState('');
  const [newFileName, setNewFileName] = useState('');
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const testEndpoints = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // First, get the asset to make sure it exists
      const getResponse = await axios.get(`/api/media/assets/getById/${assetId}`);
      console.log('Asset found:', getResponse.data);
      
      // Try different rename endpoints
      const endpoints = [
        { name: 'PUT /api/media/assets/update/{id}', url: `/api/media/assets/update/${assetId}`, method: 'put', data: { fileName: newFileName } },
        { name: 'PUT /api/media/assets/rename/{id}', url: `/api/media/assets/rename/${assetId}`, method: 'put', data: { fileName: newFileName } },
        { name: 'PUT /api/media/assets/{id}/rename', url: `/api/media/assets/${assetId}/rename`, method: 'put', data: { fileName: newFileName } },
        { name: 'PUT /api/media/assets/{id}', url: `/api/media/assets/${assetId}`, method: 'put', data: { fileName: newFileName } },
        { name: 'PATCH /api/media/assets/{id}', url: `/api/media/assets/${assetId}`, method: 'patch', data: { fileName: newFileName } }
      ];
      
      const results = [];
      
      for (const endpoint of endpoints) {
        try {
          console.log(`Trying ${endpoint.name}...`);
          const response = await axios({
            method: endpoint.method,
            url: endpoint.url,
            data: endpoint.data
          });
          
          results.push({
            endpoint: endpoint.name,
            success: true,
            status: response.status,
            data: response.data
          });
          
          console.log(`Success with ${endpoint.name}:`, response.data);
        } catch (err: any) {
          results.push({
            endpoint: endpoint.name,
            success: false,
            status: err.response?.status,
            error: err.message
          });
          
          console.error(`Failed with ${endpoint.name}:`, err.message);
        }
      }
      
      // Get the asset again to see if any of the renames worked
      const getAfterResponse = await axios.get(`/api/media/assets/getById/${assetId}`);
      console.log('Asset after rename attempts:', getAfterResponse.data);
      
      setResult({
        originalAsset: getResponse.data,
        endpointResults: results,
        finalAsset: getAfterResponse.data
      });
    } catch (err: any) {
      console.error('Error in test:', err);
      setError(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-6">Media Rename Test</h1>
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Test Rename Endpoints</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Asset ID</label>
              <Input 
                value={assetId} 
                onChange={(e) => setAssetId(e.target.value)} 
                placeholder="Enter asset ID" 
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">New File Name</label>
              <Input 
                value={newFileName} 
                onChange={(e) => setNewFileName(e.target.value)} 
                placeholder="Enter new file name" 
              />
            </div>
            
            <Button 
              onClick={testEndpoints} 
              disabled={loading || !assetId || !newFileName}
            >
              {loading ? 'Testing...' : 'Test Rename Endpoints'}
            </Button>
          </div>
        </CardContent>
      </Card>
      
      {error && (
        <div className="p-4 mb-6 bg-red-50 border border-red-200 rounded-md text-red-600">
          {error}
        </div>
      )}
      
      {result && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Original Asset</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded-md overflow-auto">
                {JSON.stringify(result.originalAsset, null, 2)}
              </pre>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Endpoint Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {result.endpointResults.map((r: any, i: number) => (
                  <div key={i} className={`p-4 rounded-md ${r.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                    <h3 className="font-medium">{r.endpoint}</h3>
                    <p>Status: {r.status || 'N/A'}</p>
                    {r.success ? (
                      <pre className="mt-2 bg-white p-2 rounded-md overflow-auto">
                        {JSON.stringify(r.data, null, 2)}
                      </pre>
                    ) : (
                      <p className="mt-2 text-red-600">{r.error}</p>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Final Asset</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded-md overflow-auto">
                {JSON.stringify(result.finalAsset, null, 2)}
              </pre>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}

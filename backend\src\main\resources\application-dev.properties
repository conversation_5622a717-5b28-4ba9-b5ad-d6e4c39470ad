# Database Configuration
spring.datasource.url=************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Multi-tenancy Configuration
multitenancy.default-tenant=public

# Initialize the database using SQL scripts
spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:schema_consolidated.sql,classpath:db/field_type_data.sql,classpath:db/tenants_table.sql
spring.sql.init.data-locations=classpath:data.sql,classpath:db/field_type_insert_data.sql,classpath:db/field_config_insert_data.sql
spring.sql.init.continue-on-error=true
spring.jpa.defer-datasource-initialization=true

# Database Indexing
spring.sql.init.separator=;
spring.sql.init.platform=postgresql

# Server Configuration
server.port=8071
server.servlet.context-path=/api

# JWT Configuration
jwt.expiration=86400000

# Swagger Configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method

# Logging Configuration
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=ERROR
logging.level.com.cms=DEBUG

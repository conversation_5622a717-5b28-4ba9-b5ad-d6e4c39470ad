<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Registration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Test Registration API</h1>
    
    <div class="form-group">
        <label for="username">Username:</label>
        <input type="text" id="username" value="testuser">
    </div>
    
    <div class="form-group">
        <label for="email">Email:</label>
        <input type="email" id="email" value="<EMAIL>">
    </div>
    
    <div class="form-group">
        <label for="password">Password:</label>
        <input type="password" id="password" value="password123">
    </div>
    
    <button onclick="register()">Register</button>
    
    <h2>Response:</h2>
    <pre id="response"></pre>
    
    <script>
        async function register() {
            const username = document.getElementById('username').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            const responseElement = document.getElementById('response');
            responseElement.textContent = 'Sending request...';
            
            try {
                const response = await fetch('http://localhost:8071/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        username,
                        email,
                        password
                    })
                });
                
                const statusCode = response.status;
                let responseData;
                
                try {
                    responseData = await response.json();
                } catch (e) {
                    responseData = await response.text();
                }
                
                responseElement.textContent = `Status: ${statusCode}\n\nResponse:\n${JSON.stringify(responseData, null, 2)}`;
            } catch (error) {
                responseElement.textContent = `Error: ${error.message}`;
                console.error('Error:', error);
            }
        }
    </script>
</body>
</html>

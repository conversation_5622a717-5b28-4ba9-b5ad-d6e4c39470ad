package com.cms.config;

import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.stereotype.Component;

@Component
public class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> implements WebServerFactoryCustomizer<TomcatServletWebServerFactory> {

    @Override
    public void customize(TomcatServletWebServerFactory factory) {
        factory.addConnectorCustomizers(connector -> {
            connector.setURIEncoding("UTF-8");
            connector.setUseBodyEncodingForURI(true);
        });
    }
}

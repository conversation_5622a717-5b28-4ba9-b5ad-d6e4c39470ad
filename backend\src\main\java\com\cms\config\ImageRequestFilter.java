package com.cms.config;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * Special filter for handling image requests to prevent ORB blocking issues.
 * This filter has the highest precedence to ensure it runs before any other filters.
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
@Slf4j
@PropertySource("classpath:cors-config.properties")
public class ImageRequestFilter implements Filter {

    @Value("${cors.resource-policy:cross-origin}")
    private String resourcePolicy;

    @Value("${cors.embedder-policy:unsafe-none}")
    private String embedderPolicy;

    @Value("${cors.opener-policy:unsafe-none}")
    private String openerPolicy;

    @Value("${cors.exposed-headers:Content-Disposition,Cross-Origin-Resource-Policy,Cross-Origin-Embedder-Policy,Cross-Origin-Opener-Policy}")
    private String exposedHeaders;

    @Override
    public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain)
            throws IOException, ServletException {
        HttpServletResponse response = (HttpServletResponse) res;
        HttpServletRequest request = (HttpServletRequest) req;

        String requestURI = request.getRequestURI();
        String method = request.getMethod();

        // Check if this is an image request
        boolean isImageRequest = false;
        if (requestURI.contains("/media/files/") ||
            requestURI.contains("/media/assets/") ||
            requestURI.contains("/media/download/")) {

            // Check file extension or content type
            if (requestURI.endsWith(".jpg") ||
                requestURI.endsWith(".jpeg") ||
                requestURI.endsWith(".png") ||
                requestURI.endsWith(".gif") ||
                requestURI.contains("/content") ||
                requestURI.contains("/download")) {

                isImageRequest = true;
            }
        }

        if (isImageRequest) {
            log.debug("Handling image request: {}", requestURI);

            // Get the origin from the request
            String origin = request.getHeader("Origin");

            // Set permissive CORS headers for image requests
            if (origin != null) {
                response.setHeader("Access-Control-Allow-Origin", origin);
            } else {
                response.setHeader("Access-Control-Allow-Origin", "*");
            }

            // Add exposed headers
            response.setHeader("Access-Control-Expose-Headers", exposedHeaders);

            // Disable ORB protection for image requests
            response.setHeader("Cross-Origin-Resource-Policy", resourcePolicy);
            response.setHeader("Cross-Origin-Embedder-Policy", embedderPolicy);
            response.setHeader("Cross-Origin-Opener-Policy", openerPolicy);

            // Add cache control headers to prevent caching issues
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");

            // Add additional headers for image requests
            response.setHeader("Timing-Allow-Origin", "*");
            response.setHeader("X-Content-Type-Options", "nosniff");

            log.debug("Added ORB prevention headers for image request: {}", requestURI);

            // Handle preflight requests
            if ("OPTIONS".equalsIgnoreCase(method)) {
                response.setHeader("Access-Control-Allow-Methods", "GET, HEAD, OPTIONS");
                response.setHeader("Access-Control-Allow-Headers", "*");
                response.setHeader("Access-Control-Max-Age", "3600");
                response.setStatus(HttpServletResponse.SC_OK);
                return;
            }
        }

        chain.doFilter(req, res);
    }

    @Override
    public void init(FilterConfig filterConfig) {
    }

    @Override
    public void destroy() {
    }
}

package com.cms.service;

import com.cms.entity.ContentEntry;

import java.util.List;
import java.util.Optional;

public interface ContentEntryService {
    List<ContentEntry> getAllContentEntries();
    Optional<ContentEntry> getContentEntryById(Integer id);
    List<ContentEntry> getContentEntriesByCollectionId(Integer collectionId);
    List<ContentEntry> searchContentEntriesByCollectionId(Integer collectionId, String searchText);
    List<ContentEntry> findContentEntriesByJsonPath(String jsonPath);
    List<ContentEntry> findContentEntriesByJsonPathAndValue(String jsonPath, String jsonValue);
    ContentEntry createContentEntry(ContentEntry contentEntry);
    ContentEntry updateContentEntry(Integer id, ContentEntry contentEntry);
    void deleteContentEntry(Integer id);
}

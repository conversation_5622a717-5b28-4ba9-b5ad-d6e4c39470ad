<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>R-CMS API Tester</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"], textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            min-height: 100px;
            font-family: monospace;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .response {
            margin-top: 10px;
            min-height: 100px;
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid transparent;
        }
        .tab.active {
            border: 1px solid #ddd;
            border-bottom: 1px solid white;
            border-radius: 4px 4px 0 0;
            margin-bottom: -1px;
            background-color: white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <h1>R-CMS API Tester</h1>
    
    <div class="container">
        <div class="card">
            <h2>Authentication</h2>
            <div class="form-group">
                <label for="login-username">Username (with tenant):</label>
                <input type="text" id="login-username" value="Saumya@redberyl">
            </div>
            <div class="form-group">
                <label for="login-password">Password:</label>
                <input type="password" id="login-password">
            </div>
            <button onclick="login()">Login</button>
            <div class="response">
                <h3>Response:</h3>
                <pre id="login-response"></pre>
            </div>
        </div>

        <div class="card">
            <h2>API Operations</h2>
            <div class="tabs">
                <div class="tab active" onclick="showTab('get-collections')">Get Collections</div>
                <div class="tab" onclick="showTab('create-collection')">Create Collection</div>
                <div class="tab" onclick="showTab('get-components')">Get Components</div>
                <div class="tab" onclick="showTab('create-component')">Create Component</div>
                <div class="tab" onclick="showTab('custom-request')">Custom Request</div>
            </div>

            <div id="get-collections" class="tab-content active">
                <button onclick="getCollections()">Get Collections</button>
                <div class="response">
                    <h3>Response:</h3>
                    <pre id="collections-response"></pre>
                </div>
            </div>

            <div id="create-collection" class="tab-content">
                <div class="form-group">
                    <label for="collection-name">Collection Name:</label>
                    <input type="text" id="collection-name" value="TestCollection">
                </div>
                <div class="form-group">
                    <label for="collection-api-id">API ID:</label>
                    <input type="text" id="collection-api-id" value="testCollection">
                </div>
                <div class="form-group">
                    <label for="collection-api-id-plural">API ID Plural:</label>
                    <input type="text" id="collection-api-id-plural" value="testCollections">
                </div>
                <button onclick="createCollection()">Create Collection</button>
                <div class="response">
                    <h3>Response:</h3>
                    <pre id="create-collection-response"></pre>
                </div>
            </div>

            <div id="get-components" class="tab-content">
                <button onclick="getComponents()">Get Components</button>
                <div class="response">
                    <h3>Response:</h3>
                    <pre id="components-response"></pre>
                </div>
            </div>

            <div id="create-component" class="tab-content">
                <div class="form-group">
                    <label for="component-name">Component Name:</label>
                    <input type="text" id="component-name" value="TestComponent">
                </div>
                <div class="form-group">
                    <label for="component-api-id">API ID:</label>
                    <input type="text" id="component-api-id" value="testComponent">
                </div>
                <button onclick="createComponent()">Create Component</button>
                <div class="response">
                    <h3>Response:</h3>
                    <pre id="create-component-response"></pre>
                </div>
            </div>

            <div id="custom-request" class="tab-content">
                <div class="form-group">
                    <label for="request-method">Method:</label>
                    <select id="request-method">
                        <option value="GET">GET</option>
                        <option value="POST">POST</option>
                        <option value="PUT">PUT</option>
                        <option value="DELETE">DELETE</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="request-url">URL (e.g., /collections):</label>
                    <input type="text" id="request-url" value="/collections">
                </div>
                <div class="form-group">
                    <label for="request-body">Request Body (JSON):</label>
                    <textarea id="request-body">{}</textarea>
                </div>
                <button onclick="sendCustomRequest()">Send Request</button>
                <div class="response">
                    <h3>Response:</h3>
                    <pre id="custom-response"></pre>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API base URL
        const API_URL = 'http://localhost:8071/api';
        let token = '';
        let tenant = '';

        function showTab(tabId) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Deactivate all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show the selected tab content
            document.getElementById(tabId).classList.add('active');
            
            // Activate the clicked tab
            event.currentTarget.classList.add('active');
        }

        async function login() {
            const username = document.getElementById('login-username').value;
            const password = document.getElementById('login-password').value;

            try {
                const response = await fetch(`${API_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                document.getElementById('login-response').textContent = JSON.stringify(data, null, 2);
                console.log('Login response:', data);

                if (data.accessToken) {
                    token = data.accessToken;
                    console.log('Token saved:', token);
                    
                    // Extract tenant from username
                    if (username.includes('@')) {
                        tenant = username.split('@')[1];
                        console.log('Tenant extracted:', tenant);
                    }
                }
            } catch (error) {
                document.getElementById('login-response').textContent = `Error: ${error.message}`;
                console.error('Login error:', error);
            }
        }

        async function getCollections() {
            if (!token) {
                document.getElementById('collections-response').textContent = 'Please login first';
                return;
            }

            try {
                const headers = {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                };

                if (tenant) {
                    headers['X-TenantID'] = tenant;
                }

                const response = await fetch(`${API_URL}/collections`, {
                    method: 'GET',
                    headers: headers
                });

                let data;
                try {
                    data = await response.json();
                } catch (e) {
                    data = await response.text();
                }

                document.getElementById('collections-response').textContent = 
                    typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
                console.log('Collections response:', data);
            } catch (error) {
                document.getElementById('collections-response').textContent = `Error: ${error.message}`;
                console.error('API call error:', error);
            }
        }

        async function createCollection() {
            if (!token) {
                document.getElementById('create-collection-response').textContent = 'Please login first';
                return;
            }

            const name = document.getElementById('collection-name').value;
            const apiId = document.getElementById('collection-api-id').value;
            const apiIdPlural = document.getElementById('collection-api-id-plural').value;

            try {
                const headers = {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                };

                if (tenant) {
                    headers['X-TenantID'] = tenant;
                }

                const response = await fetch(`${API_URL}/collections`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({
                        name: name,
                        apiId: apiId,
                        apiIdPlural: apiIdPlural
                    })
                });

                let data;
                try {
                    data = await response.json();
                } catch (e) {
                    data = await response.text();
                }

                document.getElementById('create-collection-response').textContent = 
                    typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
                console.log('Create collection response:', data);
            } catch (error) {
                document.getElementById('create-collection-response').textContent = `Error: ${error.message}`;
                console.error('API call error:', error);
            }
        }

        async function getComponents() {
            if (!token) {
                document.getElementById('components-response').textContent = 'Please login first';
                return;
            }

            try {
                const headers = {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                };

                if (tenant) {
                    headers['X-TenantID'] = tenant;
                }

                const response = await fetch(`${API_URL}/components`, {
                    method: 'GET',
                    headers: headers
                });

                let data;
                try {
                    data = await response.json();
                } catch (e) {
                    data = await response.text();
                }

                document.getElementById('components-response').textContent = 
                    typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
                console.log('Components response:', data);
            } catch (error) {
                document.getElementById('components-response').textContent = `Error: ${error.message}`;
                console.error('API call error:', error);
            }
        }

        async function createComponent() {
            if (!token) {
                document.getElementById('create-component-response').textContent = 'Please login first';
                return;
            }

            const name = document.getElementById('component-name').value;
            const apiId = document.getElementById('component-api-id').value;

            try {
                const headers = {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                };

                if (tenant) {
                    headers['X-TenantID'] = tenant;
                }

                const response = await fetch(`${API_URL}/components`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({
                        name: name,
                        apiId: apiId
                    })
                });

                let data;
                try {
                    data = await response.json();
                } catch (e) {
                    data = await response.text();
                }

                document.getElementById('create-component-response').textContent = 
                    typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
                console.log('Create component response:', data);
            } catch (error) {
                document.getElementById('create-component-response').textContent = `Error: ${error.message}`;
                console.error('API call error:', error);
            }
        }

        async function sendCustomRequest() {
            if (!token) {
                document.getElementById('custom-response').textContent = 'Please login first';
                return;
            }

            const method = document.getElementById('request-method').value;
            let url = document.getElementById('request-url').value;
            const body = document.getElementById('request-body').value;

            // Ensure URL starts with a slash
            if (!url.startsWith('/')) {
                url = '/' + url;
            }

            try {
                const headers = {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                };

                if (tenant) {
                    headers['X-TenantID'] = tenant;
                }

                const options = {
                    method: method,
                    headers: headers
                };

                if (method !== 'GET' && method !== 'HEAD') {
                    options.body = body;
                }

                const response = await fetch(`${API_URL}${url}`, options);

                let data;
                try {
                    data = await response.json();
                } catch (e) {
                    data = await response.text();
                }

                document.getElementById('custom-response').textContent = 
                    typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
                console.log('Custom request response:', data);
            } catch (error) {
                document.getElementById('custom-response').textContent = `Error: ${error.message}`;
                console.error('API call error:', error);
            }
        }
    </script>
</body>
</html>

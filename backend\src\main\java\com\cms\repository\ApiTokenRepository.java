package com.cms.repository;

import com.cms.entity.ApiToken;
import com.cms.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ApiTokenRepository extends JpaRepository<ApiToken, Long> {
    
    List<ApiToken> findByUser(User user);
    
    List<ApiToken> findByUserAndIsActiveTrue(User user);
    
    Optional<ApiToken> findByTokenValue(String tokenValue);
    
    boolean existsByTokenValue(String tokenValue);
    
    List<ApiToken> findByExpiresAtBefore(LocalDateTime dateTime);
}

{"openapi": "3.0.0", "info": {"title": "CMS API", "description": "Content Management System API", "version": "1.0.0", "contact": {"name": "CMS Team", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}}, "servers": [{"url": "/api", "description": "Local server"}], "tags": [{"name": "Media", "description": "Media management endpoints"}, {"name": "Components", "description": "Component management endpoints"}, {"name": "Collections", "description": "Collection management endpoints"}], "paths": {"/media/upload": {"post": {"tags": ["Media"], "summary": "Upload a file", "description": "Upload a file to the media library", "operationId": "uploadFile", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}, "folderId": {"type": "integer", "format": "int32"}, "description": {"type": "string"}, "altText": {"type": "string"}, "isPublic": {"type": "boolean", "default": false}}, "required": ["file"]}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "fileName": {"type": "string"}, "originalFileName": {"type": "string"}, "fileType": {"type": "string"}, "fileSize": {"type": "integer", "format": "int64"}, "publicUrl": {"type": "string"}, "description": {"type": "string"}, "altText": {"type": "string"}, "isPublic": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}}}}}}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}}}}, "/media/assets/getAll": {"get": {"tags": ["Media"], "summary": "Get all media assets", "description": "Returns a list of all media assets", "operationId": "getAllAssets", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "fileName": {"type": "string"}, "fileType": {"type": "string"}, "publicUrl": {"type": "string"}}}}}}}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}
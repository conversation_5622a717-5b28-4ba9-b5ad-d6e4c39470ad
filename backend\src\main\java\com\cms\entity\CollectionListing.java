package com.cms.entity;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIdentityReference;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Entity
@Table(
        name = "collection_listing",
        uniqueConstraints = {
                @UniqueConstraint(
                        name = "uk_collection_name_category_id",
                        columnNames = {"collection_name", "category_id"}
                )
        }
)
@Getter
@Setter
@ToString(exclude = {"components", "fields", "category"})
@NoArgsConstructor
@AllArgsConstructor
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id", scope = CollectionListing.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CollectionListing extends Auditable {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "cms_collection_listing_seq")
    @SequenceGenerator(name = "cms_collection_listing_seq", sequenceName = "cms_collection_listing_seq", initialValue = 100, allocationSize = 1)
    private Integer id;

    @NotBlank(message = "Collection name is required")
    @Column(name = "collection_name", nullable = false) // removed unique = true
    private String collectionName;

    @Column(name = "collection_desc")
    private String collectionDesc;

    @Column(name = "additional_information")
    private String additionalInformation;

    @Column(name = "disclaimer_text")
    private String disclaimerText;

    @Column(name = "collection_api_id", nullable = false)
    private String collectionApiId;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "category_id", nullable = false)
    private Category category;

    @OneToMany(mappedBy = "collection", cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JsonIdentityReference(alwaysAsId = true)
    private List<CollectionComponent> components = new ArrayList<>();

    @OneToMany(mappedBy = "collection", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<CollectionField> fields = new ArrayList<>();
}

package com.cms.controller;

import com.cms.config.TenantContextHolder;
import com.cms.entity.User;
import com.cms.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * Test controller for debugging authentication issues
 */
@RestController
@RequestMapping("/test/auth")
@RequiredArgsConstructor
@Slf4j
public class TestAuthController {

    private final UserService userService;

    @GetMapping("/debug-user/{username}")
    public ResponseEntity<?> debugUser(@PathVariable String username) {
        log.info("Debug request for username: {}", username);

        Map<String, Object> response = new HashMap<>();

        try {
            // Check current tenant context
            String currentTenant = TenantContextHolder.getTenantId();
            response.put("currentTenant", currentTenant);

            // Try to find user in current tenant
            Optional<User> userOpt = userService.findByUsername(username);

            if (userOpt.isPresent()) {
                User user = userOpt.get();
                response.put("userFound", true);
                response.put("userId", user.getId());
                response.put("username", user.getUsername());
                response.put("email", user.getEmail());
                response.put("isActive", user.getIsActive());
                response.put("isLoggedIn", user.getIsLoggedIn());
            } else {
                response.put("userFound", false);
                response.put("message", "User not found in tenant: " + currentTenant);
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error debugging user: {}", e.getMessage(), e);
            response.put("error", e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    @PostMapping("/set-tenant/{tenantId}")
    public ResponseEntity<?> setTenant(@PathVariable String tenantId) {
        log.info("Setting tenant context to: {}", tenantId);

        try {
            TenantContextHolder.setTenantId(tenantId);
            String actualTenant = TenantContextHolder.getTenantId();

            Map<String, Object> response = new HashMap<>();
            response.put("requestedTenant", tenantId);
            response.put("actualTenant", actualTenant);
            response.put("success", tenantId.equals(actualTenant));

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error setting tenant: {}", e.getMessage(), e);
            Map<String, Object> response = new HashMap<>();
            response.put("error", e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    @GetMapping("/current-tenant")
    public ResponseEntity<?> getCurrentTenant() {
        String currentTenant = TenantContextHolder.getTenantId();

        Map<String, Object> response = new HashMap<>();
        response.put("currentTenant", currentTenant);

        return ResponseEntity.ok(response);
    }

    @PostMapping("/clear-tenant")
    public ResponseEntity<?> clearTenant() {
        log.info("Clearing tenant context");

        TenantContextHolder.clear();
        String currentTenant = TenantContextHolder.getTenantId();

        Map<String, Object> response = new HashMap<>();
        response.put("currentTenant", currentTenant);
        response.put("message", "Tenant context cleared");

        return ResponseEntity.ok(response);
    }

    @GetMapping("/check-auth-status")
    public ResponseEntity<?> checkAuthStatus() {
        log.info("Checking authentication status");

        Map<String, Object> response = new HashMap<>();

        try {
            // Check security context
            var authentication = org.springframework.security.core.context.SecurityContextHolder.getContext().getAuthentication();
            boolean isAuthenticated = authentication != null && authentication.isAuthenticated() &&
                                    !authentication.getName().equals("anonymousUser");

            response.put("isAuthenticated", isAuthenticated);
            response.put("currentTenant", TenantContextHolder.getTenantId());

            if (isAuthenticated) {
                response.put("username", authentication.getName());
                response.put("authorities", authentication.getAuthorities().toString());
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error checking auth status: {}", e.getMessage(), e);
            response.put("error", e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
}

package com.cms.config;

import org.hibernate.HibernateException;
import org.hibernate.engine.jdbc.connections.spi.MultiTenantConnectionProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

public class MultiTenantConnectionProviderImpl implements MultiTenantConnectionProvider<String> {

    private static final Logger log = LoggerFactory.getLogger(MultiTenantConnectionProviderImpl.class);

    private static final long serialVersionUID = 1L;

    private static final String DEFAULT_TENANT_ID = "public";

    private DataSource dataSource;

    public void setDataSource(DataSource dataSource) {
        this.dataSource = dataSource;
    }

    @Override
    public Connection getAnyConnection() throws SQLException {
        return dataSource.getConnection();
    }

    @Override
    public void releaseAnyConnection(Connection connection) throws SQLException {
        connection.close();
    }

    @Override
    public Connection getConnection(String tenantIdentifier) throws SQLException {
        final Connection connection = getAnyConnection();
        try {
            log.info("Getting connection for tenant: {}, thread: {}",
                    tenantIdentifier, Thread.currentThread().getName());

            // Get current search_path before changing it
            String currentSearchPath = "unknown";
            try {
                java.sql.Statement stmt = connection.createStatement();
                java.sql.ResultSet rs = stmt.executeQuery("SHOW search_path");
                if (rs.next()) {
                    currentSearchPath = rs.getString(1);
                }
                rs.close();
                stmt.close();
                log.info("Current search_path before change: {}", currentSearchPath);
            } catch (Exception e) {
                log.warn("Could not get current search_path: {}", e.getMessage());
            }

            // Use the tenant identifier from the context if not provided
            if (tenantIdentifier == null || tenantIdentifier.isEmpty()) {
                tenantIdentifier = TenantContextHolder.getTenantId();
                log.info("No tenant identifier provided, using tenant from context: {}", tenantIdentifier);
            }

            // Set the schema for the connection
            String schemaToUse = tenantIdentifier;
            String sql = String.format("SET search_path TO %s", schemaToUse);
            log.info("Executing SQL: {}, thread: {}", sql, Thread.currentThread().getName());

            // Execute the SQL statement to set the search path
            connection.createStatement().execute(sql);

            // Force the connection to use the specified schema
            connection.setSchema(schemaToUse);
            log.info("Connection schema set to: {}", schemaToUse);

            // Verify the search_path was set correctly
            String newSearchPath = "unknown";
            try {
                java.sql.Statement stmt = connection.createStatement();
                java.sql.ResultSet rs = stmt.executeQuery("SHOW search_path");
                if (rs.next()) {
                    newSearchPath = rs.getString(1);
                }
                rs.close();
                stmt.close();
                log.debug("New search_path after change: {}", newSearchPath);

                if (!newSearchPath.contains(tenantIdentifier != null ? tenantIdentifier : DEFAULT_TENANT_ID)) {
                    log.warn("Search path does not contain expected tenant! Expected: {}, Actual: {}",
                            tenantIdentifier != null ? tenantIdentifier : DEFAULT_TENANT_ID, newSearchPath);
                }
            } catch (Exception e) {
                log.warn("Could not verify search_path: {}", e.getMessage());
            }

            log.debug("Connection established for tenant: {}", tenantIdentifier);
        } catch (SQLException e) {
            log.error("Could not alter JDBC connection to specified schema [{}]: {}",
                    tenantIdentifier, e.getMessage());
            throw new HibernateException("Could not alter JDBC connection to specified schema [" +
                    tenantIdentifier + "]", e);
        }
        return connection;
    }

    @Override
    public void releaseConnection(String tenantIdentifier, Connection connection) throws SQLException {
        try {
            connection.createStatement().execute("SET search_path TO public");
        } catch (SQLException e) {
            // Do nothing
        }
        connection.close();
    }

    @Override
    public boolean supportsAggressiveRelease() {
        return false;
    }

    @Override
    public boolean isUnwrappableAs(Class<?> unwrapType) {
        return false;
    }

    @Override
    public <T> T unwrap(Class<T> unwrapType) {
        return null;
    }
}

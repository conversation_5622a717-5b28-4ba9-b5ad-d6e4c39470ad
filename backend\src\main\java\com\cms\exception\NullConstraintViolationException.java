package com.cms.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

// Custom status code 700 is used for null constraint violations
@ResponseStatus(value = HttpStatus.BAD_REQUEST, reason = "Field cannot be null")
public class NullConstraintViolationException extends RuntimeException {

    private final String fieldName;

    public NullConstraintViolationException(String fieldName) {
        super(String.format("Field '%s' cannot be null", fieldName));
        this.fieldName = fieldName;
    }

    public String getFieldName() {
        return fieldName;
    }
}

-- Field Configs table
CREATE TABLE IF NOT EXISTS field_configs (
    id INT PRIMARY KEY,
    config_name CHARACTER VARYING(255),
    is_active BOOLEAN,
    value_type CHARACTER VARYING(50),
    config_type_id INTEGER,
    field_type_id INTEGER,
    created_at TIMESTAMP WITHOUT TIME ZONE,
    created_by <PERSON><PERSON><PERSON><PERSON>R VARYING(255),
    modified_at TIMESTAMP WITHOUT TIME ZONE,
    modified_by CHARACTER VARYING(255),
    FOREIGN KEY (field_type_id) REFERENCES field_types(id),
    FOREIGN KEY (config_type_id) REFERENCES config_types(id)
);

-- Create sequence for FieldConfig entity
CREATE SEQUENCE IF NOT EXISTS cms_field_config_seq START WITH 500 INCREMENT BY 1;

-- Indexes for field_configs table
CREATE INDEX IF NOT EXISTS idx_field_configs_field_type_id ON field_configs(field_type_id);
CREATE INDEX IF NOT EXISTS idx_field_configs_config_type_id ON field_configs(config_type_id);
CREATE INDEX IF NOT EXISTS idx_field_configs_config_name ON field_configs(config_name);
CREATE INDEX IF NOT EXISTS idx_field_configs_is_active ON field_configs(is_active);

-- No data is inserted here to allow for custom field_configs
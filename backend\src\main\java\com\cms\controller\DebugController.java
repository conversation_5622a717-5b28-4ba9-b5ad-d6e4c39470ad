package com.cms.controller;

import com.cms.entity.CollectionComponent;
import com.cms.repository.CollectionComponentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/debug")
public class DebugController {

    @Autowired
    private CollectionComponentRepository collectionComponentRepository;

    @GetMapping("/getCollectionComponents")
    public ResponseEntity<List<CollectionComponent>> getAllCollectionComponents() {
        return ResponseEntity.ok(collectionComponentRepository.findAll());
    }
}

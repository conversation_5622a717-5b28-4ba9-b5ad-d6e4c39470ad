package com.cms.config;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * Global filter to add security headers to all responses.
 * This filter has the highest precedence to ensure it runs before any other filters.
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
@Slf4j
public class GlobalHeadersFilter implements Filter {

    @Override
    public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain)
            throws IOException, ServletException {
        HttpServletResponse response = (HttpServletResponse) res;
        HttpServletRequest request = (HttpServletRequest) req;

        String requestURI = request.getRequestURI();

        // Add global security headers to all responses
        response.setHeader("Cross-Origin-Resource-Policy", "cross-origin");
        response.setHeader("Cross-Origin-Embedder-Policy", "unsafe-none");
        response.setHeader("Cross-Origin-Opener-Policy", "unsafe-none");

        // Add timing allow origin header to all responses
        response.setHeader("Timing-Allow-Origin", "*");

        // Add CORS headers to all responses
        String origin = request.getHeader("Origin");
        if (origin != null) {
            response.setHeader("Access-Control-Allow-Origin", origin);
        } else {
            response.setHeader("Access-Control-Allow-Origin", "*");
        }

        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH");
        response.setHeader("Access-Control-Allow-Headers", "*");
        response.setHeader("Access-Control-Expose-Headers", "*");
        response.setHeader("Access-Control-Max-Age", "3600");

        // For media endpoints, add additional headers
        if (requestURI.contains("/media/") || requestURI.contains("/api/media/")) {
            log.debug("Adding media-specific headers to: {}", requestURI);

            // Add cache control headers
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");

            // Add additional security headers
            response.setHeader("Timing-Allow-Origin", "*");
            response.setHeader("X-Content-Type-Options", "nosniff");
            response.setHeader("X-Frame-Options", "SAMEORIGIN");

            // Handle preflight requests for media endpoints
            if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
                response.setStatus(HttpServletResponse.SC_OK);
                return;
            }
        }

        chain.doFilter(req, res);
    }

    @Override
    public void init(FilterConfig filterConfig) {
    }

    @Override
    public void destroy() {
    }
}

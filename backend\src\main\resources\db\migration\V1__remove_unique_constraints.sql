-- Drop unique constraints on username and email columns in all schemas
-- This script should be executed manually by the database administrator

-- Function to drop constraints in all schemas
CREATE OR REPLACE FUNCTION drop_unique_constraints_in_all_schemas() RET<PERSON><PERSON> void AS $$
DECLARE
    schema_name text;
BEGIN
    -- Loop through all schemas except system schemas
    FOR schema_name IN 
        SELECT nspname FROM pg_namespace 
        WHERE nspname NOT LIKE 'pg_%' AND nspname != 'information_schema'
    LOOP
        -- Drop unique constraint on username if it exists
        BEGIN
            EXECUTE format('ALTER TABLE %I.users DROP CONSTRAINT IF EXISTS users_username_key', schema_name);
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error dropping username constraint in schema %: %', schema_name, SQLERRM;
        END;
        
        -- Drop unique constraint on email if it exists
        BEGIN
            EXECUTE format('ALTER TABLE %I.users DROP CONSTRAINT IF EXISTS users_email_key', schema_name);
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error dropping email constraint in schema %: %', schema_name, SQLERRM;
        END;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Execute the function
SELECT drop_unique_constraints_in_all_schemas();

-- Drop the function after use
DROP FUNCTION drop_unique_constraints_in_all_schemas();

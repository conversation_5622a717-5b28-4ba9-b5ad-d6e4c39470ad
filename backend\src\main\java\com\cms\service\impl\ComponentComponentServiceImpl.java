package com.cms.service.impl;

import com.cms.entity.ComponentComponent;
import com.cms.entity.ComponentListing;
import com.cms.exception.CircularReferenceException;
import com.cms.exception.ForeignKeyViolationException;
import com.cms.exception.NullConstraintViolationException;
import com.cms.exception.ResourceNotFoundException;
import com.cms.repository.ComponentComponentRepository;
import com.cms.repository.ComponentListingRepository;
import com.cms.service.ComponentComponentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
@RequiredArgsConstructor
@Slf4j
public class ComponentComponentServiceImpl implements ComponentComponentService {

    private final ComponentComponentRepository componentComponentRepository;
    private final ComponentListingRepository componentListingRepository;

    @Override
    public List<ComponentComponent> getAllComponentComponents() {
        return componentComponentRepository.findAll();
    }

    @Override
    public List<ComponentComponent> getComponentComponentsByParentId(Integer parentComponentId) {
        return componentComponentRepository.findByParentComponentIdOrderByDisplayPreference(parentComponentId);
    }

    @Override
    public java.util.Optional<ComponentComponent> getComponentComponentById(Integer id) {
        return componentComponentRepository.findById(id);
    }

    @Override
    @Transactional
    public ComponentComponent createComponentComponent(ComponentComponent componentComponent) {
        // Validate parent component
        if (componentComponent.getParentComponent() == null || componentComponent.getParentComponent().getId() == null) {
            throw new NullConstraintViolationException("parentComponent");
        }

        // Verify parent component exists
        ComponentListing parentComponent = componentListingRepository.findById(componentComponent.getParentComponent().getId())
                .orElseThrow(() -> new ForeignKeyViolationException("ComponentListing", "id",
                        componentComponent.getParentComponent().getId()));
        componentComponent.setParentComponent(parentComponent);

        // Validate child component
        if (componentComponent.getChildComponent() == null || componentComponent.getChildComponent().getId() == null) {
            throw new NullConstraintViolationException("childComponent");
        }

        // Verify child component exists
        ComponentListing childComponent = componentListingRepository.findById(componentComponent.getChildComponent().getId())
                .orElseThrow(() -> new ForeignKeyViolationException("ComponentListing", "id",
                        componentComponent.getChildComponent().getId()));
        componentComponent.setChildComponent(childComponent);

        // Check for circular references
        if (componentComponent.getParentComponent().getId().equals(componentComponent.getChildComponent().getId())) {
            throw new CircularReferenceException("A component cannot be a child of itself");
        }

        // Check for deeper circular references
        checkForCircularReferences(componentComponent.getParentComponent().getId(),
                                  componentComponent.getChildComponent().getId(),
                                  new HashSet<>());

        // Set display preference if not provided
        if (componentComponent.getDisplayPreference() == null) {
            Integer maxDisplayPreference = componentComponentRepository.findMaxDisplayPreferenceByParentComponentId(
                    componentComponent.getParentComponent().getId());
            componentComponent.setDisplayPreference(maxDisplayPreference != null ? maxDisplayPreference + 10 : 10);
        }

        // Set default values if not provided
        if (componentComponent.getIsRepeatable() == null) {
            componentComponent.setIsRepeatable(false);
        }
        if (componentComponent.getIsActive() == null) {
            componentComponent.setIsActive(true);
        }

        return componentComponentRepository.save(componentComponent);
    }

    @Override
    @Transactional
    public ComponentComponent updateComponentComponent(Integer id, ComponentComponent componentComponent) {
        // Verify the component exists
        ComponentComponent existingComponent = getComponentComponentById(id)
                .orElseThrow(() -> new ResourceNotFoundException("ComponentComponent not found with id: " + id));

        // Update child component reference if provided
        if (componentComponent.getChildComponent() != null && componentComponent.getChildComponent().getId() != null) {
            // Check if the new child component exists
            ComponentListing childComponent = componentListingRepository.findById(componentComponent.getChildComponent().getId())
                    .orElseThrow(() -> new ForeignKeyViolationException("ComponentListing", "id",
                            componentComponent.getChildComponent().getId()));

            // Check for circular references if child component is changing
            if (!existingComponent.getChildComponent().getId().equals(childComponent.getId())) {
                if (existingComponent.getParentComponent().getId().equals(childComponent.getId())) {
                    throw new CircularReferenceException("A component cannot be a child of itself");
                }

                // Check for deeper circular references
                checkForCircularReferences(existingComponent.getParentComponent().getId(),
                                          childComponent.getId(),
                                          new HashSet<>());
            }

            existingComponent.setChildComponent(childComponent);
        }

        // Update other fields if provided
        if (componentComponent.getDisplayPreference() != null) {
            existingComponent.setDisplayPreference(componentComponent.getDisplayPreference());
        }
        if (componentComponent.getIsRepeatable() != null) {
            existingComponent.setIsRepeatable(componentComponent.getIsRepeatable());
        }
        if (componentComponent.getIsActive() != null) {
            existingComponent.setIsActive(componentComponent.getIsActive());
        }
        if (componentComponent.getAdditionalInformation() != null) {
            existingComponent.setAdditionalInformation(componentComponent.getAdditionalInformation());
        }

        return componentComponentRepository.save(existingComponent);
    }

    @Override
    @Transactional
    public void deleteComponentComponent(Integer id) {
        // Log the deletion attempt
        log.info("Attempting to delete component-component relationship with ID: {}", id);

        try {
            // Verify the component exists
            ComponentComponent componentComponent = getComponentComponentById(id)
                    .orElseThrow(() -> {
                        log.error("ComponentComponent not found with id: {}", id);
                        return new ResourceNotFoundException("ComponentComponent not found with id: " + id);
                    });

            // Log the component details before deletion
            log.info("Found component-component relationship: Parent ID={}, Child ID={}",
                    componentComponent.getParentComponent().getId(),
                    componentComponent.getChildComponent().getId());

            // Detach from parent component's childComponents collection to avoid cascade issues
            ComponentListing parentComponent = componentComponent.getParentComponent();
            if (parentComponent != null && parentComponent.getChildComponents() != null) {
                parentComponent.getChildComponents().removeIf(cc -> cc.getId().equals(id));
                componentListingRepository.save(parentComponent);
                log.info("Removed relationship from parent component's collection");
            }

            // Delete the component using the repository directly
            componentComponentRepository.deleteById(id);

            // Flush to ensure the delete is processed immediately
            componentComponentRepository.flush();

            // Verify deletion
            if (componentComponentRepository.existsById(id)) {
                log.error("Failed to delete component-component relationship with ID: {}", id);
                throw new RuntimeException("Failed to delete component-component relationship with ID: " + id);
            }

            log.info("Successfully deleted component-component relationship with ID: {}", id);
        } catch (Exception e) {
            log.error("Error deleting component-component relationship with ID: {}", id, e);
            throw new RuntimeException("Failed to delete component-component relationship with ID: " + id, e);
        }
    }

    @Override
    @Transactional
    public void reorderComponentComponents(Integer parentComponentId, List<Integer> componentComponentIds) {
        // Verify parent component exists
        if (!componentListingRepository.existsById(parentComponentId)) {
            throw new ResourceNotFoundException("Parent component not found with id: " + parentComponentId);
        }

        // Get all component components for this parent
        List<ComponentComponent> componentComponents = componentComponentRepository.findByParentComponentId(parentComponentId);

        // Create a map of id to component component for quick lookup
        java.util.Map<Integer, ComponentComponent> componentComponentMap = new java.util.HashMap<>();
        for (ComponentComponent cc : componentComponents) {
            componentComponentMap.put(cc.getId(), cc);
        }

        // Update display preferences based on the order in the list
        int displayPreference = 1;
        for (Integer ccId : componentComponentIds) {
            ComponentComponent cc = componentComponentMap.get(ccId);
            if (cc != null) {
                cc.setDisplayPreference(displayPreference++);
                componentComponentRepository.save(cc);
            }
        }
    }

    /**
     * Recursively check for circular references in component hierarchy
     *
     * @param parentId The ID of the parent component
     * @param childId The ID of the child component to check
     * @param visitedIds Set of component IDs already visited in this path
     */
    private void checkForCircularReferences(Integer parentId, Integer childId, Set<Integer> visitedIds) {
        // If we've already visited this component in this path, we have a circular reference
        if (visitedIds.contains(childId)) {
            throw new CircularReferenceException("Circular reference detected in component hierarchy");
        }

        // Add the current child to the visited set
        visitedIds.add(childId);

        // Get all components where this child is a parent
        List<ComponentComponent> childComponents = componentComponentRepository.findByParentComponentId(childId);

        // Recursively check each child's children
        for (ComponentComponent cc : childComponents) {
            checkForCircularReferences(parentId, cc.getChildComponent().getId(), new HashSet<>(visitedIds));
        }
    }
}

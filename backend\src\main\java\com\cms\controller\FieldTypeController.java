package com.cms.controller;

import com.cms.entity.FieldType;
import com.cms.exception.UniqueConstraintViolationException;
import com.cms.service.FieldTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/field-types")
@RequiredArgsConstructor
@Tag(name = "Field Type", description = "Field Type API")
public class FieldTypeController {

    private final FieldTypeService fieldTypeService;

    @GetMapping("/getAll")
    @Operation(summary = "Get all field types", description = "Returns a list of all field types")
    public ResponseEntity<List<FieldType>> getAllFieldTypes() {
        return ResponseEntity.ok(fieldTypeService.getAllFieldTypes());
    }

    @GetMapping("/getAllActive")
    @Operation(summary = "Get active field types", description = "Returns a list of active field types or 204 No Content if none are active")
    public ResponseEntity<?> getActiveFieldTypes() {
        List<FieldType> activeFieldTypes = fieldTypeService.getActiveFieldTypes();

        if (activeFieldTypes.isEmpty()) {
            return ResponseEntity.noContent().build();
        }

        return ResponseEntity.ok(activeFieldTypes);
    }

    @GetMapping("/getById/{id}")
    @Operation(summary = "Get field type by ID", description = "Returns a field type by its ID or 204 No Content if not found")
    public ResponseEntity<?> getFieldTypeById(@PathVariable Integer id) {
        Optional<FieldType> fieldType = fieldTypeService.getFieldTypeById(id);
        if (fieldType.isPresent()) {
            return ResponseEntity.ok(fieldType.get());
        } else {
            return ResponseEntity.noContent().build();
        }
    }

    @PostMapping("/create")
    @Operation(summary = "Create a new field type", description = "Creates a new field type")
    public ResponseEntity<FieldType> createFieldType(@Valid @RequestBody FieldType fieldType) {
        if (fieldTypeService.existsByFieldTypeName(fieldType.getFieldTypeName())) {
            throw new UniqueConstraintViolationException("fieldTypeName", fieldType.getFieldTypeName());
        }
        return ResponseEntity.status(HttpStatus.CREATED).body(fieldTypeService.createFieldType(fieldType));
    }

    @PutMapping("/update/{id}")
    @Operation(summary = "Update a field type", description = "Updates an existing field type")
    public ResponseEntity<FieldType> updateFieldType(@PathVariable Integer id, @Valid @RequestBody FieldType fieldType) {
        return ResponseEntity.ok(fieldTypeService.updateFieldType(id, fieldType));
    }

    @DeleteMapping("/deleteById/{id}")
    @Operation(summary = "Delete a field type", description = "Soft deletes a field type by setting isActive to false")
    public ResponseEntity<Void> deleteFieldType(@PathVariable Integer id) {
        fieldTypeService.deleteFieldType(id);
        return ResponseEntity.noContent().build();
    }
}

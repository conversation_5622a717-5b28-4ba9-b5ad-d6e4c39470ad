package com.cms.controller;

import com.cms.config.TenantContextHolder;
import com.cms.entity.Tenant;
import com.cms.entity.User;
import com.cms.payload.AddUserToTenantRequest;
import com.cms.payload.JwtAuthResponse;
import com.cms.payload.ListUsersInTenantRequest;
import com.cms.payload.LoginRequest;
import com.cms.payload.SignupRequest;
import com.cms.repository.TenantRepository;
import com.cms.repository.UserRepository;
import com.cms.security.JwtTokenProvider;
import com.cms.service.TenantService;
import com.cms.service.UserService;
import com.cms.util.TenantUtils;
import org.springframework.transaction.annotation.Transactional;
import io.swagger.v3.oas.annotations.Operation;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Tag(name = "Authentication", description = "Authentication API")
@Slf4j
public class AuthController {

    private final AuthenticationManager authenticationManager;
    private final UserRepository userRepository;
    private final TenantRepository tenantRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtTokenProvider tokenProvider;
    private final TenantUtils tenantUtils;
    private final TenantService tenantService;
    private final UserService userService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @PostMapping("/login")
    @Operation(
        summary = "Login",
        description = "Authenticate user and return JWT token. Use this token with the Authorize button in Swagger UI.",
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "Login credentials",
            content = @Content(
                mediaType = "application/json",
                examples = {
                    @ExampleObject(
                        name = "Standard Login",
                        summary = "Login with username and password",
                        value = "{\"username\":\"admin\",\"password\":\"password\"}"
                    )
                },
                schema = @Schema(implementation = LoginRequest.class)
            )
        ),
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "Login successful, returns JWT token",
                content = @Content(mediaType = "application/json")
            ),
            @ApiResponse(
                responseCode = "401",
                description = "Invalid credentials",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<JwtAuthResponse> authenticateUser(@Valid @RequestBody LoginRequest loginRequest) {
        log.info("Login attempt for username: {}", loginRequest.getUsername());

        // Clear any existing tenant context to start fresh
        TenantContextHolder.clear();
        log.info("Tenant context cleared before login attempt");

        try {
            // Log the current tenant context before authentication
            String currentTenant = TenantContextHolder.getTenantId();
            log.info("Current tenant context before authentication: {}", currentTenant);

            // Authenticate with the provided username (may include @tenant)
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            loginRequest.getUsername(),
                            loginRequest.getPassword()
                    )
            );

            // Log the tenant context after authentication
            String tenantAfterAuth = TenantContextHolder.getTenantId();
            log.info("Tenant context after authentication: {}", tenantAfterAuth);

            SecurityContextHolder.getContext().setAuthentication(authentication);
            String jwt = tokenProvider.generateToken(authentication);

            // Update login status to true
            try {
                userService.updateLoginStatus(loginRequest.getUsername(), true);
                log.info("Login status updated to true for user: {}", loginRequest.getUsername());
            } catch (Exception e) {
                log.warn("Failed to update login status for user: {}, error: {}", loginRequest.getUsername(), e.getMessage());
            }

            log.info("Login successful for user: {}", loginRequest.getUsername());
            return ResponseEntity.ok(new JwtAuthResponse(jwt));

        } catch (Exception e) {
            log.error("Login failed for user: {}, error: {}", loginRequest.getUsername(), e.getMessage());
            log.error("Current tenant context during error: {}", TenantContextHolder.getTenantId());
            throw e;
        } finally {
            // Don't clear tenant context here - let it persist for the session
            log.info("Login process completed, tenant context preserved: {}", TenantContextHolder.getTenantId());
        }
    }

    @PostMapping("/register")
    @Operation(summary = "Register", description = "Register a new user. Tenant schema is derived from email domain (e.g., <EMAIL> → acme_com schema)")
    @Transactional
    public ResponseEntity<?> registerUser(@Valid @RequestBody SignupRequest signupRequest) {
        log.info("Registering user: {} with email: {}", signupRequest.getUsername(), signupRequest.getEmail());

        // Clear any existing tenant context to start fresh
        TenantContextHolder.clear();

        // Determine tenant schema using new email domain-based logic
        String usernameWithTenant = signupRequest.getUsername();
        String username = tenantUtils.extractUsername(usernameWithTenant);
        String tenantSchemaName = tenantUtils.determineTenantSchemaForRegistration(usernameWithTenant, signupRequest.getEmail());

        log.info("Extracted username: {}, determined tenant schema: {}", username, tenantSchemaName);

        // Determine the target tenant schema
        String targetTenantId = (tenantSchemaName != null && !tenantSchemaName.isEmpty())
                ? tenantSchemaName
                : TenantContextHolder.getDefaultTenant();

        log.info("Target tenant ID for user registration: {}", targetTenantId);

        // Check if tenant exists or needs to be created
        if (tenantSchemaName != null && !tenantSchemaName.isEmpty()) {
            // Temporarily set context to public for tenant operations
            TenantContextHolder.forceTenantContext(TenantContextHolder.getDefaultTenant());

            if (!tenantRepository.existsBySchemaName(tenantSchemaName)) {
                log.info("Creating new tenant with schema name: {}", tenantSchemaName);

                // Create new tenant
                Tenant newTenant = new Tenant();
                newTenant.setName(tenantSchemaName); // Using schema name as tenant name for simplicity
                newTenant.setSchemaName(tenantSchemaName);
                newTenant.setDescription("Auto-created tenant for user " + username);
                newTenant.setIsActive(true);

                try {
                    // Create the tenant and verify it was created successfully
                    Tenant createdTenant = tenantService.createTenant(newTenant);
                    log.info("Tenant created successfully: {}, ID: {}", tenantSchemaName, createdTenant.getId());

                    // Verify the schema was created
                    jdbcTemplate.execute("SET search_path TO " + tenantSchemaName);
                    List<Map<String, Object>> tables = jdbcTemplate.queryForList(
                            "SELECT table_name FROM information_schema.tables WHERE table_schema = ?",
                            tenantSchemaName);

                    log.info("Tables created in schema {}: {}", tenantSchemaName, tables.size());
                    if (tables.isEmpty()) {
                        log.error("No tables found in schema: {}", tenantSchemaName);
                        return ResponseEntity
                                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body("Error: Schema created but no tables were initialized: " + tenantSchemaName);
                    }

                    // Reset search path to public
                    jdbcTemplate.execute("SET search_path TO public");
                } catch (Exception e) {
                    log.error("Failed to create tenant: {}", e.getMessage(), e);
                    return ResponseEntity
                            .status(HttpStatus.INTERNAL_SERVER_ERROR)
                            .body("Error: Failed to create tenant: " + e.getMessage());
                }
            }
        }

        // IMPORTANT: Set the tenant context for user creation
        // This must be done AFTER tenant creation but BEFORE user operations
        TenantContextHolder.forceTenantContext(targetTenantId);
        log.info("Forced tenant context to: {}, current context: {}",
                targetTenantId, TenantContextHolder.getTenantId());

        try {
            // Check if username already exists in the current tenant schema
            log.info("Checking if username '{}' exists in tenant: {}", username, TenantContextHolder.getTenantId());

            // Check username existence using the UserService
            boolean usernameExists = userService.existsByUsername(username);
            log.info("Username exists check result (in current schema): {}", usernameExists);

            if (usernameExists) {
                log.warn("Username '{}' already exists in tenant: {}", username, TenantContextHolder.getTenantId());
                return ResponseEntity
                        .badRequest()
                        .body("Error: Username is already taken in this tenant!");
            }

            // Check email existence using the UserService
            boolean emailExists = userService.existsByEmail(signupRequest.getEmail());
            log.info("Email exists check result (in current schema): {}", emailExists);

            if (emailExists) {
                log.warn("Email '{}' already exists in tenant: {}", signupRequest.getEmail(), TenantContextHolder.getTenantId());
                return ResponseEntity
                        .badRequest()
                        .body("Error: Email is already in use in this tenant!");
            }

            // Create new user's account with just the username part (without tenant)
            User user = new User();
            user.setUsername(username); // Store only the username part
            user.setEmail(signupRequest.getEmail());
            user.setPassword(passwordEncoder.encode(signupRequest.getPassword()));

            // Save the user in the current tenant schema using the UserService
            // Use the tenant-specific createUser method to ensure the user is saved in the correct schema
            log.info("Saving user '{}' in tenant: {}", username, targetTenantId);
            userService.createUser(user, targetTenantId);
            log.info("User registered successfully: {} in tenant: {}", username, targetTenantId);

            return ResponseEntity.status(HttpStatus.CREATED)
                    .body("User registered successfully in tenant: " + targetTenantId);
        } finally {
            // Clear tenant context
            log.info("Clearing tenant context after registration");
            TenantContextHolder.clear();
        }
    }

    @PostMapping("/add-user-to-tenant")
    @Operation(summary = "Add User to Existing Tenant", description = "Add a new user to an existing tenant schema. If tenantSchemaName is not provided, it will be derived from email domain.")
    @Transactional
    public ResponseEntity<?> addUserToTenant(@Valid @RequestBody AddUserToTenantRequest request) {
        log.info("Adding user '{}' with email '{}' to tenant: {}", request.getUsername(), request.getEmail(), request.getTenantSchemaName());

        // Clear any existing tenant context to start fresh
        TenantContextHolder.clear();

        try {
            // Determine the target tenant schema
            String targetTenantSchema = request.getTenantSchemaName();

            // If no tenant schema provided, derive from email domain
            if (targetTenantSchema == null || targetTenantSchema.trim().isEmpty()) {
                targetTenantSchema = tenantUtils.extractTenantSchemaFromEmail(request.getEmail());
                if (targetTenantSchema == null) {
                    return ResponseEntity
                            .badRequest()
                            .body("Error: Could not determine tenant schema from email domain. Please provide tenantSchemaName explicitly.");
                }
                log.info("Derived tenant schema '{}' from email domain", targetTenantSchema);
            }

            // Validate that the tenant exists
            TenantContextHolder.forceTenantContext(TenantContextHolder.getDefaultTenant());

            if (!tenantRepository.existsBySchemaName(targetTenantSchema)) {
                log.warn("Tenant with schema name '{}' does not exist", targetTenantSchema);
                return ResponseEntity
                        .badRequest()
                        .body("Error: Tenant '" + targetTenantSchema + "' does not exist!");
            }

            // Get the tenant to verify it's active
            Tenant tenant = tenantRepository.findBySchemaName(targetTenantSchema)
                    .orElseThrow(() -> new RuntimeException("Tenant not found"));

            if (!tenant.getIsActive()) {
                log.warn("Tenant '{}' is not active", targetTenantSchema);
                return ResponseEntity
                        .badRequest()
                        .body("Error: Tenant '" + targetTenantSchema + "' is not active!");
            }

            // Set tenant context to the target tenant
            log.info("Setting tenant context to: {}", targetTenantSchema);
            TenantContextHolder.forceTenantContext(targetTenantSchema);

            // Check if username already exists in the target tenant schema
            log.info("Checking if username '{}' exists in tenant: {}", request.getUsername(), targetTenantSchema);
            boolean usernameExists = userService.existsByUsername(request.getUsername());

            if (usernameExists) {
                log.warn("Username '{}' already exists in tenant: {}", request.getUsername(), targetTenantSchema);
                return ResponseEntity
                        .badRequest()
                        .body("Error: Username '" + request.getUsername() + "' already exists in tenant '" + targetTenantSchema + "'!");
            }

            // Check email existence in the target tenant schema
            boolean emailExists = userService.existsByEmail(request.getEmail());

            if (emailExists) {
                log.warn("Email '{}' already exists in tenant: {}", request.getEmail(), targetTenantSchema);
                return ResponseEntity
                        .badRequest()
                        .body("Error: Email '" + request.getEmail() + "' already exists in tenant '" + targetTenantSchema + "'!");
            }

            // Create new user
            User user = new User();
            user.setUsername(request.getUsername());
            user.setEmail(request.getEmail());
            user.setPassword(passwordEncoder.encode(request.getPassword()));
            user.setIsActive(true);

            // Save the user in the target tenant schema
            log.info("Saving user '{}' in tenant: {}", request.getUsername(), targetTenantSchema);
            userService.createUser(user, targetTenantSchema);

            log.info("User '{}' added successfully to tenant: {}", request.getUsername(), targetTenantSchema);

            return ResponseEntity.ok().body("User '" + request.getUsername() + "' added successfully to tenant '" + targetTenantSchema + "'!");

        } catch (Exception e) {
            log.error("Error adding user to tenant: {}", e.getMessage(), e);
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error: Failed to add user to tenant. " + e.getMessage());
        } finally {
            // Clear tenant context
            log.info("Clearing tenant context after adding user to tenant");
            TenantContextHolder.clear();
        }
    }

    @PostMapping("/list-users-in-tenant")
    @Operation(summary = "List Users in Tenant", description = "List all users in a specific tenant schema. If tenantSchemaName is not provided, it will be derived from email domain.")
    public ResponseEntity<?> listUsersInTenant(@Valid @RequestBody ListUsersInTenantRequest request) {
        log.info("Listing users in tenant: {}", request.getTenantSchemaName());

        // Clear any existing tenant context to start fresh
        TenantContextHolder.clear();

        try {
            String targetTenantSchema = request.getTenantSchemaName();

            // Validate that the tenant exists
            TenantContextHolder.forceTenantContext(TenantContextHolder.getDefaultTenant());

            if (!tenantRepository.existsBySchemaName(targetTenantSchema)) {
                log.warn("Tenant with schema name '{}' does not exist", targetTenantSchema);
                return ResponseEntity
                        .badRequest()
                        .body("Error: Tenant '" + targetTenantSchema + "' does not exist!");
            }

            // Get the tenant to verify it's active
            Tenant tenant = tenantRepository.findBySchemaName(targetTenantSchema)
                    .orElseThrow(() -> new RuntimeException("Tenant not found"));

            if (!tenant.getIsActive()) {
                log.warn("Tenant '{}' is not active", targetTenantSchema);
                return ResponseEntity
                        .badRequest()
                        .body("Error: Tenant '" + targetTenantSchema + "' is not active!");
            }

            // Set tenant context to the target tenant
            log.info("Setting tenant context to: {}", targetTenantSchema);
            TenantContextHolder.forceTenantContext(targetTenantSchema);

            // Get all users in the tenant
            List<User> users = userService.getAllUsers();

            log.info("Found {} users in tenant: {}", users.size(), targetTenantSchema);

            return ResponseEntity.ok(users);

        } catch (Exception e) {
            log.error("Error listing users in tenant: {}", e.getMessage(), e);
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error: Failed to list users in tenant. " + e.getMessage());
        } finally {
            // Clear tenant context
            log.info("Clearing tenant context after listing users in tenant");
            TenantContextHolder.clear();
        }
    }

    @PostMapping("/get-login-format")
    @Operation(summary = "Get Login Format", description = "Get the correct login format for a user based on their email domain")
    public ResponseEntity<?> getLoginFormat(@Valid @RequestBody Map<String, String> request) {
        String email = request.get("email");
        String username = request.get("username");

        if (email == null || email.isEmpty()) {
            return ResponseEntity.badRequest().body("Error: Email is required");
        }

        if (username == null || username.isEmpty()) {
            return ResponseEntity.badRequest().body("Error: Username is required");
        }

        try {
            // Extract tenant schema from email domain
            String tenantSchema = tenantUtils.extractTenantSchemaFromEmail(email);

            if (tenantSchema == null || tenantSchema.isEmpty()) {
                return ResponseEntity.badRequest().body("Error: Could not determine tenant schema from email domain");
            }

            // Check if tenant exists
            TenantContextHolder.forceTenantContext(TenantContextHolder.getDefaultTenant());
            boolean tenantExists = tenantRepository.existsBySchemaName(tenantSchema);

            if (!tenantExists) {
                return ResponseEntity.badRequest().body("Error: Tenant '" + tenantSchema + "' does not exist. Please register first.");
            }

            String loginFormat = username + "@" + tenantSchema;

            Map<String, Object> response = new HashMap<>();
            response.put("email", email);
            response.put("username", username);
            response.put("tenantSchema", tenantSchema);
            response.put("loginFormat", loginFormat);
            response.put("message", "Use this format to login: " + loginFormat);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error determining login format: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error: Failed to determine login format. " + e.getMessage());
        } finally {
            TenantContextHolder.clear();
        }
    }

    @PostMapping("/logout")
    @Operation(summary = "Logout", description = "Logout the current user")
    public ResponseEntity<?> logout() {
        // Get the current user before clearing the context
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = null;

        if (authentication != null && authentication.isAuthenticated()) {
            username = authentication.getName();
            log.info("Logging out user: {}", username);

            // Update login status to false
            try {
                userService.updateLoginStatus(username, false);
                log.info("Login status updated to false for user: {}", username);
            } catch (Exception e) {
                log.warn("Failed to update login status for user: {}, error: {}", username, e.getMessage());
            }
        }

        // Clear the security context
        SecurityContextHolder.clearContext();

        // Clear the tenant context to prevent any stale tenant information
        TenantContextHolder.clear();
        log.info("Security context and tenant context cleared for logout");

        return ResponseEntity.ok().body("Logged out successfully");
    }

    @GetMapping("/logged-in-users")
    @Operation(summary = "Get logged-in users", description = "Get all currently logged-in users in the current tenant")
    public ResponseEntity<?> getLoggedInUsers() {
        try {
            List<User> loggedInUsers = userService.getLoggedInUsers();

            // Create a simplified response without sensitive information
            List<Map<String, Object>> response = loggedInUsers.stream()
                .map(user -> {
                    Map<String, Object> userInfo = new HashMap<>();
                    userInfo.put("id", user.getId());
                    userInfo.put("username", user.getUsername());
                    userInfo.put("email", user.getEmail());
                    userInfo.put("isActive", user.getIsActive());
                    userInfo.put("isLoggedIn", user.getIsLoggedIn());
                    return userInfo;
                })
                .toList();

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error getting logged-in users: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Error getting logged-in users");
        }
    }
}

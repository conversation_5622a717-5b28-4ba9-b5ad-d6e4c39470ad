# Database Indexing and Optimization

This document describes the database indexing and optimization strategies implemented in the CMS application.

## Overview

Database indexes have been added to improve query performance across the application. These indexes target:

1. Foreign key columns to speed up join operations
2. Columns frequently used in WHERE clauses
3. Columns used for lookups by name or ID
4. JSONB data for efficient text search and path-based queries

## Implementation Details

### Index Creation

Indexes are created through the `indexes.sql` script located in `src/main/resources/db/indexes.sql`. This script is executed after the main schema initialization through the `DatabaseIndexingConfig` class.

### JSONB Optimization

The `content_entries` table uses PostgreSQL's JSONB data type for storing flexible content structures. To optimize queries on this data:

1. A GIN index has been added to the `data_json` column:
   ```sql
   CREATE INDEX IF NOT EXISTS idx_content_entries_data_json_gin ON content_entries USING GIN (data_json);
   ```

2. New repository methods have been added to `ContentEntryRepository`:
   - `findByCollectionIdAndDataJsonContaining` - For text search within JSON data
   - `findByJsonPath` - For querying using JSON path expressions
   - `findByJsonPathAndValue` - For querying specific values at JSON paths

### API Endpoints

New API endpoints have been added to leverage these optimizations:

1. **Text Search**:
   ```
   GET /content-entries/search/collection/{collectionId}?searchText=example
   ```
   Searches for content entries in a specific collection containing the given text.

2. **JSON Path Search**:
   ```
   GET /content-entries/search/json-path?jsonPath=$.title
   ```
   Finds content entries that have the specified JSON path.

3. **JSON Path Value Search**:
   ```
   GET /content-entries/search/json-path-value?jsonPath=$.title&jsonValue="Example Title"
   ```
   Finds content entries where the specified JSON path contains the given value.

## Performance Benefits

These optimizations provide several benefits:

- **Faster Queries**: Indexes reduce the need for full table scans
- **Efficient Joins**: Foreign key indexes speed up related data retrieval
- **Improved Search**: GIN index enables efficient text search within JSON data
- **Scalability**: Better performance with larger datasets

## Usage Examples

### Searching Content by Text

```java
// Find all blog posts containing "example" in any field
List<ContentEntry> results = contentEntryService.searchContentEntriesByCollectionId(1, "example");
```

### Querying Specific JSON Fields

```java
// Find all content entries with a "title" field
List<ContentEntry> results = contentEntryService.findContentEntriesByJsonPath("$.title");

// Find all content entries with title "Example Post"
List<ContentEntry> results = contentEntryService.findContentEntriesByJsonPathAndValue("$.title", "\"Example Post\"");
```

## Maintenance Considerations

While indexes improve read performance, they can slightly impact write performance due to the overhead of maintaining the indexes. This trade-off is generally acceptable for CMS applications where read operations are more frequent than writes.

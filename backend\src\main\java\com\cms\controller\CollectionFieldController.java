package com.cms.controller;

import com.cms.entity.CollectionField;
import com.cms.entity.CollectionFieldConfig;
import com.cms.service.CollectionFieldService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/collection-fields")
@Tag(name = "Collection Field", description = "Collection Field API")
@Slf4j
public class CollectionFieldController {

    @Autowired
    private CollectionFieldService collectionFieldService;

    @GetMapping("/getAll")
    @Operation(summary = "Get all collection fields", description = "Returns a list of all collection fields or 204 if none found")
    public ResponseEntity<?> getAllCollectionFields() {
        List<CollectionField> fields = collectionFieldService.getAllCollectionFields();
        if (fields.isEmpty()) {
            return ResponseEntity.status(HttpStatus.NO_CONTENT)
                    .body(Map.of("message", "No data in the table"));
        }
        return ResponseEntity.ok(fields);
    }

    @GetMapping("/getById/{id}")
    @Operation(summary = "Get collection field by ID", description = "Returns a collection field by its ID or 204 if not found")
    public ResponseEntity<?> getCollectionFieldById(@PathVariable Integer id) {
        try {
            CollectionField field = collectionFieldService.getCollectionFieldById(id);
            return ResponseEntity.ok(field);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.NO_CONTENT)
                    .body(Map.of("message", "No content found for collection field with id: " + id));
        }
    }

    @GetMapping("/getByCollectionId/{collectionId}")
    @Operation(summary = "Get collection fields by collection ID",
               description = "Returns a list of collection fields for a specific collection or 204 if none found")
    public ResponseEntity<?> getCollectionFieldsByCollectionId(@PathVariable Integer collectionId) {
        try {
            List<CollectionField> fields = collectionFieldService.getCollectionFieldsByCollectionId(collectionId);
            if (fields.isEmpty()) {
                return ResponseEntity.status(HttpStatus.NO_CONTENT)
                        .body(Map.of("message", "No fields found for collection with ID: " + collectionId));
            }
            return ResponseEntity.ok(fields);
        } catch (Exception e) {
            // Handle case where collection doesn't exist
            return ResponseEntity.status(HttpStatus.NO_CONTENT)
                    .body(Map.of("message", "No fields found for collection with ID: " + collectionId));
        }
    }

    @GetMapping("/getNextId")
    @Operation(summary = "Get next available ID", description = "Returns the next available ID for a collection field")
    public ResponseEntity<Map<String, Integer>> getNextId() {
        Integer nextId = collectionFieldService.getNextAvailableId();
        Map<String, Integer> response = new HashMap<>();
        response.put("nextId", nextId);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/create")
    @Operation(summary = "Create a new collection field", description = "Creates a new collection field")
    public ResponseEntity<?> createCollectionField(@Valid @RequestBody CollectionField collectionField) {
        log.info("Received collection field creation request:");
        log.info("Field Type ID: {}", collectionField.getFieldType() != null ? collectionField.getFieldType().getId() : "NULL");
        log.info("Collection ID: {}", collectionField.getCollection() != null ? collectionField.getCollection().getId() : "NULL");
        log.info("Display Preference: {}", collectionField.getDisplayPreference());
        log.info("Additional Information: {}", collectionField.getAdditionalInformation());
        log.info("Configs count: {}", collectionField.getConfigs() != null ? collectionField.getConfigs().size() : "NULL");

        if (collectionField.getConfigs() != null) {
            for (int i = 0; i < collectionField.getConfigs().size(); i++) {
                CollectionFieldConfig config = collectionField.getConfigs().get(i);
                log.info("Config {}: Field Config ID = {}, Value = {}",
                    i,
                    config.getFieldConfig() != null ? config.getFieldConfig().getId() : "NULL",
                    config.getFieldConfigValue());
            }
        }

        // Check if collection is null
        if (collectionField.getCollection() == null) {
            return ResponseEntity.status(700)
                    .body(Map.of("error", "Not null constraint: Collection is null"));
        }

        // Check if collection ID is null
        if (collectionField.getCollection().getId() == null) {
            return ResponseEntity.status(700)
                    .body(Map.of("error", "Not null constraint: Collection ID is null"));
        }

        // Check if field type is null
        if (collectionField.getFieldType() == null) {
            return ResponseEntity.status(700)
                    .body(Map.of("error", "Not null constraint: Field type is null"));
        }

        // Check if field type ID is null
        if (collectionField.getFieldType().getId() == null) {
            return ResponseEntity.status(700)
                    .body(Map.of("error", "Not null constraint: Field type ID is null"));
        }

        try {
            CollectionField createdField = collectionFieldService.createCollectionField(collectionField);
            return new ResponseEntity<>(createdField, HttpStatus.CREATED);
        } catch (Exception e) {
            // If it's a null constraint violation, return 700
            if (e.getMessage() != null && e.getMessage().toLowerCase().contains("null")) {
                return ResponseEntity.status(700)
                        .body(Map.of("error", "Not null constraint: " + e.getMessage()));
            }
            // For other errors, return 500
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to create collection field: " + e.getMessage()));
        }
    }

    @PutMapping("/update/{id}")
    @Operation(summary = "Update a collection field", description = "Updates an existing collection field")
    public ResponseEntity<?> updateCollectionField(
            @PathVariable Integer id, @Valid @RequestBody CollectionField collectionField) {
        // Check if component ID is null
        if (id == null) {
            return ResponseEntity.status(700)
                    .body(Map.of("error", "Not null constraint: Component ID is null"));
        }

        // Check if collection is null
        if (collectionField.getCollection() == null) {
            return ResponseEntity.status(700)
                    .body(Map.of("error", "Not null constraint: Collection is null"));
        }
        
        // Prevent null additionalInformation updates
        if (collectionField.getAdditionalInformation() == null) {
            // Get existing field to preserve additionalInformation
            CollectionField existingField = collectionFieldService.getCollectionFieldById(id);
            collectionField.setAdditionalInformation(existingField.getAdditionalInformation());
            log.info("Preserved additionalInformation for field ID {}: {}", id, existingField.getAdditionalInformation());
        }

        try {
            CollectionField updatedField = collectionFieldService.updateCollectionField(id, collectionField);
            return ResponseEntity.ok(updatedField);
        } catch (Exception e) {
            return ResponseEntity.status(700).body(Map.of("error", e.getMessage()));
        }
    }

    @DeleteMapping("/deleteById/{id}")
    @Operation(summary = "Delete a collection field", description = "Deletes a collection field or returns 204 if it doesn't exist")
    public ResponseEntity<Map<String, String>> deleteCollectionField(@PathVariable Integer id) {
        try {
            // Check if the collection field exists
            try {
                collectionFieldService.getCollectionFieldById(id);
            } catch (Exception e) {
                // Return 204 with no content message
                return ResponseEntity.status(HttpStatus.NO_CONTENT)
                        .body(Map.of("message", "No content found for collection field with id: " + id));
            }

            // Delete the collection field
            collectionFieldService.deleteCollectionField(id);

            // Return 204 with success message
            return ResponseEntity.status(HttpStatus.NO_CONTENT)
                    .body(Map.of("message", "Successfully deleted collection field with id: " + id));
        } catch (Exception e) {
            // If an unexpected error occurs, return 500 with error message
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to process delete request for collection field with id: " + id + ". Error: " + e.getMessage()));
        }
    }
}

package com.cms.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "tenants")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class Tenant extends Auditable {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "cms_tenant_seq")
    @SequenceGenerator(name = "cms_tenant_seq", sequenceName = "cms_tenant_seq", initialValue = 100, allocationSize = 1)
    private Integer id;
    
    @NotBlank(message = "Tenant name is required")
    @Column(name = "name", nullable = false)
    private String name;
    
    @NotBlank(message = "Schema name is required")
    @Column(name = "schema_name", nullable = false, unique = true)
    private String schemaName;
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    @Column(name = "description")
    private String description;
}

package com.cms.dto.simplified;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SimplifiedCollectionDTO {
    private Integer id;
    private String collectionName;
    private String collectionDesc;
    private String collectionApiId;
    private List<SimplifiedComponentDTO> components = new ArrayList<>();
    private List<SimplifiedFieldDTO> fields = new ArrayList<>();
}

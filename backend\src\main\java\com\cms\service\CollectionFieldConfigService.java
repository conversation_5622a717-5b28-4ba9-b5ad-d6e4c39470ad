package com.cms.service;

import com.cms.entity.CollectionFieldConfig;

import java.util.List;
import java.util.Optional;

public interface CollectionFieldConfigService {
    List<CollectionFieldConfig> getAllCollectionFieldConfigs();
    List<CollectionFieldConfig> getCollectionFieldConfigsByCollectionFieldId(Integer collectionFieldId);
    Optional<CollectionFieldConfig> getCollectionFieldConfigById(Integer id);
    CollectionFieldConfig createCollectionFieldConfig(CollectionFieldConfig collectionFieldConfig);
    List<CollectionFieldConfig> createCollectionFieldConfigs(List<CollectionFieldConfig> collectionFieldConfigs);
    CollectionFieldConfig updateCollectionFieldConfig(Integer id, CollectionFieldConfig collectionFieldConfig);
    void deleteCollectionFieldConfig(Integer id);
}

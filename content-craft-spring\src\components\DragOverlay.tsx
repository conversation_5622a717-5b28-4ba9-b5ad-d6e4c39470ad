import React from 'react';
import { Upload } from 'lucide-react';

interface DragOverlayProps {
  visible: boolean;
}

export function DragOverlay({ visible }: DragOverlayProps) {
  if (!visible) return null;
  
  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="bg-primary/5 border-2 border-dashed border-primary rounded-lg p-12 flex flex-col items-center justify-center max-w-md mx-auto">
        <Upload className="h-16 w-16 text-primary mb-4 animate-bounce" />
        <h2 className="text-2xl font-bold mb-2">Drop Files to Upload</h2>
        <p className="text-center text-muted-foreground">
          Release to upload your files to the media library
        </p>
      </div>
    </div>
  );
}

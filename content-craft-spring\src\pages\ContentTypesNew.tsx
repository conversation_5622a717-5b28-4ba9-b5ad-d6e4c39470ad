import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Plus, Folder, Edit, ArrowUp, Search, X, Trash2, Pencil, MoreHorizontal, MoreVertical, ArrowLeft } from 'lucide-react';
import { useClickedClient, useClickedParentCategory, useCollectionStore } from '@/lib/store';
import { collectionsApi, categoriesApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import BasicCollectionDialog from '@/components/content-type/BasicCollectionDialog';
import { RenameCategoryDialog } from '@/components/dialogs/RenameCategoryDialog';
import { DeleteCategoryDialog } from '@/components/dialogs/DeleteCategoryDialog';

export default function ContentTypesNew() {
  const navigate = useNavigate();
  const { toast } = useToast();
  // Get collections from store and local state
  const { collections: storeCollections } = useCollectionStore();
  const [collections, setCollections] = React.useState<any[]>([]);
  // const { clientId } = useParams<{ clientId: string }>();
  const parentCategoryId = useClickedParentCategory.getState().parentCategoryId;
  const [client, setClient] = React.useState<any>(null);

  // State
  const [createCategoryDialogOpen, setCreateCategoryDialogOpen] = React.useState(false);
  const [createCollectionDialogOpen, setCreateCollectionDialogOpen] = React.useState(false);
  const [categoryName, setCategoryName] = React.useState('');
  const [categoryDescription, setCategoryDescription] = React.useState('');
  const [categories, setCategories] = React.useState<any[]>([]);
  const [loadingCategories, setLoadingCategories] = React.useState(false);
  const [loadingCollections, setLoadingCollections] = React.useState(false);
  const [categoryCollectionCounts, setCategoryCollectionCounts] = React.useState<Record<number, number>>({});
  const [showScrollButton, setShowScrollButton] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [renameCategoryDialogOpen, setRenameCategoryDialogOpen] = React.useState(false);
  const [deleteCategoryDialogOpen, setDeleteCategoryDialogOpen] = React.useState(false);
  const [selectedCategory, setSelectedCategory] = React.useState<any>(null);

  // Function to check database connection
  const checkDatabaseConnection = async () => {
    try {
      await categoriesApi.getByParentCategoryId(parentCategoryId);
      console.log('Database connection successful');
      return true;
    } catch (error: any) {
      console.error('Database connection error:', error);
      toast({
        title: 'Database Connection Error',
        description: 'Failed to connect to the database',
        variant: 'destructive',
      });
      return false;
    }
  };

  // Fetch categories and collections on component mount
  React.useEffect(() => {
    const fetchData = async () => {
      if (!parentCategoryId) return;

      try {
        setLoadingCategories(true);
        setLoadingCollections(true);

        // First check database connection
        const isConnected = await checkDatabaseConnection();
        if (!isConnected) {
          return;
        }

        // Fetch categories
        const categoriesResponse = await categoriesApi.getByParentCategoryId(parentCategoryId);
        console.log('Categories fetched from API:', categoriesResponse);
        setCategories(categoriesResponse.data || []);

        // Fetch collections for each category to calculate collection counts
        const counts: Record<number, number> = {};

        if (categoriesResponse.data && Array.isArray(categoriesResponse.data)) {
          // For each category, fetch its collections
          for (const category of categoriesResponse.data) {
            try {
              const collectionsResponse = await collectionsApi.getByCategoryId(category.id.toString());
              const categoryCollections = collectionsResponse.data || [];
              counts[category.id] = categoryCollections.length;
              console.log(`Category ${category.categoryName} has ${categoryCollections.length} collections`);
            } catch (error) {
              console.error(`Error fetching collections for category ${category.id}:`, error);
              counts[category.id] = 0;
            }
          }
        }

        setCategoryCollectionCounts(counts);
        console.log('Category collection counts:', counts);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load data',
          variant: 'destructive',
        });
      } finally {
        setLoadingCategories(false);
        setLoadingCollections(false);
      }
    };

    fetchData();
  }, [toast]);

  // Handle scroll event to show/hide scroll-to-top button
  React.useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowScrollButton(true);
      } else {
        setShowScrollButton(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Safe function to get category ID from a collection
  const getCategoryId = (collection) => {
    try {
      if (!collection) return null;
      if (!collection.category) return null;

      if (typeof collection.category === 'object' && collection.category !== null) {
        return collection.category.id;
      } else if (typeof collection.category === 'number') {
        return collection.category;
      } else if (typeof collection.category === 'string') {
        return parseInt(collection.category);
      }
      return null;
    } catch (error) {
      console.error('Error in getCategoryId:', error);
      return null;
    }
  };

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const handleCreateCategory = () => {
    // Reset form fields
    setCategoryName('');
    setCategoryDescription('');
    setCreateCategoryDialogOpen(true);
  };

  const handleCreateCollection = () => {
    setCreateCollectionDialogOpen(true);
  };

  const handleCloseCollectionDialog = () => {
    setCreateCollectionDialogOpen(false);
  };

  const handleSaveCollection = async (data: any) => {
    try {
      console.log('Handling save collection with data:', data);

      // First check database connection
      const isConnected = await checkDatabaseConnection();
      if (!isConnected) {
        throw new Error('Database connection failed');
      }

      // Create the collection data
      const collectionData = {
        id: null, // Let the backend assign the ID
        collectionName: data.name,
        collectionDesc: `${data.name} collection`,
        collectionApiId: data.apiIdSingular,
        // Store additional properties in the description field as JSON
        additionalInformation: JSON.stringify({
          apiIdPlural: data.apiIdPlural,
          draftAndPublish: data.draftAndPublish,
          isInternationally: data.isInternationally
        }),
        fields: [],
      };

      // Add category ID if selected
      if (data.categoryId && data.categoryId !== '') {
        console.log(`Adding category ID ${data.categoryId} to collection`);
        collectionData.category = { id: parseInt(data.categoryId) };
      }

      console.log('Creating collection with data:', collectionData);
      try {
        const response = await collectionsApi.create(collectionData);
        console.log('Collection created successfully:', response.data);

        toast({
          title: 'Collection created',
          description: 'Your collection has been created successfully',
        });

        // Close the dialog
        setCreateCollectionDialogOpen(false);

        // Navigate to the edit page
        navigate(`/content-types/edit/${response.data.id}`);
      } catch (apiError: any) {
        console.error('API error creating collection:', apiError);
        toast({
          title: 'Error',
          description: 'Failed to create collection',
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      console.error('Error creating collection:', error);
      toast({
        title: 'Error',
        description: 'Failed to create collection',
        variant: 'destructive',
      });
    }
  };

  const handleCreateCategorySubmit = async () => {
    if (!categoryName.trim()) {
      toast({
        title: "Validation Error",
        description: "Category name is required",
        variant: "destructive"
      });
      return;
    }

    try {
      // First check database connection
      const isConnected = await checkDatabaseConnection();
      if (!isConnected) {
        throw new Error('Database connection failed');
      }

      console.log('Creating category with name:', categoryName.trim());

      // Create the category data
      const categoryData = {
        parentCategory :
        {id: parentCategoryId},
  // "client":{
  //   id: clientId
  // },

        categoryName: categoryName.trim()
      };

      console.log('Creating category with data:', categoryData);

      // Use the API service instead of direct fetch
      try {
        const response = await categoriesApi.create(categoryData);
        console.log('Category created successfully:', response.data);
      } catch (apiError: any) {
        console.error('API error:', apiError);
        throw new Error(`Failed to create category: ${apiError.message}`);
      }

      toast({
        title: "Category created",
        description: `Category "${categoryName}" has been created successfully`,
      });

      // Refresh categories list and collection counts
      try {
        const categoriesResponse = await categoriesApi.getByParentCategoryId(parentCategoryId);
        setCategories(categoriesResponse.data || []);

        // Recalculate collection counts
        const counts: Record<number, number> = {};
        if (categoriesResponse.data && Array.isArray(categoriesResponse.data)) {
          for (const category of categoriesResponse.data) {
            try {
              const collectionsResponse = await collectionsApi.getByCategoryId(category.id.toString());
              const categoryCollections = collectionsResponse.data || [];
              counts[category.id] = categoryCollections.length;
            } catch (error) {
              console.error(`Error fetching collections for category ${category.id}:`, error);
              counts[category.id] = 0;
            }
          }
        }
        setCategoryCollectionCounts(counts);
      } catch (refreshError) {
        console.error('Error refreshing categories:', refreshError);
      }

      // Close the dialog and reset form
      setCreateCategoryDialogOpen(false);
      setCategoryName('');
      setCategoryDescription('');
    } catch (error: any) {
      console.error('Error creating category:', error);
      toast({
        title: "Error",
        description: 'Failed to create category: ' + error.message,
        variant: "destructive",
      });
    }
  };

  // Handle rename category
  const handleRenameCategory = (category: any) => {
    setSelectedCategory(category);
    setRenameCategoryDialogOpen(true);
  };

  // Handle save renamed category
  const handleSaveRenamedCategory = async (data: { categoryName: string }) => {
    if (!selectedCategory) return;

    try {
      // Create the category data for update
      const categoryData = {
        categoryName: data.categoryName,
        description: selectedCategory.description || ''
      };

      console.log('Updating category with data:', categoryData);

      // Call the API to update the category
      const response = await categoriesApi.update(selectedCategory.id.toString(), categoryData);
      console.log('Category updated successfully:', response.data);

      // Update the category in the local state
      const updatedCategories = categories.map(cat =>
        cat.id === selectedCategory.id ? { ...cat, categoryName: data.categoryName } : cat
      );
      setCategories(updatedCategories);

      toast({
        title: 'Success',
        description: 'Category renamed successfully',
      });

      setRenameCategoryDialogOpen(false);
    } catch (error) {
      console.error('Error renaming category:', error);
      toast({
        title: 'Error',
        description: 'Failed to rename category',
        variant: 'destructive',
      });
    }
  };

  // Handle delete category
  const handleDeleteCategory = (category: any) => {
    setSelectedCategory(category);
    setDeleteCategoryDialogOpen(true);
  };

  // Handle confirm delete category
  const handleConfirmDeleteCategory = async () => {
    if (!selectedCategory) return;

    try {
      // Call the API to delete the category
      await categoriesApi.delete(selectedCategory.id.toString());
      console.log('Category deleted successfully');

      // Remove the category from the local state
      const updatedCategories = categories.filter(cat => cat.id !== selectedCategory.id);
      setCategories(updatedCategories);

      // Update collection counts by removing the deleted category
      const updatedCounts = { ...categoryCollectionCounts };
      delete updatedCounts[selectedCategory.id];
      setCategoryCollectionCounts(updatedCounts);

      toast({
        title: 'Success',
        description: 'Category deleted successfully',
      });

      setDeleteCategoryDialogOpen(false);
    } catch (error) {
      console.error('Error deleting category:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete category',
        variant: 'destructive',
      });
    }
  };

  // Filter categories and collections based on search query
  const getFilteredCategories = () => {
    if (!searchQuery.trim()) return categories;

    const query = searchQuery.toLowerCase().trim();
    return categories.filter(category =>
      category.categoryName.toLowerCase().includes(query)
    );
  };

  // Filter collections based on search query
  const getFilteredCollections = () => {
    if (!searchQuery.trim()) return [];

    const query = searchQuery.toLowerCase().trim();
    console.log('Searching collections with query:', query);
    console.log('Collections available:', collections);

    const filtered = collections.filter(collection => {
      if (!collection) return false;

      const nameMatch = collection.name && collection.name.toLowerCase().includes(query);
      const apiIdMatch = collection.apiId && collection.apiId.toLowerCase().includes(query);
      const collectionNameMatch = collection.collectionName && collection.collectionName.toLowerCase().includes(query);

      return nameMatch || apiIdMatch || collectionNameMatch;
    });

    console.log('Filtered collections:', filtered);
    return filtered;
  };

  // Check if there are any search results
  const hasSearchResults = () => {
    return getFilteredCategories().length > 0 || getFilteredCollections().length > 0;
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/parent-categories')}
            className="mr-2"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Parent Categories
          </Button>
          <h1 className="text-3xl font-bold">Categories</h1>
          {searchQuery.trim() && (
            <div className="ml-2 bg-primary/10 text-primary px-3 py-1 rounded-full flex items-center gap-1">
              <span>Search:</span>
              <span className="font-medium">"{searchQuery}"</span>
              <Button
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0 ml-1 rounded-full"
                onClick={() => setSearchQuery('')}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          )}
        </div>
        <div className="flex items-center gap-2">
          <div className="relative w-full md:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search categories and collections..."
              className="pl-8"
              value={searchQuery}
              onChange={handleSearchChange}
            />
          </div>
          <div className="flex gap-2">
            <Button onClick={handleCreateCategory} variant="outline">
              <Folder className="mr-2 h-4 w-4" />
              Create Category
            </Button>
            <Button onClick={handleCreateCollection}>
              <Plus className="mr-2 h-4 w-4" />
              Create Collection
            </Button>
          </div>
        </div>
      </div>

      {/* Categories Section */}
      <div>
        {loadingCategories ? (
          <div className="flex justify-center p-4">
            <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full"></div>
          </div>
        ) : categories.length === 0 && !searchQuery.trim() ? (
          <div className="bg-muted/50 rounded-md p-6 text-center">
            <Folder className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No categories found</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Create categories to organize your collections
            </p>
            <Button onClick={handleCreateCategory} variant="outline">
              <Plus className="mr-2 h-4 w-4" />
              Create Category
            </Button>
          </div>
        ) : searchQuery.trim() && getFilteredCategories().length === 0 && getFilteredCollections().length === 0 ? (
          <div className="bg-muted/50 rounded-md p-6 text-center">
            <h3 className="text-lg font-medium mb-2">No results found</h3>
            <p className="text-sm text-muted-foreground mb-4">
              No categories or collections match your search query "{searchQuery}"
            </p>
            <Button variant="outline" onClick={() => setSearchQuery('')}>
              Clear search
            </Button>
          </div>
        ) : (
          <>
            {/* Show categories section if there are matching categories or no search query */}
            {(getFilteredCategories().length > 0 || !searchQuery.trim()) && (
              <div className="mb-8">
                <h2 className="text-xl font-semibold mb-4">Categories</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {getFilteredCategories().map((category) => (
              <div
                key={category.id}
                className="bg-card border rounded-md shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => navigate(`/content-types/category/${category.id}`)}
              >
                <div className="p-4 border-b">
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-primary/10 text-primary flex items-center justify-center mr-3">
                      <Folder className="h-4 w-4" />
                    </div>
                    <h3 className="font-medium">{category.categoryName}</h3>
                  </div>
                </div>
                <div className="p-4 flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    {loadingCollections ? (
                      <span className="inline-flex items-center">
                        <span className="w-3 h-3 mr-1 rounded-full animate-pulse bg-primary/50"></span>
                        Loading...
                      </span>
                    ) : (
                      <span>
                        {categoryCollectionCounts[category.id] || 0} collections
                      </span>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <div onClick={(e) => e.stopPropagation()}>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-muted">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleRenameCategory(category)}>
                            <Pencil className="h-4 w-4 mr-2" />
                            Rename
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleDeleteCategory(category)}>
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </div>
              </div>
            ))}
                </div>
              </div>
            )}

            {/* Show collections section if there are matching collections and search query is not empty */}
            {getFilteredCollections().length > 0 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">Matching Collections</h2>
                <div className="border rounded-md overflow-hidden">
                  {getFilteredCollections().map((collection) => (
                    <div
                      key={collection.id}
                      className="p-4 border-b hover:bg-muted/50 cursor-pointer"
                      onClick={() => navigate(`/content-types/edit/${collection.id}`)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center mr-3 border border-border">
                            {(collection.collectionName || collection.name || 'U')[0].toUpperCase()}
                          </div>
                          <div>
                            <h3 className="font-medium">{collection.collectionName || collection.name || 'Unnamed Collection'}</h3>
                            <p className="text-xs text-muted-foreground">API ID: {collection.collectionApiId || collection.apiId || 'N/A'}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {collection.category && (
                            <Badge variant="outline" className="text-xs">
                              {categories.find(c => c.id === (typeof collection.category === 'object' ? collection.category.id : collection.category))?.categoryName || 'Category'}
                            </Badge>
                          )}
                          <Badge variant="outline" className="text-xs">
                            {collection.fields?.length || 0} fields
                          </Badge>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              navigate(`/content-types/edit/${collection.id}`);
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Create category dialog */}
      <Dialog open={createCategoryDialogOpen} onOpenChange={setCreateCategoryDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader className="flex flex-row items-center">
            <div className="flex items-center">
              <div className="flex items-center justify-center w-8 h-8 mr-2 bg-primary text-primary-foreground rounded">
                C
              </div>
              <DialogTitle>Create a category</DialogTitle>
            </div>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div>
              <h3 className="text-lg font-medium">Category</h3>
              <p className="text-sm text-muted-foreground">Create a category to organize your collections</p>
            </div>
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <label htmlFor="categoryName" className="text-sm font-medium">Category Name</label>
                  <Input
                    id="categoryName"
                    placeholder="Enter category name"
                    value={categoryName}
                    onChange={(e) => setCategoryName(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="categoryDescription" className="text-sm font-medium">Description (optional)</label>
                  <Input
                    id="categoryDescription"
                    placeholder="Enter category description"
                    value={categoryDescription}
                    onChange={(e) => setCategoryDescription(e.target.value)}
                  />
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setCreateCategoryDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleCreateCategorySubmit} disabled={!categoryName.trim()}>Create Category</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Create collection dialog */}
      <BasicCollectionDialog
        isOpen={createCollectionDialogOpen}
        onClose={handleCloseCollectionDialog}
        onSave={handleSaveCollection}
      />

      {/* Scroll to top button */}
      {showScrollButton && (
        <Button
          className="fixed bottom-6 right-6 rounded-full w-12 h-12 shadow-lg flex items-center justify-center bg-primary hover:bg-primary/90 transition-all"
          onClick={scrollToTop}
          aria-label="Scroll to top"
        >
          <ArrowUp className="h-5 w-5 text-white" />
        </Button>
      )}

      {/* Rename category dialog */}
      {selectedCategory && (
        <RenameCategoryDialog
          isOpen={renameCategoryDialogOpen}
          onClose={() => setRenameCategoryDialogOpen(false)}
          onSave={handleSaveRenamedCategory}
          initialName={selectedCategory.categoryName}
        />
      )}

      {/* Delete category dialog */}
      {selectedCategory && (
        <DeleteCategoryDialog
          isOpen={deleteCategoryDialogOpen}
          onClose={() => setDeleteCategoryDialogOpen(false)}
          onConfirm={handleConfirmDeleteCategory}
          categoryName={selectedCategory.categoryName}
          collectionCount={categoryCollectionCounts[selectedCategory.id] || 0}
        />
      )}
    </div>
  );
}

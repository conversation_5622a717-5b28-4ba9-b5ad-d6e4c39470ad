package com.cms.payload;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Request payload for reordering collection components and fields
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReorderItemsRequest {
    
    /**
     * List of component IDs in display order
     */
    private List<Integer> componentIds;
    
    /**
     * List of field IDs in display order
     */
    private List<Integer> fieldIds;
}

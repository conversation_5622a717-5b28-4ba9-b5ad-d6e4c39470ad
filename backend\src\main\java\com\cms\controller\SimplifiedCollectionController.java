package com.cms.controller;

import com.cms.dto.simplified.SimplifiedCollectionDTO;
import com.cms.entity.CollectionListing;
import com.cms.mapper.SimplifiedCollectionMapper;
import com.cms.service.CollectionListingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/simplified-collections")
@RequiredArgsConstructor
@Tag(name = "Simplified Collection", description = "Simplified Collection API")
public class SimplifiedCollectionController {

    private final CollectionListingService collectionListingService;
    private final SimplifiedCollectionMapper simplifiedCollectionMapper;

    @GetMapping("/getAll")
    @Operation(
        summary = "Get all collections in simplified format",
        description = "Returns a list of all collections in a simplified format",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "List of simplified collections retrieved successfully",
                content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = SimplifiedCollectionDTO.class),
                    examples = {
                        @ExampleObject(
                            value = "[{\"id\":100,\"collectionName\":\"Example Collection\",\"collectionDesc\":\"This is an example collection\",\"collectionApiId\":\"example_collection\",\"additionalInformation\":\"Additional information about the collection\",\"disclaimerText\":\"Disclaimer text for the collection\"}]"
                        )
                    }
                )
            ),
            @ApiResponse(
                responseCode = "204",
                description = "No collections found",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<List<SimplifiedCollectionDTO>> getAllSimplifiedCollections() {
        List<CollectionListing> collections = collectionListingService.getAllCollectionsWithDetails();
        return ResponseEntity.ok(simplifiedCollectionMapper.toDTOList(collections));
    }

    @GetMapping("/getById/{id}")
    @Operation(
        summary = "Get collection by ID in simplified format",
        description = "Returns a collection by its ID in a simplified format",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "Simplified collection retrieved successfully",
                content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = SimplifiedCollectionDTO.class),
                    examples = {
                        @ExampleObject(
                            value = "{\"id\":100,\"collectionName\":\"Example Collection\",\"collectionDesc\":\"This is an example collection\",\"collectionApiId\":\"example_collection\",\"additionalInformation\":\"Additional information about the collection\",\"disclaimerText\":\"Disclaimer text for the collection\"}"
                        )
                    }
                )
            ),
            @ApiResponse(
                responseCode = "204",
                description = "Collection not found",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<SimplifiedCollectionDTO> getSimplifiedCollectionById(@PathVariable Integer id) {
        return collectionListingService.getCollectionByIdWithDetails(id)
                .map(collection -> ResponseEntity.ok(simplifiedCollectionMapper.toDTO(collection)))
                .orElse(ResponseEntity.noContent().build());
    }

    @GetMapping("/getByApiId/{apiId}")
    @Operation(
        summary = "Get collection by API ID in simplified format",
        description = "Returns a collection by its API ID in a simplified format",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "Simplified collection retrieved successfully",
                content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = SimplifiedCollectionDTO.class),
                    examples = {
                        @ExampleObject(
                            value = "{\"id\":100,\"collectionName\":\"Example Collection\",\"collectionDesc\":\"This is an example collection\",\"collectionApiId\":\"example_collection\",\"additionalInformation\":\"Additional information about the collection\",\"disclaimerText\":\"Disclaimer text for the collection\"}"
                        )
                    }
                )
            ),
            @ApiResponse(
                responseCode = "204",
                description = "Collection not found",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<SimplifiedCollectionDTO> getSimplifiedCollectionByApiId(@PathVariable String apiId) {
        return collectionListingService.getCollectionByApiIdWithDetails(apiId)
                .map(collection -> ResponseEntity.ok(simplifiedCollectionMapper.toDTO(collection)))
                .orElse(ResponseEntity.noContent().build());
    }
}

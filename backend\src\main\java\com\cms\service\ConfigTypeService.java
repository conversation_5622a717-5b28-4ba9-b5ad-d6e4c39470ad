package com.cms.service;

import com.cms.entity.ConfigType;

import java.util.List;
import java.util.Optional;

public interface ConfigTypeService {
    List<ConfigType> getAllConfigTypes();
    List<ConfigType> getActiveConfigTypes();
    Optional<ConfigType> getConfigTypeById(Integer id);
    ConfigType createConfigType(ConfigType configType);
    ConfigType updateConfigType(Integer id, ConfigType configType);
    void deleteConfigType(Integer id);
    boolean existsByConfigTypeName(String configTypeName);
}

package com.cms.repository;

import com.cms.entity.CollectionComponent;
import com.cms.entity.CollectionListing;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CollectionComponentRepository extends JpaRepository<CollectionComponent, Integer> {
    List<CollectionComponent> findByCollectionAndIsActiveTrue(CollectionListing collection);

    List<CollectionComponent> findByCollectionId(Integer collectionId);

    List<CollectionComponent> findByCollectionIdOrderByDisplayPreference(Integer collectionId);

    @Query("SELECT MAX(cc.displayPreference) FROM CollectionComponent cc WHERE cc.collection.id = :collectionId")
    Integer findMaxDisplayPreferenceByCollectionId(@Param("collectionId") Integer collectionId);

    @Query("SELECT MAX(cc.id) FROM CollectionComponent cc")
    Integer findMaxId();
}

-- Create the tenants table
CREATE TABLE IF NOT EXISTS tenants (
    id INT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    schema_name VARCHAR(255) NOT NULL UNIQUE,
    is_active BOOLEAN DEFAULT true,
    description TEXT,
    created_by <PERSON><PERSON><PERSON><PERSON>(50),
    created_at TIMESTAMP,
    modified_by <PERSON><PERSON><PERSON><PERSON>(50),
    modified_at TIMESTAMP
);

-- Create sequence for Tenant entity
CREATE SEQUENCE IF NOT EXISTS cms_tenant_seq START WITH 100 INCREMENT BY 1;

-- Create indexes for tenants table
CREATE INDEX IF NOT EXISTS idx_tenants_schema_name ON tenants(schema_name);
CREATE INDEX IF NOT EXISTS idx_tenants_is_active ON tenants(is_active);

-- Insert default public tenant (only if not exists)
INSERT INTO tenants (id, name, schema_name, is_active, description)
SELECT 1, 'Public Tenant', 'public', true, 'Default public tenant'
WHERE NOT EXISTS (SELECT 1 FROM tenants WHERE id = 1);
